"""Create auto_qa_checks table

Revision ID: a1b2c3d4e5f8
Revises: 0cae082db56f
Create Date: 2025-06-02 09:34:42.000000

"""
from alembic import op
from sqlalchemy import text
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f8"
down_revision = "0cae082db56f"
branch_labels = None
depends_on = None


def upgrade():
    # Create the auto_qa_checks table
    op.create_table(
        "auto_qa_checks",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column("file_id", postgresql.UUID(as_uuid=True), nullable=True, index=True),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.Column("check_type", sa.String(), nullable=False),
        sa.Column("original_value", sa.String(), nullable=False),
        sa.Column("original_value_source", sa.String(), nullable=True),
        sa.Column("check_status", sa.String(), nullable=False),
        sa.Column("check_score", sa.Integer(), nullable=False),
        sa.Column("explanation", sa.String(), nullable=False),
        sa.Column("details", postgresql.JSONB(), nullable=True),
        sa.Column("suggested_value", sa.String(), nullable=True),
        sa.Column("suggested_value_reasoning", sa.String(), nullable=True),
        sa.Column("suggested_value_confidence", sa.Integer(), nullable=True),
    )
    
    # Create foreign key constraints
    op.create_foreign_key(
        "fk_auto_qa_checks_submission_id",
        "auto_qa_checks",
        "submissions",
        ["submission_id"],
        ["id"],
        ondelete="CASCADE"
    )
    
    op.create_foreign_key(
        "fk_auto_qa_checks_file_id",
        "auto_qa_checks",
        "files",
        ["file_id"],
        ["id"],
        ondelete="CASCADE"
    )
    
    op.create_foreign_key(
        "fk_auto_qa_checks_organization_id",
        "auto_qa_checks",
        "organization",
        ["organization_id"],
        ["id"],
        ondelete="CASCADE"
    )
    
    # Create indexes
    op.create_index("ix_auto_qa_checks_organization_check_type_check_status", "auto_qa_checks", ["organization_id", "check_type", "check_status"])
    op.create_index("ix_auto_qa_checks_check_type_check_status", "auto_qa_checks", ["check_type", "check_status"])



def downgrade():
    # Drop indexes first
    op.drop_index("ix_auto_qa_checks_organization_check_type_check_status", "auto_qa_checks")
    op.drop_index("ix_auto_qa_checks_check_type_check_status", "auto_qa_checks")
    
    # Drop foreign key constraints
    op.drop_constraint("fk_auto_qa_checks_organization_id", "auto_qa_checks", type_="foreignkey")
    op.drop_constraint("fk_auto_qa_checks_file_id", "auto_qa_checks", type_="foreignkey")
    op.drop_constraint("fk_auto_qa_checks_submission_id", "auto_qa_checks", type_="foreignkey")
    
    # Drop the table
    op.drop_table("auto_qa_checks") 
