"""Add new file type

Revision ID: 1bbae8ecdc35
Revises: 00e9ac0a3c0c
Create Date: 2025-06-03 10:21:55.453587+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '1bbae8ecdc35'
down_revision = '00e9ac0a3c0c'
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE filetype ADD VALUE IF NOT EXISTS 'BETTERVIEW_REPORT';""")


def downgrade():
    pass
