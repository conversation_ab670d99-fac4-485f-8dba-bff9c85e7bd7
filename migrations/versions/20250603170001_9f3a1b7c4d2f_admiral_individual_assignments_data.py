"""Fill Admiral Individual Assignments data

Revision ID: 9f3a1b7c4d2f
Revises: 9f3a1b7c4d2e
Create Date: 2025-06-03 17:00:01.000000+00.00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import csv
import os
from sqlalchemy.sql import table, column
import uuid

# revision identifiers, used by Alembic.
revision = "9f3a1b7c4d2f"
down_revision = "9f3a1b7c4d2e"
branch_labels = None
depends_on = None


admiral_individual_assignments_table = table(
    "admiral_individual_assignments",
    column("id", postgresql.UUID(as_uuid=True)),
    column("broker_email", sa.String),
    column("inbox", sa.String),
    column("uw_email", sa.String),
)

CSV_FILE_PATH = f"migrations/data/{revision}.csv"


def upgrade():
    op.execute("TRUNCATE TABLE admiral_individual_assignments RESTART IDENTITY CASCADE")

    data_dir = os.path.dirname(CSV_FILE_PATH)
    if not os.path.isdir(data_dir):
        raise Exception(f"Data directory not found at {data_dir}")
    if not os.path.exists(CSV_FILE_PATH):
        raise Exception(f"CSV file not found at {CSV_FILE_PATH}")

    assignments_to_insert = []
    seen = set()
    try:
        with open(CSV_FILE_PATH, mode="r", encoding="utf-8-sig") as csvfile:
            reader = csv.DictReader(csvfile, delimiter=";")
            for row_number, row in enumerate(reader):
                broker_email = row.get("broker_email", "").strip()
                inbox = row.get("inbox", "").strip()
                uw_email = row.get("uw_email", "").strip()
                if not broker_email or not inbox or not uw_email:
                    print(f"Skipping row {row_number + 2} due to missing required fields: {row}")
                    continue
                key = (broker_email, inbox)
                if key in seen:
                    print(f"Skipping duplicate row {row_number + 2}: {row}")
                    continue
                seen.add(key)
                assignments_to_insert.append(
                    {
                        "id": uuid.uuid4(),
                        "broker_email": broker_email,
                        "inbox": inbox,
                        "uw_email": uw_email,
                    }
                )
    except Exception as e:
        print(f"Error reading or processing CSV file at {CSV_FILE_PATH}: {e}")
        raise

    if assignments_to_insert:
        try:
            op.bulk_insert(admiral_individual_assignments_table, assignments_to_insert)
            print(
                f"Successfully inserted {len(assignments_to_insert)} rows into admiral_individual_assignments from"
                f" {CSV_FILE_PATH}."
            )
        except Exception as e:
            print(f"Error during bulk insert: {e}")
            raise
    else:
        print(f"No valid assignments found in CSV file {CSV_FILE_PATH} to insert.")


def downgrade():
    op.execute("TRUNCATE TABLE admiral_individual_assignments RESTART IDENTITY CASCADE")
