"""add index on integration_logs status

Revision ID: c9bdd439a0ad
Revises: 959b9b06a3a4
Create Date: 2025-05-29 18:57:19.834137+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'c9bdd439a0ad'
down_revision = '959b9b06a3a4'
branch_labels = None
depends_on = None


def upgrade():
    # Create an index on the status column of the integration_logs table
    op.create_index(
        'ix_integration_logs_status',
        'integration_logs',
        ['status'],
        unique=False
    )


def downgrade():
    pass
