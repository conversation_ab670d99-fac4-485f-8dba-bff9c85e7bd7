type: object
allOf:
  - $ref: "../../v3.yml#/components/schemas/ID"
  - $ref: "../../v3.yml#/components/schemas/CreatedAt"
properties:
  name:
    type: string
    description: the name of the File.
    example: "shipton.xlsx"
  submission_business_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    nullable: true
  submission_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    nullable: true
  s3_key:
    type: string
    description: s3 key of the File.
  presigned_url:
    type: string
    description: presigned url to download the file.
    nullable: true
  file_type:
    type: string
    description: file type of the file
    enum:
      - CAT Result
      - Loss Run
      - Loss Run Summary
      - Other
      - CAT Request Form
      - SOV
      - Named Insured Schedule
      - Safety Manual
      - Site Report
      - Drivers
      - Vehicles
      - Geotech Report
      - Supplemental Form
      - Internal SOV
      - Email
      - Export Email File
      - Vehicles - underlying policy
      - ACORD Form
      - Multiple Files
      - Unknown
      - Archive
      - Company Structure
      - Employee Handbook
      - Hiring Guidelines
      - Company's Bylaws
      - Resume
      - Budget
      - Financial Statement
      - Consolidated Financial Statement
      - Copilot Terms and Conditions
      - Workers Compensation Experience Modifier
      - Workers Compensation Payroll
      - Well Schedule
      - MVR
      - IFTA
      - Ally Auto Supplemental
      - Empty
      - CAB Report
      - Html Document
      - Cover Sheet
      - Correspondence Email
      - Directors & Officers
      - Directors & Officers Roster
      - ERISA Form 5500
      - ERISA Form 5500-SF
      - EEOC
      - Equipment
      - Broker of Record Letter
      - Quote
      - General Liability Quote
      - Policy
      - Endorsement
      - Project Schedule
      - Non-Disclosure Agreement
      - Letter of Intent
      - Investment Report / Presentation
      - Insurance Joinder
      - Purchase Agreement
      - Betterview Report
      - Custom
    nullable: true
  classification:
    type: string
    nullable: true
    description: classification document type for the file.
  user_id:
    type: integer
    nullable: true
  organization_id:
    type: integer
    nullable: true
  processing_state:
    type: string
    nullable: true
    enum:
      - "NOT_CLASSIFIED"
      - "CLASSIFICATION_FAILED"
      - "NOT_APPLICABLE_FOR_PROCESSING"
      - "CLASSIFIED"
      - "PROCESSING"
      - "PROCESSING_FAILED"
      - "CACHE_PROCESSED"
      - "PROCESSED"
      - "WAITING_FOR_DATA_ONBOARDING"
      - "DATA_ONBOARDED"
      - "WAITING_FOR_HUMAN_INPUT"
      - "WAITING_FOR_ENTITY_MAPPING"
      - "WAITING_FOR_BUSINESS_CONFIRMATION"
      - "AUTOCONFIRMING"
      - "WAITING_FOR_DATA_CONSOLIDATION"
      - "DATA_CONSOLIDATED"
      - "BUSINESS_DEDUPLICATION"
      - "REPLACED"
      - "COMPLETED"
      - "WAITING_FOR_COMPLETION"
      - "IGNORED"
      - "WAITING_FOR_PROCESSING"
      - "CLASSIFYING"
      - "WAITING_FOR_FACTS_AND_SUGGESTIONS_RESOLUTION"
      - "FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED"
      - "WAITING_FOR_FILE_INSIGHTS"
      - "FILE_INSIGHTS_FINISHED"
  sensible_status:
    type: string
    nullable: true
    description: Status returned from Sensible ingestion.
  origin:
    type: string
    nullable: true
    enum:
      - "API"
      - "EMAIL"
      - "COPILOT"
      - "COPY"
      - "SYNC"
  initial_classification_confidence:
    type: number
    format: float
    nullable: true
    description: confidence of file classification result
  user_file_type:
    type: string
    description: file type set by user overriding the one that was inferred and stored in file_type
    enum:
      - CAT Result
      - Loss Run
      - Loss Run Summary
      - Other
      - CAT Request Form
      - SOV
      - Named Insured Schedule
      - Safety Manual
      - Site Report
      - Drivers
      - Vehicles
      - Geotech Report
      - Supplemental Form
      - Internal SOV
      - Email
      - Export Email File
      - Vehicles - underlying policy
      - ACORD Form
      - Multiple Files
      - Unknown
      - Archive
      - Company Structure
      - Employee Handbook
      - Hiring Guidelines
      - Company's Bylaws
      - Resume
      - Budget
      - Financial Statement
      - Consolidated Financial Statement
      - Copilot Terms and Conditions
      - Workers Compensation Experience Modifier
      - Workers Compensation Payroll
      - Well Schedule
      - MVR
      - IFTA
      - Ally Auto Supplemental
      - Empty
      - CAB Report
      - Html Document
      - Cover Sheet
      - Correspondence Email
      - Directors & Officers
      - Directors & Officers Roster
      - ERISA Form 5500
      - ERISA Form 5500-SF
      - EEOC
      - Equipment
      - Broker of Record Letter
      - Quote
      - General Liability Quote
      - Policy
      - Endorsement
      - Project Schedule
      - Non-Disclosure Agreement
      - Letter of Intent
      - Investment Report / Presentation
      - Insurance Joinder
      - Purchase Agreement
      - Betterview Report
      - Custom
    nullable: true
  initial_classification:
    type: string
    description: initial classification document type of the file
    nullable: true
  parent_file_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    nullable: true
  additional_info:
    type: object
    description: additional information about file like low-level classification subtype
    nullable: true
  additional_info_json:
    type: string
    description: additional information about file like low-level classification subtype encoded as a JSON value
    nullable: true
  issues:
    type: array
    items:
      type: string
    description: list of issues with file itself or its processing/parsing
    nullable: true
  is_internal:
    type: boolean
    description: whether the file is internal
    nullable: true
  is_required_shadow_processing:
    type: boolean
    description: whether the file potentially requires shadow processing
    nullable: true
  replaced_by_file_ids:
    type: array
    items:
      type: string
      format: uuid
    description: the IDs of the Files replacing this file.
    nullable: true
  internal_notes:
    type: array
    items:
      type: string
    description: list of internal notes about the file
    nullable: true
  is_handsigned:
    type: boolean
    description: whether the file is a document that has been hand-signed
    nullable: false
  processed_file:
    $ref: "../../v3.yml#/components/schemas/ProcessedFile"
  client_file_type:
    type: string
    description: Client facing file type if enabled
    nullable: true
  client_file_tags:
    type: object
    additionalProperties: true
    nullable: true
  description:
    type: string
    nullable: true
  external_identifier:
    type: string
    nullable: true
  hidden:
    type: boolean
    description: whether the files comes from a hidden tab
    nullable: true
  display_name:
    type: string
    description: The display name of the file.
    nullable: true
  additional_file_names:
    type: array
    items:
      type: string
    description: list of file names that are duplicates of already existing file
    nullable: true
  user_shadow_state:
    type: string
    nullable: true
    enum:
      - "DELETED"
      - "REPLACED"
      - "CREATED"
  is_locked:
    type: boolean
    description: whether the file is locked
    nullable: true
  is_deleted:
    type: boolean
    description: whether the file is soft deleted
    nullable: true
  custom_file_type:
    $ref: "../../v3.yml#/components/schemas/CustomFileType"
  custom_file_type_id:
    type: string
    format: uuid
    description: id of the custom file type
    nullable: true
  custom_classification:
    type: string
    description: custom classification
    nullable: true
  content_type:
    type: string
    description: content type of the file
    nullable: true
