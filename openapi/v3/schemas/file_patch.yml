type: object
properties:
  processing_state:
    type: string
    nullable: true
    enum:
      - "NOT_C<PERSON><PERSON><PERSON>IED"
      - "CLASS<PERSON><PERSON>ATION_FAILED"
      - "NOT_APPLICABLE_FOR_PROCESSING"
      - "CLA<PERSON><PERSON>IED"
      - "PROCESSING"
      - "PROCESSING_FAILED"
      - "PROCESSED"
      - "WAITING_FOR_DATA_ONBOARDING"
      - "DATA_ONBOARDED"
      - "WAITING_FOR_HUMAN_INPUT"
      - "WAITING_FOR_ENTITY_MAPPING"
      - "WAITING_FOR_BUSINESS_CONFIRMATION"
      - "AUTOCONFIRMING"
      - "WAITING_FOR_DATA_CONSOLIDATION"
      - "REPLACED"
      - "COMPLETED"
      - "WAITING_FOR_COMPLETION"
      - "IGNORED"
      - "WAITING_FOR_PROCESSING"
      - "CLASSIFYING"
      - "WAITING_FOR_FACTS_AND_SUGGESTIONS_RESOLUTION"
      - "FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED"
      - "WAITING_FOR_FILE_INSIGHTS"
      - "FILE_INSIGHTS_FINISHED"
  file_type:
    type: string
    nullable: true
  classification:
    type: string
    nullable: true
  user_file_type:
    type: string
    description: file type set by user overriding the one that was inferred and stored in file_type
    enum:
      - CAT Result
      - Loss Run
      - Loss Run Summary
      - Other
      - CAT Request Form
      - SOV
      - Named Insured Schedule
      - Safety Manual
      - Site Report
      - Drivers
      - Vehicles
      - Geotech Report
      - Supplemental Form
      - Internal SOV
      - Email
      - Export Email File
      - Vehicles - underlying policy
      - ACORD Form
      - Multiple Files
      - Unknown
      - Archive
      - Company Structure
      - Employee Handbook
      - Hiring Guidelines
      - Company's Bylaws
      - Resume
      - Budget
      - Financial Statement
      - Consolidated Financial Statement
      - Copilot Terms and Conditions
      - Workers Compensation Experience Modifier
      - Workers Compensation Payroll
      - Well Schedule
      - MVR
      - IFTA
      - Ally Auto Supplemental
      - Empty
      - CAB Report
      - Html Document
      - Cover Sheet
      - Correspondence Email
      - Directors & Officers
      - Directors & Officers Roster
      - ERISA Form 5500
      - ERISA Form 5500-SF
      - EEOC
      - Equipment
      - Broker of Record Letter
      - Quote
      - Policy
      - Endorsement
      - General Liability Quote
      - Project Schedule
      - Non-Disclosure Agreement
      - Letter of Intent
      - Investment Report / Presentation
      - Insurance Joinder
      - Purchase Agreement
      - Betterview Report
      - Custom
    nullable: true
  initial_classification_confidence:
    type: number
    format: float
    nullable: true
    description: confidence of file classification result
  initial_classification:
    type: string
    description: initial classification document type of the file
    nullable: true
  comment:
    type: string
    nullable: true
  additional_info:
    type: object
    description: additional information about file like low-level classification subtype
    nullable: true
  issue:
    type: string
    description: information about an issue with file itself or its processing/parsing
    nullable: true
  issues:
    type: array
    items:
      type: string
    description: information about issues with file itself or its processing/parsing
    nullable: true
  is_internal:
    type: boolean
    description: whether the file is internal
    nullable: true
  is_required_shadow_processing:
    type: boolean
    description: whether the file potentially requires shadow processing
    nullable: true
  internal_note:
    type: string
    description: internal note to be append to internal notes of the file
    nullable: true
  internal_notes:
    type: array
    items:
      type: string
    description: internal notes to replace existing internal notes of the file
    nullable: true
  client_file_tags:
    type: object
    additionalProperties: true
    nullable: true
  client_file_type:
    type: string
    nullable: true
  external_identifier:
    type: string
    format: uuid
    pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
    nullable: true
  hidden:
    type: boolean
    description: whether the files comes from a hidden tab
    nullable: true
  is_locked:
    type: boolean
    description: whether the file is locked
    nullable: true
  custom_file_type_id:
    type: string
    format: uuid
    description: id of the custom file type
    nullable: true
  content_type:
    type: string
    description: content type of the file
    nullable: true
