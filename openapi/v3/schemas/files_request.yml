type: object
properties:
  file:
    type: string
    format: binary
  report_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
  submission_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
  submission_business_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
  comment:
    type: string
    nullable: true
    description: general comment about the file
    example: "Very urgent, please process ASAP"
  file_type:
    type: string
    description: file type of the file
    enum:
      - CAT Result
      - Loss Run
      - Loss Run Summary
      - Other
      - CAT Request Form
      - SOV
      - Named Insured Schedule
      - Safety Manual
      - Site Report
      - Drivers
      - Vehicles
      - Geotech Report
      - Supplemental Form
      - Internal SOV
      - Email
      - Vehicles - underlying policy
      - Export Email File
      - ACORD Form
      - Multiple Files
      - Unknown
      - Archive
      - Company Structure
      - Employee Handbook
      - Hiring Guidelines
      - Company's Bylaws
      - Resume
      - Budget
      - Financial Statement
      - Consolidated Financial Statement
      - Copilot Terms and Conditions
      - Workers Compensation Experience Modifier
      - Workers Compensation Payroll
      - Well Schedule
      - MVR
      - IFTA
      - Ally Auto Supplemental
      - Empty
      - CAB Report
      - Html Document
      - Cover Sheet
      - Correspondence Email
      - Directors & Officers
      - Directors & Officers Roster
      - ERISA Form 5500
      - ERISA Form 5500-SF
      - EEOC
      - Equipment
      - Broker of Record Letter
      - Quote
      - General Liability Quote
      - Policy
      - Endorsement
      - Project Schedule
      - Non-Disclosure Agreement
      - Letter of Intent
      - Investment Report / Presentation
      - Insurance Joinder
      - Purchase Agreement
      - Betterview Report
      - Custom
  classification:
    type: string
    description: file classification type
  origin:
    type: string
    nullable: true
    enum:
      - "API"
      - "EMAIL"
      - "COPILOT"
      - "SYNC"
      - "COPY"
  initial_classification_confidence:
    type: number
    format: float
    nullable: true
    description: confidence of file classification result
  initial_classification:
    type: string
    description: initial classification document type of the file
    nullable: true
  additional_info_json:
    type: string
    description: additional information about file like low-level classification subtype encoded as a JSON value
    nullable: true
  parent_file_id:
    type: string
    nullable: true
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
  reset_submission:
    type: boolean
    description: boolean on whether to reset the entities in the submission
    nullable: true
  replace_files:
    type: array
    nullable: true
    items:
      type: string
      format: uuid
      pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
  is_internal:
    type: boolean
    description: whether the file is internal
    nullable: true
  internal_notes:
    type: array
    items:
      type: string
    description: internal notes to replace existing internal notes of the file
    nullable: true
  client_file_type:
    type: string
    nullable: true
    description: File type for the client to use
  tags:
    type: string
    nullable: true
    description: File tags string encoded as a JSON array
  hidden:
    type: boolean
    description: whether the files comes from a hidden tab
    nullable: true
  custom_file_type_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    nullable: true
  custom_classification:
    type: string
    nullable: true
