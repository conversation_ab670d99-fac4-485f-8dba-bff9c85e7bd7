post:
  x-openapi-router-controller: copilot.v3.controllers.integrations
  operationId: bulk_get_integration_error_logs
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/IntegrationLogBulkRequest"
  responses:
    "200":
      description: List of integration logs matching the provided criteria.
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "../../v3.yml#/components/schemas/IntegrationLogBulkDetails"
    "400":
      description: the request is invalid
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred
