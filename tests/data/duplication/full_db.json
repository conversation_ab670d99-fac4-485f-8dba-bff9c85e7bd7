{"organization": [{"id": 1, "name": "test", "description": "test", "appetite": {"test": "test"}, "renewal_creation_interval": "0 years 0 mons 90 days 0 hours 0 mins 0.0 secs", "email_domain": "test", "identity_provider": "test", "shared_notification_address": "test", "for_analysis": true}], "users": [{"id": 1, "email": "test", "pass_hash": "test", "external_id": "test", "organization_id": 1, "role": "underwriter", "name": "test", "photo_s3_file_path": "test", "timezone": "test", "created_at": null, "updated_at": null, "is_read_only_account": false, "email_aliases": ["test"], "cross_organization_access": false, "redirect_user_id": null, "is_enabled": true, "last_opened_report": "test", "tac_file_id": "test", "tac_signed_on": "2024-02-10 01:54:03.499000 +00:00", "signup_data": {"test": "test"}, "registered_in_knock": true}], "audit_trails": [{"id": 1, "created_at": "2023-02-10 02:44:49.420495", "user_external_id": "test", "table_name": "test", "column_name": "test", "row_id": 1, "new_value": {"test": "test"}}], "brokerages_v2": [{"updated_at": null, "id": "1eaf8b40-4615-4b6d-bdaf-83abd2e619f9", "name": "test", "created_at": "2023-02-10 02:40:22.415545 +00:00", "organization_id": 1, "is_confirmed": true, "domains": ["test.com"], "aliases": ["test"]}], "brokerage_employees": [{"updated_at": null, "id": "11af8b40-4615-4b6d-bdaf-83abd2e619f9", "created_at": "2023-02-10 02:43:04.947338 +00:00", "brokerage_id": "1eaf8b40-4615-4b6d-bdaf-83abd2e619f9", "organization_id": 1, "name": "test test", "email": "<EMAIL>", "roles": ["CORRESPONDENCE_CONTACT"], "aliases": ["test"]}], "boss_uw_mappings": [{"id": "44398d38-1cd5-41f1-b9db-317d049dd725", "created_at": "2023-02-10 03:29:20.043810 +00:00", "updated_at": null, "extract_broker_name": "test", "broker_v2_id": "11af8b40-4615-4b6d-bdaf-83abd2e619f9", "extract_brokerage_name": "test", "state": "test", "naics_code": "test", "coverage_type": "test", "extract_uw_name": "test", "user_id": 1}], "paragon_wc_uw_mappings": [{"id": "44398d38-1cd5-41f1-b9db-317d049dd725", "created_at": "2023-02-10 03:29:20.043810 +00:00", "updated_at": null, "sheet_broker_name": "test", "sheet_broker_email": "test", "sheet_domain": "test", "sheet_brokerage_name": "test", "sheet_agency_family": "test", "sheet_uw_name": "test", "user_id": 1}], "client_application": [{"id": "689951b1-e16d-4bb9-b53b-115de907e4af", "created_at": "2023-02-11 00:23:04.866410 +00:00", "updated_at": null, "name": "test", "organization_id": 1, "client_id": "test", "client_secret": "test"}], "coverages": [{"id": "b003ccdd-1d30-4c39-bda7-74f262d12c4e", "created_at": "2023-02-11 00:50:40.899673 +00:00", "updated_at": null, "name": "test", "display_name": "test", "is_disabled": false, "organization_id": 1, "coverage_types": ["PRIMARY"], "groups": null, "logical_group": "LIABILITY"}], "email_templates": [{"id": "bf036e81-2034-471c-a907-6faad24fc8ca", "created_at": "2023-02-11 01:26:04.273249 +00:00", "updated_at": null, "external_template_id": "test", "external_version_id": "test", "name": "test", "subject": "test", "html_content": "test", "owner_id": 1, "is_shared_with_organization": true, "type": "OTHER", "to_address": "test", "cc_addresses": ["test"], "reply_to": "test"}], "report_email_correspondence": [{"id": "23af0391-a21c-4d8a-b9ea-5d9f97cabd9d", "created_at": "2023-02-11 01:31:18.215740 +00:00", "updated_at": null, "thread_id": "test", "active_email_template_id": "test", "email_cc": ["test"], "recipient_address": "test", "email_account": "test"}], "emails": [{"id": "191b59fd-7445-4641-ac26-5e97ca88306c", "created_at": "2023-02-11 01:37:03.603626 +00:00", "updated_at": null, "message_id": "test", "correspondence_id": "23af0391-a21c-4d8a-b9ea-5d9f97cabd9d", "email_account": "test", "email_subject": "test", "email_from": "test", "email_to": "test", "email_cc": ["test"], "email_attachments_count": 1, "attachments": {"test": "test"}, "email_body": "test", "email_sent_at": "2024-02-11 01:35:42.077000 +00:00", "email_tracking_id": "191b59fd-7445-4641-ac26-5e97ca88306c", "email_delivered": true, "send_email_error": "OTHER_ERROR", "type": "ROOT", "email_reply_to": "test", "normalized_subject": "test", "is_processed": true, "in_reply_to": "test", "email_references": "test", "was_sent": true, "email_sender": "test", "is_embedded": true, "is_deleted": false}], "email_status": [{"id": "e32f8c58-e626-4a9b-b48e-5afb86abcc37", "created_at": "2023-02-11 01:41:25.706479 +00:00", "updated_at": null, "email_id": "191b59fd-7445-4641-ac26-5e97ca88306c", "status": "test", "status_details": "test", "update_timestamp": "2024-02-02 01:41:13.000000"}], "report_bundle": [{"id": "********-5014-4d3c-9d0c-38b5db03c3b1", "created_at": "2023-02-11 01:45:31.949576 +00:00", "updated_at": null}], "lob": [{"id": "b10de463-1aba-4479-9dfc-c39daaf0d716", "created_at": "2023-02-11 03:08:54.814665 +00:00", "updated_at": null, "display_name": "test", "tabs": ["test"]}], "report_and_submission": [{"report": {"id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "created_at": "2023-02-11 01:41:25.706479 +00:00", "updated_at": null, "is_deleted": false, "deletion_reason": "test", "name": "test", "owner_id": 1, "additional_data": {"test": "test"}, "is_archived": false, "organization_permission_level": null, "email_body": "test", "email_subject": "test", "report_bundle_id": "********-5014-4d3c-9d0c-38b5db03c3b1", "email_message_id": "test", "email_references": "test", "correspondence_id": "23af0391-a21c-4d8a-b9ea-5d9f97cabd9d", "is_copy": false, "organization_id": 1, "routing_tags": ["test"], "tier": 1, "is_rush": true, "is_user_waiting_for_shadow": true, "is_email_classification_enabled": false}, "submission": {"id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "created_at": "2023-02-11 03:39:41.307335 +00:00", "updated_at": null, "name": "test", "owner_id": 1, "stage": "ON_MY_PLATE", "proposed_effective_date": "2024-02-11 03:27:23.000000", "acord_125": null, "stage_details": {"test": "test"}, "is_deleted": false, "description_of_operations": "test", "is_renewal": false, "declined_date": "2024-02-11 03:28:08.000000", "declined_user_id": 1, "clearing_assignee_id": 1, "reason_for_declining": "test", "due_date": "2024-02-11 03:29:50.000000", "parent_id": null, "renewal_creation_date": "2024-02-11 03:30:02.000000", "account_name": "test", "policy_expiration_date": "2024-02-11 03:30:09.000000", "lob_id": "b10de463-1aba-4479-9dfc-c39daaf0d716", "mode": "STANDARD", "coverage_type": "test", "is_naics_verified": true, "is_verification_required": false, "is_verified": false, "received_date": "2024-02-11 03:31:18.000000", "is_metrics_set_manually": false, "processing_state": "COMPLETED", "businesses_resolving_state": "CONFIRMED", "origin": "EMAIL", "lost_reasons": ["test"], "primary_naics_code": "NAICS_231234", "iso_gl_code": "ISO_GL_91523", "sic_code": "SIC_0116", "icc_code": "ICC_421", "copilot_2_mode_id": null, "notes": "test", "recommendation_v2_action": "NO_ACTION", "recommendation_v2_priority": 1, "send_decline_email": false, "active_email_template_id": "test", "decline_email_sent": false, "send_decline_email_error": "OTHER_ERROR", "decline_email_recipient_address": "test", "is_auto_processed": true, "decline_email_tracking_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "decline_email_delivered": false, "decline_email_cc": ["test"], "decline_custom_template": "test", "decline_attachments": ["name:content"], "generated_description_of_operations": "test", "account_id": "test", "is_copilot_2_mode_frozen": false, "email_description": "test", "email_project_description": "test", "recommendation_v2_score": 1, "is_waiting_for_auto_verify": false, "verified_at": "2024-02-11 03:35:34.000000", "light_cleared": true, "is_auto_verified": true, "auto_verified_at": "2024-02-11 03:35:48.000000", "is_manual_verified": true, "manual_verified_at": "2024-02-11 03:35:55.000000", "target_premium": 1, "expired_premium": 1, "stuck_reason": null, "is_for_audit": true, "audited_at": "2024-02-11 03:36:21.000000", "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "sales": 1, "audited_by": 1, "is_verified_shell": false, "manual_naics_assignment_required": false, "is_stuck_engineering": false, "pds_special_note": "test", "is_shadow_processed": false, "recommendation_v2_is_refer": true, "missing_data_status": "NO_DATA", "is_processing": false, "missing_documents": ["test"], "decline_custom_subject": "test", "sent_rule_email": "test", "brokerage_id": "1eaf8b40-4615-4b6d-bdaf-83abd2e619f9", "brokerage_contact_id": "11af8b40-4615-4b6d-bdaf-83abd2e619f9", "synced_at": "2024-02-11 03:38:31.000000", "quoted_date": "2024-02-11 03:35:55.000000", "bound_date": "2024-02-11 03:35:55.000000", "cleared_date": "2024-02-11 03:35:55.000000", "contractor_submission_type": "PROJECT", "project_insurance_type": "WRAP_UP", "incumbent_carrier": [{"name": "Carrier 1"}], "fni_state": "WA", "is_enhanced_shell": false, "fni_fein": "12-123123", "adjusted_tiv": 1, "tiv": 10, "primary_state": "WA", "client_stage_comment": "Comment", "client_clearing_status": "Clear", "clearing_status": "CLEARED", "clearing_sub_statuses": [{"status": "Conflict", "sub_status": "Something"}], "brokerage_office": "brokerage office", "sub_producer_name": "Sub Producer", "sub_producer_email": "<EMAIL>", "client_recommendations_score": "A", "is_pre_renewal": false, "is_renewal_shell": false, "policy_status": "Active", "lookalike_bind_rate": 0.2137, "is_stub": false}}, {"report": {"id": "5eccb523-79b8-4386-b8d0-06aac80c7a3b", "created_at": "2023-02-11 01:41:25.706479 +00:00", "updated_at": null, "is_deleted": false, "deletion_reason": "test", "name": "test", "owner_id": 1, "additional_data": {"test": "test"}, "is_archived": false, "organization_permission_level": null, "email_body": "test", "email_subject": "test", "report_bundle_id": "********-5014-4d3c-9d0c-38b5db03c3b1", "email_message_id": "test", "email_references": "test", "correspondence_id": "23af0391-a21c-4d8a-b9ea-5d9f97cabd9d", "is_copy": false, "organization_id": 1, "routing_tags": ["test"], "tier": 1, "is_rush": true, "is_user_waiting_for_shadow": true}, "submission": {"id": "17100b7d-6abf-480a-8ca9-6cff97f00b6a", "created_at": "2024-02-11 03:39:41.307335 +00:00", "updated_at": null, "name": "test", "owner_id": 1, "stage": "ON_MY_PLATE", "proposed_effective_date": "2026-02-11 03:27:23.000000", "acord_125": null, "stage_details": {"test": "test"}, "is_deleted": false, "description_of_operations": "test", "is_renewal": false, "declined_date": "2024-02-11 03:28:08.000000", "declined_user_id": 1, "clearing_assignee_id": 1, "reason_for_declining": "test", "due_date": "2024-02-11 03:29:50.000000", "parent_id": null, "renewal_creation_date": "2024-02-11 03:30:02.000000", "account_name": "test", "policy_expiration_date": "2024-02-11 03:30:09.000000", "lob_id": "b10de463-1aba-4479-9dfc-c39daaf0d716", "mode": "STANDARD", "coverage_type": "test", "is_naics_verified": true, "is_verification_required": false, "is_verified": false, "received_date": "2024-02-11 03:31:18.000000", "is_metrics_set_manually": false, "processing_state": "COMPLETED", "businesses_resolving_state": "CONFIRMED", "origin": "EMAIL", "lost_reasons": ["test"], "primary_naics_code": "NAICS_231234", "iso_gl_code": "ISO_GL_91523", "sic_code": "SIC_0116", "icc_code": "ICC_421", "copilot_2_mode_id": null, "notes": "test", "recommendation_v2_action": "NO_ACTION", "recommendation_v2_priority": 1, "send_decline_email": false, "active_email_template_id": "test", "decline_email_sent": false, "send_decline_email_error": "OTHER_ERROR", "decline_email_recipient_address": "test", "is_auto_processed": true, "decline_email_tracking_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "decline_email_delivered": false, "decline_email_cc": ["test"], "decline_custom_template": "test", "decline_attachments": ["name:content"], "generated_description_of_operations": "test", "account_id": "test", "is_copilot_2_mode_frozen": false, "email_description": "test", "email_project_description": "test", "recommendation_v2_score": 1, "is_waiting_for_auto_verify": false, "verified_at": "2024-02-11 03:35:34.000000", "light_cleared": true, "is_auto_verified": true, "auto_verified_at": "2024-02-11 03:35:48.000000", "is_manual_verified": true, "manual_verified_at": "2024-02-11 03:35:55.000000", "target_premium": 1, "expired_premium": 1, "stuck_reason": null, "is_for_audit": true, "audited_at": "2024-02-11 03:36:21.000000", "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3b", "sales": 1, "audited_by": 1, "is_verified_shell": false, "manual_naics_assignment_required": false, "is_stuck_engineering": false, "pds_special_note": "test", "is_shadow_processed": false, "recommendation_v2_is_refer": true, "missing_data_status": "NO_DATA", "is_processing": false, "missing_documents": ["test"], "decline_custom_subject": "test", "sent_rule_email": "test", "brokerage_id": "1eaf8b40-4615-4b6d-bdaf-83abd2e619f9", "brokerage_contact_id": "11af8b40-4615-4b6d-bdaf-83abd2e619f9", "synced_at": "2024-02-11 03:38:31.000000", "quoted_date": "2024-02-11 03:35:55.000000", "bound_date": "2024-02-11 03:35:55.000000", "cleared_date": "2024-02-11 03:35:55.000000", "contractor_submission_type": "PROJECT", "project_insurance_type": "WRAP_UP", "prevent_clearing_updates": true, "incumbent_carrier": [{"name": "Carrier 1"}], "fni_state": "WA", "is_enhanced_shell": false, "fni_fein": "12-123123", "adjusted_tiv": 1, "tiv": 10, "brokerage_office": "brokerage office", "sub_producer_name": "Sub Producer", "sub_producer_email": "<EMAIL>", "is_pre_renewal": false, "is_renewal_shell": false, "facts_config": {}, "policy_status": "Active", "lookalike_bind_rate": 0.2137, "is_stub": false}}], "execution_event": [{"id": "18ccbe74-c68a-4d46-89e5-cc1777472a4f", "created_at": "2023-02-11 02:58:42.566804 +00:00", "updated_at": null, "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "event_type": "SUCCEEDED", "occurred_at": "2024-02-11 02:58:42.566804 +00:00", "execution_id": "18ccbe74-c68a-4d46-89e5-cc1777472a4f"}], "feedback": [{"id": "69aba88b-e4bc-4e99-8e7f-6977615a9fdc", "created_at": "2023-02-11 03:03:49.146115 +00:00", "updated_at": null, "attribute_name": "test", "object_id": "test", "object_type": "test", "value": {"test": "test"}, "user_id": 1, "user_origin": "test", "is_trusted": true, "user_feedback": "test", "business_id": "69aba88b-e4bc-4e99-8e7f-6977615a9fdc"}], "ask_questions": [{"id": "3280ed6a-e0e0-41dc-a632-dc6e6b9a4d59", "created_at": "2023-02-11 05:28:07.082146 +00:00", "updated_at": null, "question": "test", "answer": "test", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a"}], "submission_businesses": [{"id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "created_at": "2023-02-11 05:40:06.225395 +00:00", "updated_at": null, "requested_name": "test", "requested_address": "test", "requested_phone_number": "test", "requested_industries": ["test"], "business_id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "is_user_confirmed": false, "aliases": ["test"], "selected_alias": "test", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "requested_legal_name": "test", "serial_id": 1, "ers_snapshot_id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "description_of_operations": "test", "entity_type": null, "entity_role": "PROJECT", "named_insured": "FIRST_NAMED_INSURED", "project_insurance_type": "WRAP_UP", "hide_property_facts": false, "hide": false, "additional_info": {"test": "test"}, "file_id_sources": ["fdbc95d8-47a2-4a29-886c-160d82b604c5"]}], "copilot_worker_execution_event": [{"id": "3e205645-a7f2-4136-a499-27eb60b111b0", "created_at": "2023-02-11 05:46:37.471323 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "submission_business_id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "first_party_field_original_name": "test", "old_parent_id": "3e205645-a7f2-4136-a499-27eb60b111b0", "execution_type": "LOAD_VINS", "event_type": "SUCCEEDED", "occurred_at": "2024-02-11 05:40:06.225395 +00:00", "execution_id": "3e205645-a7f2-4136-a499-27eb60b111b0"}], "files": [{"id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "created_at": "2023-02-12 00:32:36.289911 +00:00", "updated_at": null, "name": "test", "s3_key": "test", "submission_business_id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "file_type": "EMAIL", "comment": "test", "processing_state": "COMPLETED", "origin": "EMAIL", "user_id": 1, "organization_id": 1, "classification": "EMAIL", "sensible_status": "COMPLETE", "user_file_type": "EMAIL", "parent_file_id": null, "additional_info": {"test": "test"}, "size": 1, "initial_classification": "EMAIL", "initial_classification_confidence": 1, "issue": "test", "checksum": "098f6bcd4621d373cade4e832627b4f6", "is_internal": false, "initial_processing_state": "COMPLETED", "is_required_shadow_processing": false, "replaced_by_file_ids": ["fdbc95d8-47a2-4a29-886c-160d82b604c5"], "issues": ["test"], "internal_notes": ["test"], "execution_arn": "test", "retry_count": 1, "processing_state_updated_at": "2023-02-12 00:32:36.289911 +00:00", "client_file_tags": {"tags": ["tag1"]}, "client_file_type": "Other", "external_identifier": "fdbc95d8-47a2-4a29-886c-160d82b604c2", "additional_file_names": ["test.pdf"], "is_locked": false, "is_deleted": false, "custom_file_type_id": null, "custom_classification": null, "content_type": "application/pdf"}], "custom_file_type": [{"id": "fdbc95d8-47a2-4a29-886c-160d82b604c2", "file_type_name": "test", "organization_id": 1, "is_deleted": false}], "enhanced_files": [{"id": "a371d3cd-925d-4302-9fa1-ae89340df27c", "created_at": "2023-02-12 00:37:00.000000 +00:00", "updated_at": null, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "enhancement_type": "COLOR_HIGHLIGHT", "s3_key": "test"}], "file_metrics": [{"id": "7e379bfe-3e36-411e-8eda-cc45160b2090", "created_at": "2023-02-12 01:29:53.162076 +00:00", "updated_at": null, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "metric_name": "PARSED_ROWS_COUNT", "metric_value": 1, "score_calculation": {"column": "name", "value": "test"}}], "files_extracted_metadata": [{"id": "be4b3da4-a207-4904-8bbf-1b9ce6d21409", "created_at": "2023-02-12 01:33:01.780491 +00:00", "updated_at": null, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "s3_key": "test", "metadata_type": "AWS_TEXTRACT", "metadata_extract": {"test": "test"}, "status": "READY", "pending_info": {"test": "test"}, "retry_count": 1}], "files_label_studio": [{"id": "bba56701-c440-46a2-9ea9-74c0d57481e3", "created_at": "2023-02-12 01:35:22.258503 +00:00", "updated_at": null, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "total_pages": 1, "page_num": 1, "status": "COMPLETED", "image_s3_key": "test", "task_id": 1, "processed_data": {"test": "test"}}], "hub_templates": [{"id": "29b9e277-09d8-4de7-97d5-36433dc5ec29", "created_at": "2023-02-12 01:42:05.343060 +00:00", "updated_at": null, "user_id": 1, "name": "test", "is_default": false, "template": {"test": "test"}}], "ifta_data": [{"id": "946d77c9-108e-4992-95ce-45976c19cdaf", "created_at": "2023-02-12 01:45:44.543932 +00:00", "updated_at": null, "jurisdiction": "CA", "date": "2024-02-03 01:44:28.647000 +00:00", "total_miles": 1, "taxable_gallons": 1, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "matched_total": true}], "lob_carrier": [{"id": "6e21b909-1891-43ae-a587-200c324aa7c3", "created_at": "2023-02-12 01:53:52.424753 +00:00", "updated_at": null, "lob_raw": "test", "carrier": "test", "line_of_business": "OTHER"}], "loss_policy": [{"id": "1901d5e8-397a-4bd2-b51b-92661c0e49f7", "created_at": "2023-02-12 02:00:10.906806 +00:00", "updated_at": null, "number": "test", "effective_date": "2024-02-12", "expiration_date": "2024-02-19", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "organization_id": 1, "raw_line_of_business": "test", "line_of_business": "OTHER", "original_line_of_business": "test", "was_created_artificially": false}], "loss": [{"id": "439735bc-073f-437f-b7bf-0bcd4abd6077", "created_at": "2023-02-12 02:04:25.810975 +00:00", "updated_at": null, "coverage_id": "b003ccdd-1d30-4c39-bda7-74f262d12c4e", "submission_business_id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "claim_number": "test", "insured_name": "test", "claimant_name": "test", "loss_date": "2024-02-12", "report_date": "2024-02-12", "claim_date": "2024-02-12", "policy_effective_date": "2024-02-12", "exposure_close_date": "2024-02-12", "loss_address": "test", "claim_description": "test", "line_of_business": "OTHER", "loss_type_raw": "OTHER", "loss_type": "OTHER", "sum_of_loss_reserve": 1, "sum_of_alae_reserve": 1, "sum_of_net_paid_loss": 1, "sum_of_net_outstanding_loss": 1, "sum_of_net_paid_alae": 1, "sum_of_net_outstanding_alae": 1, "sum_of_total_net_incurred": 1, "claim_status": "test", "ligitation_status": "test", "original_line_of_business": "test", "net_of_deductible": 1, "policy_expiration_date": "2024-02-12", "recoveries": 1, "total_paid": 1, "organization_id": 1, "policy_id": "test", "carrier": "test", "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "coverage_type": "PRIMARY", "report_generated_date": "2024-02-12", "lob_raw": "test", "is_manual": false, "loss_policy_id": "1901d5e8-397a-4bd2-b51b-92661c0e49f7", "merged_parent_id": null, "is_duplicate": false, "partial_hash": 1, "full_hash": 1}], "sensible_extraction": [{"id": "aeddae2c-a315-443a-a531-e4b909cfce92", "created_at": "2023-02-12 02:38:18.720384 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "status": "RESPONSE_RECEIVED", "upload_error": "test", "extraction_id": "aeddae2c-a315-443a-a531-e4b909cfce92", "extraction_status": "COMPLETE", "number_of_pages": 1, "extraction_errors": [{"test": "test"}]}], "sensible_extraction_document": [{"id": "5b461f84-c5e6-489a-8a2b-c54ae5282950", "created_at": "2023-02-12 02:41:17.751529 +00:00", "extraction_id": "aeddae2c-a315-443a-a531-e4b909cfce92", "document_type": "test", "configuration": "test", "start_page": 1, "end_page": 2, "validations": {"test": "test"}, "errors": {"test": "test"}, "classification_summary": {"test": "test"}, "validation_summary": {"test": "test"}}], "loss_run_sensible_extraction_document": [{"id": "5b461f84-c5e6-489a-8a2b-c54ae5282950", "created_at": "2023-02-12 02:43:05.099051 +00:00", "extracted_claims": 1, "invalid_claims": 1, "not_loadable_claims": 1, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "duplicated_claims": 1, "same_file_duplicated_claims": 1}], "metric_group": [{"id": "00048892-380b-47eb-95aa-6407c6a84fb9", "created_at": "2023-02-12 03:12:49.725775 +00:00", "updated_at": null, "display_name": "test"}], "metric_preference": [{"id": "dcc4f9bf-2a76-4131-af0f-79c41cfdfc76", "created_at": "2023-02-12 03:15:24.764388 +00:00", "updated_at": null, "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "is_applicable": false, "is_enabled": true, "is_submitted_data": false, "parent_id": "dcc4f9bf-2a76-4131-af0f-79c41cfdfc76", "parent_type": "BUSINESS", "summary_config_id": "test", "display_name": "test", "metric_group_name": "test", "children_type": "test", "only_discovered_in": ["test"], "only_from_relationships": ["test"]}], "metric_v2": [{"id": "81720bbd-db0d-46d6-be85-da749824c814", "created_at": "2023-02-12 03:19:56.156748 +00:00", "updated_at": null, "report_v2_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "submission_business_id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "execution_id": "81720bbd-db0d-46d6-be85-da749824c814", "metric_type": "SUM", "name": "test", "parent_id": "81720bbd-db0d-46d6-be85-da749824c814", "parent_type": "BUSINESS", "children_type": "test", "summary_config_id": "test", "value_parent_ids": ["81720bbd-db0d-46d6-be85-da749824c814"], "value_business_ids": ["81720bbd-db0d-46d6-be85-da749824c814"], "value_parent_types": ["BUSINESS"], "float_values": [1.0], "datetime_values": ["2024-02-12 03:15:24.764388"], "string_values": ["test"], "minimums": [1.0], "maximums": [1.0], "distance_threshold": 1, "units": "test", "list_item_type": "test"}], "metric_source": [{"id": "836813cd-4937-4b2b-b14a-519bfe4ccebd", "created_at": "2023-02-12 03:25:43.648441 +00:00", "updated_at": null, "type": "test", "properties": {"test": "test"}, "metric_v2_id": "81720bbd-db0d-46d6-be85-da749824c814"}], "metric_template": [{"id": "a36d44c0-ab51-4604-86b2-3b618d524b7b", "created_at": "2023-02-12 03:27:41.637228 +00:00", "updated_at": null, "user_id": 1, "name": "test", "is_shared": false}], "metric_preferences_templates": [{"id": "b41206c2-306a-4bc3-8208-579cef3fc084", "created_at": "2023-02-12 03:29:45.477362 +00:00", "updated_at": null, "template_id": "a36d44c0-ab51-4604-86b2-3b618d524b7b", "metric_preferences_id": "dcc4f9bf-2a76-4131-af0f-79c41cfdfc76"}], "naics_code": [{"id": 1, "title": "test", "description": "test"}], "notebook_thread": [{"id": "05724223-ee98-4270-9e3c-4fac1de41a46", "created_at": "2023-02-12 03:34:28.432222 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "relates_to": "SUBMISSION", "submission_business_id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "is_deleted": false, "dossier_component_paths": ["test"], "copilot_component_paths": ["test"]}], "notebook_message": [{"id": "4f61da82-735f-4ffe-a201-d10760082c65", "created_at": "2023-02-12 03:36:39.299434 +00:00", "updated_at": null, "body": "test", "author_id": 1, "thread_id": "05724223-ee98-4270-9e3c-4fac1de41a46", "is_deleted": false}], "org_metric_config": [{"id": "7f8bd710-5a43-4422-bc80-44046fbe48ac", "created_at": "2023-02-12 03:38:22.284220 +00:00", "updated_at": null, "metric_name": "test", "organization_id": 1, "weight": 1}], "paragon_company_lines": [{"id": "da87556c-6ae2-4a96-ab77-10c2387a849f", "created_at": "2023-02-12 03:41:04.589122 +00:00", "updated_at": null, "company_line_guid": "da87556c-6ae2-4a96-ab77-10c2387a849f", "company_location_guid": "da87556c-6ae2-4a96-ab77-10c2387a849f", "line_guid": "da87556c-6ae2-4a96-ab77-10c2387a849f", "office_guid": "da87556c-6ae2-4a96-ab77-10c2387a849f", "state_id": "test", "location_name": "test", "line_name": "test", "company_license": "test", "quoting_and_issuing_office_name": "test", "office_default_cost_center_id": 1, "office_default_cost_center_name": "test", "company_default_cost_center_id": 1, "company_default_cost_center_name": "test", "billing_type_id": 1, "billing_type_name": "test"}], "paragon_underwriters": [{"id": "be8139ed-c9c0-4f9e-8603-e119262fe254", "created_at": "2023-02-12 03:45:54.737813 +00:00", "updated_at": null, "username": "test", "email": "test", "kalepa_user_id": 1, "ims_underwriter_id": 1, "ims_underwriter_guid": "be8139ed-c9c0-4f9e-8603-e119262fe254", "first_name": "test", "last_name": "test", "title": "test", "office": "test", "phone": "test", "fax": "test", "mobile": "test"}], "policy": [{"id": "a64a74e5-f45d-4f11-9965-ed1a30974c04", "created_at": "2023-02-12 03:54:45.661725 +00:00", "updated_at": null, "external_id": "test", "organization_id": 1, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a"}], "processed_files": [{"id": "ade154b7-a7ab-4476-8400-03d168e2b61e", "created_at": "2023-02-12 03:56:17.618844 +00:00", "updated_at": null, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "raw_processed_data": {"test": "test"}, "processed_data": {"test": "test"}, "onboarded_data": {"test": "test"}, "entity_mapped_data": {"test": "test"}, "business_resolution_data": {"test": "test"}}], "quality_audit_questions": [{"id": "251bd9b7-0c5d-4f1a-8fb9-fa4824965e24", "created_at": "2023-02-12 03:58:30.124849 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "question": "test", "answer": "test"}], "read_submission": [{"submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "user_id": 1, "created_at": "2023-02-12 04:01:33.087704 +00:00"}], "report_alerts": [{"id": "50dd7b8b-09f0-4192-9949-29ee29807f46", "created_at": "2023-02-12 04:07:30.489750 +00:00", "updated_at": null, "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "alert_type": "MISSING_NAICS"}], "report_link": [{"report_1_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "report_2_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3b"}], "report_processing_dependency": [{"id": "9ce8d802-7f7e-4081-9b48-7478d72f67e8", "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "dependent_report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3b", "dependency_type": "SAME_ORG"}], "report_shadow_dependency": [{"id": "3f78698c-df9f-4e80-bbcf-bedc8db060ee", "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "shadow_report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3b", "is_active": false}], "summary_preference": [{"id": "a3934247-0c7e-4e54-b1e0-93fd55811880", "created_at": "2023-02-12 05:03:11.079127 +00:00", "updated_at": null, "display_name": "test", "group_display_name": "test", "is_default": false, "icon_name": "test", "metric_type": "test"}], "report_summary_preference": [{"id": "cd5a4f5e-6953-497e-8d3f-5afb9ad09513", "created_at": "2023-02-12 05:04:47.194769 +00:00", "updated_at": null, "summary_preference_id": "a3934247-0c7e-4e54-b1e0-93fd55811880", "report_v2_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a"}], "report_triage": [{"id": "6b669a0d-d5a5-420b-92b2-1160bfd60133", "created_at": "2023-02-12 05:06:33.784881 +00:00", "updated_at": null, "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "result": "test", "email_sent": false, "explanations": ["exp 1", "exp 2"]}], "routing_rule": [{"id": "e0d3ae47-5ad0-40c4-be92-fe7caea45514", "created_at": "2023-02-12 05:08:18.317000 +00:00", "updated_at": null, "tag": "test", "order": 1, "contained_words": ["test"], "not_contained_words": ["test"], "contained_phrases": {"test": "test"}, "skip_if_previous_rule_was_hit": true, "tier": 1, "organization_id": 1, "user_id": 1, "is_rush": false, "is_case_sensitive": false, "words_threshold": 1, "only_email_body": false}], "scheduled_emails": [{"created_at": "2023-02-12 05:12:15.871048 +00:00", "updated_at": null, "id": "f6008f3b-d8a2-4973-b252-2bfb0fc5be8e", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "rule_name": "test", "template_id": "bf036e81-2034-471c-a907-6faad24fc8ca", "user_id": 1}], "sensible_calls": [{"id": "465194e4-0dc0-4836-8c23-0e8d85cbb4b8", "created_at": "2023-02-12 05:18:46.605747 +00:00", "updated_at": null, "organization_id": 1, "year": 1, "month": 1, "day": 1, "calls_made": 1}], "sensible_document_response_cache": [{"id": "99ed7c23-926a-47c9-b344-3314f2e1ba9a", "created_at": "2023-02-12 05:20:54.944983 +00:00", "updated_at": null, "response": {"test": "test"}, "file_ids": ["fdbc95d8-47a2-4a29-886c-160d82b604c5"], "configurations": ["test"]}], "sensible_quota": [{"id": "19ed7c23-926a-47c9-b344-3314f2e1ba9a", "organization_id": 1, "quota": 1}], "settings": [{"id": "5f3cb34e-b0c0-4480-b9eb-47beed8311eb", "created_at": "2023-02-12 05:28:03.795183 +00:00", "updated_at": null, "organization_id": 1, "is_map_enabled_by_default": false, "default_coverages": ["b003ccdd-1d30-4c39-bda7-74f262d12c4e"], "user_id": 1, "is_clarion_door_enabled": null, "can_resolve_clearing_issues": false, "is_clearing_enabled": false, "should_be_notified_for_clearing_issues": false, "support_email": "test", "allowed_submission_lobs": ["b10de463-1aba-4479-9dfc-c39daaf0d716"], "email_domains": ["test"], "whitelisted_emails": ["test"], "allow_multiple_assigned_underwriters_per_submission": false, "allow_multiple_client_submissions_per_submission": false, "allow_multiple_policies_per_submission": false, "show_hazard_hub_source": false, "is_support": false, "loss_runs_enabled": true, "total_credits": 1, "allow_bundled_submissions": false, "distribution_channel_employed": "AGENCY_AGENT", "is_operations": false, "clear_oldest_by_default": false, "show_management_dashboard": false, "show_underwriter_dashboard": false, "show_recommendations_rating": false, "show_account_id": false, "reply_emails_enabled": false, "is_light_clearing_enabled": false, "default_tier": 1, "max_tier_for_auto_processing": 1, "is_cs_manager": false, "show_internal_account_id": false, "show_coverage_filter": false, "is_tac_acceptance_required": false, "process_submission_by_default": false, "loss_runs_manual": false, "allowed_submission_stages": ["ON_MY_PLATE"], "default_regular_email_template_id": "bf036e81-2034-471c-a907-6faad24fc8ca", "default_decline_email_template_id": "bf036e81-2034-471c-a907-6faad24fc8ca", "classify_files_for_shells": false, "enhanced_shells_enabled": false, "transitions": {"ON_MY_PLATE": {"blacklist": []}}, "use_client_file_types": false, "client_file_types_config": "{}", "allow_client_submission_stage": false, "submission_tab_stages": {}, "tier_for_night_shift_processing": 1, "max_submissions_for_night_shift_processing": 1, "org_groups": ["test"], "user_notifications_enabled_after": "2023-02-12 05:30:30.174104 +00:00", "client_clearing_status_config": "{}", "client_id_required_for_clearing": false, "submission_number_label": "", "allow_edit_quote_number": false, "allow_edit_policy_number": false, "highly_accurate_preferred": false, "md_default_to_client_id_subs": false, "clearing_fields": [], "include_oni_in_news": false, "di_disabled_file_types": [], "use_sic_codes": false, "is_email_classification_enabled": false, "is_document_ingestion_enabled": false, "show_inbox": false, "can_adjust_email_classification": false, "strict_clearance_controls": false}], "stuck_details": [{"id": "b058fec1-806d-48ec-a9e5-abc2a357ccf5", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "created_at": "2023-02-12 05:30:30.174104 +00:00", "updated_at": null, "processing_state": "ENTITY_MAPPING", "stuck_at": "2024-02-12 05:30:07.015000 +00:00", "unstuck_at": "2024-02-12 05:30:10.278000 +00:00", "stuck_by": 1, "stuck_reason_explanation": "test", "was_correct_to_stuck": true, "stuck_reason_buckets": ["test"]}], "stuck_submission_feedback": [{"id": "00b3e4dc-4b56-4289-ab7a-5b8493214645", "created_at": "2023-02-12 05:33:12.836421 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "user_id": 1, "resolution": "test", "issue": "test", "stuck_issue": "test"}], "submission_bookmarks": [{"id": "88a4fffa-3641-436f-a0f0-413cfcc9b77a", "created_at": "2023-02-12 05:35:48.819482 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "user_id": 1}], "submission_clearing_issue": [{"id": "77148061-5cd6-4373-a81d-f9d07dc70b3d", "created_at": "2023-02-12 05:37:42.134041 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "suspected_report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3b", "is_resolved": true, "is_light": false, "reason": "test"}], "submission_coverages": [{"id": "f473c3fe-f5c8-42d6-8602-b17eca5a42de", "created_at": "2023-02-12 05:41:36.172594 +00:00", "updated_at": null, "coverage_id": "b003ccdd-1d30-4c39-bda7-74f262d12c4e", "estimated_premium": 1, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "is_quoted": true, "quoted_premium": 1, "limit": 1, "bound_premium": 1, "coverage_type": "EXCESS", "attachment_point": 1, "source": "MANUAL", "source_details": "test", "total_premium": 1, "self_insurance_retention": 1, "period": "period-test", "other_terms": "other_terms-test", "additional_data": {"test": "test"}, "stage": "ON_MY_PLATE"}], "underlying_policy": [{"id": "f473c3fe-f5c8-42d6-8602-b17eca5a42de", "created_at": "2023-02-12 05:41:36.172594 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "details": {"test": "test"}}], "submission_deductibles": [{"id": "789dac5c-4792-428c-a845-95b543134c4d", "created_at": "2023-02-12 05:46:47.600104 +00:00", "updated_at": null, "coverage_id": "b003ccdd-1d30-4c39-bda7-74f262d12c4e", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "policy_limit": 1, "policy_level": 1, "policy_level_type": "NUMERIC", "minimum": 1, "comment": "test", "coverage_type": "PRIMARY"}], "submission_files_data": [{"id": "f2813cbd-b1c4-4dd8-8719-eace6ac75142", "created_at": "2023-02-12 05:49:20.070923 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "entity_mapped_data": {"test": "test"}, "onboarded_data": {"test": "test"}, "loaded_data": {"test": "test"}, "raw_em_data": {"test": "test"}, "raw_do_data": {"test": "test"}}], "submission_note": [{"id": "998ff660-f055-4285-bca2-f00a328f1388", "created_at": "2023-02-12 05:51:39.787739 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "author_id": 1, "last_edit_by_id": 1, "is_editable": false, "text": "test", "referred_to_user_ids": [1], "referrals_closed_to_user_ids": [1], "html_content": "<p>test</p>"}], "submission_priority": [{"id": "7480d231-c22f-4f0f-8cdc-cba813d6556d", "created_at": "2023-02-12 05:55:23.411283 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "escalated_at": "2024-02-12 05:55:13.021000 +00:00", "removed_from_auto_assign": true, "overwritten_priority": 1}], "submission_sync": [{"id": "70a37754-eb5a-2e7d-b8cd-887aaf11cda7", "created_at": "2023-02-12 06:00:51.853170 +00:00", "policy_number": "test", "submission_name": "test", "underwriter_email": "test", "stage": "ON_MY_PLATE", "received_date": "2024-02-12 06:00:07.000000", "effective_date": "2024-02-12 06:00:08.000000", "coverage_name": "test", "coverage_type": "PRIMARY", "quoted_premium": 1, "bound_premium": 1, "organization_id": 1, "applied_changes": [], "pending_changes": [], "applied": true, "attempt": 1, "declined_date": "2024-02-12 06:00:32.000000", "source": "test", "account_id": "test", "is_renewal": true, "brokerage": "test", "broker": "test", "coverages": [], "direct_match_only": true, "created_date": "2024-02-12 06:00:32.000000", "quoted_date": "2024-02-12 06:00:32.000000", "bound_date": "2024-02-12 06:00:32.000000", "cleared_date": "2024-02-11 03:35:55.000000", "contractor_submission_type": "PROJECT", "project_insurance_type": "WRAP_UP", "client_id_being_renewed": "test", "broker_email": "<EMAIL>", "premium": 123.0, "expired_premium": 12345.5, "primary_state": "CA", "client_stage_id": 1, "user_groups_to_match": ["Kalepa Power User"], "policy_number_being_renewed": "test", "policy_status": "Active", "policy_expiration_date": "2025-02-12 06:00:32.000000", "first_seen": "2025-01-12 06:00:32.000000", "replaces_client_id": "123456", "replaced_by_client_id": "654321", "is_deleted": false, "deleted_at": "2025-01-12 06:00:32.000000", "parent_client_id": "parent123"}], "submission_sync_report": [{"id": "fcbd7f56-ccf8-4609-9ec3-e9c3d4e204e8", "created_at": "2024-09-26 08:02:57.096747 +00:00", "updated_at": null, "org_group": "test", "submission_sync_id": "70a37754-eb5a-2e7d-b8cd-887aaf11cda7"}], "submission_sync_identifiers": [{"id": "fcbd7f56-ccf8-4609-9ec3-e9c3d4e204e8", "created_at": "2024-09-26 08:02:57.096747 +00:00", "updated_at": null, "quote_numbers": ["QUOTE1234", "QUOTE1235"], "policy_numbers": ["POLICY1234", "POLICY1235"], "submission_sync_id": "70a37754-eb5a-2e7d-b8cd-887aaf11cda7"}], "submission_sync_matcher_data": [{"id": "a9f97bdb-0e6e-47c8-8f8f-a397702219e0", "created_at": "2024-02-22 01:57:27.426491 +00:00", "updated_at": null, "named_insured": "Potato company Inc.", "named_insured_address": "Random address", "submission_sync_id": "70a37754-eb5a-2e7d-b8cd-887aaf11cda7", "additional_data": {"test": "test"}}], "submissions_client_ids": [{"id": "7260fe11-c58c-4c10-85c7-a65e2ddfb0c2", "created_at": "2023-02-12 06:02:09.610631 +00:00", "updated_at": null, "client_submission_id": "test", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "source": "API"}], "submissions_users": [{"id": "946516ba-61ac-465f-bc77-5ea1eb2a2847", "created_at": "2023-02-12 06:04:31.111105 +00:00", "updated_at": null, "user_id": 1, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "source": "AUTO"}], "support_users": [{"id": "93ce9f94-66bc-4204-971b-fb507d38a03c", "created_at": "2023-02-12 06:11:03.279327 +00:00", "updated_at": null, "user_id": 1, "last_action_at": "2024-02-12 06:10:41.607000 +00:00", "current_report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "hourly_rate": 10, "can_load_construction": true, "can_load_submissions": false, "can_assign_naics": false, "is_tier_2": false, "is_highly_accurate": false}], "support_user_report": [{"id": "21ce9f94-66bc-4204-971b-fb507d38a03c", "created_at": "2023-02-12 06:12:53.757850 +00:00", "support_user_id": "93ce9f94-66bc-4204-971b-fb507d38a03c", "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "report_assigned_by_id": 1}], "support_user_files": [{"id": "f2e6be4d-f0f0-411c-8a32-04308206ff23", "support_user_id": "93ce9f94-66bc-4204-971b-fb507d38a03c", "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "file_assigned_by_id": 1}], "tenant_feedback": [{"id": "2d0fbb40-4da4-4315-8bfe-336ccb4478b9", "created_at": "2023-02-12 06:16:50.936363 +00:00", "updated_at": null, "business_name": "test", "formatted_address": "test", "premise_id": "2d0fbb40-4da4-4315-8bfe-336ccb4478b9", "business_id": "2d0fbb40-4da4-4315-8bfe-336ccb4478b9", "open_date": "2024-02-12 06:16:37.000000", "closed_date": "2024-02-12 06:16:38.000000", "owner_name": "test", "user_id": 1, "is_trusted": false, "is_not_at_premises": false}], "update_businesses_log": [{"id": "8f0fb5ac-5c06-4ab5-8de5-fbe20426da3e", "created_at": "2023-02-12 06:18:37.028606 +00:00", "new_id": "8f0fb5ac-5c06-4ab5-8de5-fbe20426da3e", "old_ids": ["8f0fb5ac-5c06-4ab5-8de5-fbe20426da3e"], "submission_ids": ["8f0fb5ac-5c06-4ab5-8de5-fbe20426da3e"], "affected_tables": {"test": "test"}}], "user_action": [{"id": "1e3688cd-bd0c-4a46-bece-474b35023af5", "created_at": "2023-02-12 06:21:37.034033 +00:00", "updated_at": null, "user_id": 1, "action_type": "business_search", "context": {"test": "test"}, "action": {"test": "test"}}], "user_group": [{"id": "0a04a222-9367-4b26-b2da-6d7f6c914e53", "created_at": "2023-02-12 06:23:25.975287 +00:00", "updated_at": null, "name": "test", "organization_id": 1}], "verification_checks": [{"id": "23988353-7ef0-480a-a75a-aed85c12321b", "created_at": "2023-02-12 06:28:02.517049 +00:00", "updated_at": null, "name": "test", "status": "ERROR", "error_message": "test", "manual": false, "force_manual": false, "force_verify": false, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "run_id": "23988353-7ef0-480a-a75a-aed85c12321b", "is_hard_check": true, "stage": "COMPLETED"}], "workers_comp_experience": [{"id": "8789587e-4aba-41b3-adc6-127209ecdf16", "created_at": "2023-02-12 08:46:30.112052 +00:00", "updated_at": null, "risk_name": "test", "risk_id": "test", "risk_address": "test", "risk_city": "test", "rating_effective_date": "2024-02-12 08:44:36.508000 +00:00", "production_date": "2024-02-12 08:44:38.697000 +00:00", "state": "CA", "actual_primary_losses": 1, "actual_stabilizing_value": 1, "actual_ratable_excess": 1, "actual_totals": 1, "actual_losses": 1, "expected_primary_losses": 1, "expected_excess_losses": 1, "expected_losses": 1, "expected_stabilizing_value": 1, "expected_ratable_excess": 1, "expected_totals": 1, "experience_modification": 1, "combinable_group_id": "test", "issue_date": "2024-02-12 08:45:09.731000 +00:00", "status": "test", "calculated_experience_modification": 1, "capped_debit_modification": 1, "transitional_modification_factor": 1, "number_of_claims": 1, "type": "test", "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "state_list": ["CA"], "combination_code": "test", "rating_effective_date_to": "2024-02-12 08:46:21.706000 +00:00", "experience_period_date_from": "2024-02-12 08:46:22.246000 +00:00", "experience_period_date_to": "2024-02-12 08:46:25.009000 +00:00", "modified_primary_losses": 1, "modified_excess_losses": 1, "modified_losses": 1, "source": "FILE"}], "workers_comp_state_rating_info": [{"id": "6339599d-f248-4666-b97c-1730af0f1bf1", "created_at": "2023-02-12 08:50:16.156705 +00:00", "updated_at": null, "state": "CA", "location_number": 1, "class_code": "test", "description_code": "test", "category": "test", "full_time_employees": 1, "part_time_employees": 1, "sic": "test", "naics": "test", "payroll": 1, "rate": 1, "est_annual_manual_premium": 1, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "submission_business_id": "41981066-0d4d-4393-96b0-ebf7aad23d68", "number_of_employees": 2}], "submission_brokerage_employees": [{"id": "11af8b40-4615-4b6d-bdaf-83abd2e619f9", "brokerage_employee_id": "11af8b40-4615-4b6d-bdaf-83abd2e619f9", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "role": "CORRESPONDENCE_CONTACT", "source": "EMAIL"}], "submission_brokerage": [{"id": "1eaf8b40-4615-4b6d-bdaf-83abd2e619f9", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "brokerage_id": "1eaf8b40-4615-4b6d-bdaf-83abd2e619f9", "source": "EMAIL"}], "submission_relations": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "from_submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "to_submission_id": "17100b7d-6abf-480a-8ca9-6cff97f00b6a", "type": "BUNDLE", "reason": "A reason", "confidence": 0.5, "is_resolved": true, "is_light": false, "is_active": false, "source": "AUTO"}], "submission_identifiers": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "identifier": "123", "identifier_type": "quote_number"}], "submission_level_extracted_data": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "field": "BROKER_NAME", "value": "'<PERSON>'", "source_details": "FILE_CONTENT", "generation_method": "GPT", "is_selected": true, "selected_by_user": "<EMAIL>", "is_valid": true, "validation_details": "test"}], "submission_processing": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3a", "dependency_parent_report_id": "5eccb523-79b8-4386-b8d0-06aac80c7a3b", "processing_state": "ENTITY_MAPPING", "verification_sla": "2024-04-11 03:29:14.153348 +00:00", "verified_at": "2024-04-11 03:29:14.153348 +00:00", "manual_verified_at": "2024-04-11 03:29:14.153348 +00:00", "auto_verified_at": "2024-04-11 03:29:14.153348 +00:00", "stuck_reason": "test"}], "shareholders": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "shareholder_type": "PERSON", "shareholder_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "shareholder_name": "<PERSON>", "ownership_percentage": 50.0, "is_director_or_board_member": true, "submission_business_id": "41981066-0d4d-4393-96b0-ebf7aad23d68"}], "task_definition": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "code": "test", "name": "test", "llm_task_description": "test", "group": "test", "timeout": 300}], "task_model": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "name": "test", "type": "LLM_PROMPT", "execution_config": {"test": "test"}, "execution_type": "LAMBDA", "processing_type": "EXTRACTION", "llm_model": "test", "use_task_output_processor": true, "timeout": 300}], "task_definition_model": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "task_definition_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "task_model_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "validation_task_model_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "order": 1, "is_always_run": true, "is_aware_of_previous_runs": true, "is_refinement_run": true, "is_preprocessing_input_run": true, "is_consolidation_run": true, "is_validation_run": true, "can_self_reflect": true, "is_benchmark_run": true, "is_disabled": true}], "task": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "task_definition_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "organization_id": 1, "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "business_id": "fdbc95d8-47a2-4a29-886c-160d82b604c6", "status": "PENDING", "context": {"test": "test"}, "input": {"test": "test"}, "output": {"test": "test"}, "processed_output": {"test": "test"}, "metrics": {"test": "test"}, "processing_cost": 0.001, "processing_time": 12, "confidence": 100, "is_valid_output": true, "is_valid_input": true}], "task_execution": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "task_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "task_model_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "input": {"test": "test"}, "output": {"test": "test"}, "used_input_tokens": 1, "used_output_tokens": 1, "processing_cost": 0.001, "processing_time": 123, "confidence": 100, "output_evaluation": {"test": "test"}, "processed_output": {"test": "test"}, "is_validation_run": true, "is_self_reflection_run": true, "is_consolidation_run": true, "is_benchmark_run": true}], "email_classification_labels": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "label": "test", "should_process": false, "organization_id": 1}], "email_classifiers": [{"id": "c176078c-a1fe-4ad7-b1f3-192775df5ed9", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "email_label_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "order": 0, "skip_if_previous_rule_was_hit": false, "type": "ATTACHMENTS_BASED", "organization_id": 1}], "email_classifications": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "email_id": "191b59fd-7445-4641-ac26-5e97ca88306c", "classifier_id": "c176078c-a1fe-4ad7-b1f3-192775df5ed9", "label_id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "explanation": "test", "snippet": "test", "additional_data": {}}], "broker_client_codes": [{"id": "9399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "agent_code": "test", "agency_code": "test", "agent_email": "test", "portal_email": "test", "organization_id": 1}], "sync_matching_runs": [{"id": "f4cf7c34-964f-4e7e-a8cc-daed4b2207ba", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "producer_config": {}, "organization_id": 1, "status": "NOT_STARTED", "started_at": "2024-04-11 03:29:14.153348", "completed_at": "2024-04-11 03:29:14.153348", "automatically_triggered": false}], "sync_matching_results": [{"id": "91aa2a0e-f4ce-4243-b51d-2fb02c869211", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "sync_matching_run_id": "f4cf7c34-964f-4e7e-a8cc-daed4b2207ba", "submission_sync_id": "70a37754-eb5a-2e7d-b8cd-887aaf11cda7", "matching_results": {}, "is_processed": false}], "submission_identifiers_suggestions": [{"id": "91aa2a0e-f4ce-4243-b51d-2fb02c869211", "created_at": "2024-04-11 03:10:22.665031 +00:00", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "submission_sync_id": "70a37754-eb5a-2e7d-b8cd-887aaf11cda7", "confidence": 1.0}], "brokerage_client_codes": [{"id": "3399611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "brokerage_id": "1eaf8b40-4615-4b6d-bdaf-83abd2e619f9", "agency_name": "test", "agent_name": "test", "agent_code": "test", "agency_code": "test", "organization_id": 1}], "submission_field_source": [{"id": "f4cf7c34-964f-4e7e-a8cc-daed4b2207bb", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "field_name": "sic_code", "source": "MANUAL"}], "submission_premises": [{"id": "9499611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-04-11 03:10:22.665031 +00:00", "updated_at": "2024-04-11 03:29:14.153348 +00:00", "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "named_insured": "test", "address": "test", "premises_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "submission_premises_type": "FNI", "organization_id": 1, "additional_data": {"test": "test"}}], "auto_qa_checks": [{"id": "a499611f-9be4-4b4d-b978-3cc4acbbd6f5", "created_at": "2024-06-02 09:34:42.000000 +00:00", "updated_at": null, "submission_id": "e7100b7d-6abf-480a-8ca9-6cff97f00b6a", "file_id": "fdbc95d8-47a2-4a29-886c-160d82b604c5", "organization_id": 1, "check_type": "data_validation", "original_value": "original_test_value", "original_value_source": "test_file", "check_status": "passed", "check_score": 95, "explanation": "Data validation check passed successfully", "details": {"validation_method": "automated", "confidence_level": "high"}, "suggested_value": "suggested_test_value", "suggested_value_reasoning": "Based on automated data validation rules", "suggested_value_confidence": 90}]}