from datetime import datetime
from uuid import uuid4

from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_enhancement_type import FileEnhancementType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.origin import Origin
from static_common.models.file_additional_info import FileAdditionalInfo
import pytest

from copilot.models import File
from copilot.models.files import EnhancedFile, FileMetric
from tests.unit.duplication import check_copy

# fields which are skipped because they are:
# - Are relations
# - Virtual columns
# - Not copied
# - etc..
SKIPPED_FIELDS = {
    "created_at",
    "updated_at",
    "submission",
    "child_file_ids",
    "parent_file_id",
    "parent_file",
    "processed_file",
    "verification_checks",
    "initial_processing_state",
    "metrics",
    "is_required_shadow_processing",
    "stuck_details",
    "internal_notes",
    "replaced_by_file_ids",  # This is tested separately
    "submission_level_extracted_data",
    "retry_count",
    "execution_arn",
    "processing_state_updated_at",
    "external_identifier",
    "hidden",
    "pds_stats",  # this is related to the loading of the file and we don't copy
    "cache_origin_file_id",
    "custom_file_type",
    "custom_file_type_id",
    "custom_classification",
    "user_shadow_state",
    "support_user_file",  # This is related to the loading of the file and we don't copy
    "auto_qa_checks",
}

NOT_NONE_FIELDS = {"id", "submission_business_id", "submission_id"}


@pytest.fixture
def original() -> File:
    original = File()
    original.id = uuid4()
    original.created_at = datetime.now()
    original.name = "Name"
    original.s3_key = "S3_key"
    original.file_type = FileType.CAT_RESULT
    original.classification = "Classification"
    original.submission_business_id = uuid4()
    original.submission_id = uuid4()
    original.user_id = 1
    original.organization_id = 10
    original.processing_state = FileProcessingState.DATA_ONBOARDED
    original.sensible_status = "Sensible status"
    original.origin = Origin.API
    original.comment = "Comment"
    original.user_file_type = FileType.OTHER
    original.additional_info = FileAdditionalInfo(acord=None)
    original.size = 1
    original.initial_classification = ClassificationDocumentType.OTHER
    original.initial_classification_confidence = 0
    original.issues = ["No rows found"]
    original.checksum = "1234567890"
    original.is_internal = True
    original.is_required_shadow_processing = True
    original.is_handsigned = False
    original.client_file_type = "CAT"
    original.client_file_tags = ["UW things"]
    original.description = "File description"
    original.external_identifier = uuid4()
    original.additional_file_names = ["test.pdf"]
    original.is_locked = False
    original.is_deleted = False
    original.content_type = "application/pdf"
    original.enhanced_files = [
        EnhancedFile(
            id=uuid4(),
            file_id=original.id,
            enhancement_type=FileEnhancementType.COLOR_HIGHLIGHT.value,
            s3_key="enhanced_file_s3_key",
        )
    ]
    return original


def test_copy_file(original: File):
    from copilot.app import app

    with app.app_context():
        copy = original.copy({original.submission_business_id: uuid4()}, app.submission_s3_client)
        check_copy(File, original, copy, SKIPPED_FIELDS, NOT_NONE_FIELDS, different_fields={"enhanced_files"})


def test_copy_file_in_pending_state(original: File):
    from copilot.app import app

    with app.app_context():
        original.processing_state = FileProcessingState.PROCESSING
        copy = original.copy({original.submission_business_id: uuid4()}, app.submission_s3_client)
        check_copy(
            File,
            original,
            copy,
            SKIPPED_FIELDS,
            NOT_NONE_FIELDS,
            different_fields={"processing_state", "enhanced_files"},
        )
        assert copy.processing_state == FileProcessingState.CLASSIFIED


def test_copy_file_in_different_org(original: File):
    from copilot.app import app

    with app.app_context():
        original.processing_state = FileProcessingState.PROCESSED
        original.file_type = FileType.CUSTOM
        copy = original.copy(
            {original.organization_id: original.organization_id + 1, original.submission_business_id: uuid4()},
            app.submission_s3_client,
        )
        skipped_fields = SKIPPED_FIELDS | {"custom_file_type_id", "custom_classification"}
        check_copy(
            File,
            original,
            copy,
            skipped_fields,
            NOT_NONE_FIELDS,
            different_fields={
                "custom_file_type_id",
                "custom_classification",
                "organization_id",
                "processing_state",
                "file_type",
                "enhanced_files",
            },
        )
        assert copy.processing_state == FileProcessingState.NOT_CLASSIFIED
        assert copy.file_type == FileType.UNKNOWN
        assert copy.custom_file_type_id is None
        assert copy.custom_classification is None


def test_copy_file_replaced_by_file_id(original: File):
    from copilot.app import app

    replaced_file_id = uuid4()
    new_replaced_file_id = uuid4()
    original.replaced_by_file_ids = [replaced_file_id]
    old_to_new = {original.submission_business_id: uuid4()}
    with app.app_context():
        copy = original.copy(old_to_new, app.submission_s3_client)
        assert copy.replaced_by_file_ids == []
        old_to_new[replaced_file_id] = new_replaced_file_id
        copy = original.copy(old_to_new, app.submission_s3_client)
        assert copy.replaced_by_file_ids == [new_replaced_file_id]


def test_copy_file_enhanced_files(original: File):
    from copilot.app import app

    with app.app_context():
        copy = original.copy({original.submission_business_id: uuid4()}, app.submission_s3_client)
        assert len(copy.enhanced_files) == 1
        enhanced_file = copy.enhanced_files[0]
        assert enhanced_file.file_id == copy.id
        assert enhanced_file.enhancement_type == FileEnhancementType.COLOR_HIGHLIGHT.value
        assert enhanced_file.s3_key == "enhanced_file_s3_key"
