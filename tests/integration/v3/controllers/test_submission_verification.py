from datetime import datetime
from unittest.mock import Magic<PERSON>ock
import os
import uuid

from common.clients.facts import FactsClient
from dateutil.relativedelta import relativedelta
from facts_client.model.document_count import DocumentCount
from flask import g
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.metric import MetricType
from static_common.enums.organization import ExistingOrganizations, OrganizationGroups
from static_common.enums.parent import ParentType
from static_common.enums.submission import (
    SubmissionMode,
    SubmissionRelationType,
    SubmissionStage,
)
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.enums.submission_client_id import SubmissionClientIdSource
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.enums.underwriters import SubmissionUserSource
from werkzeug.exceptions import Conflict, UnprocessableEntity
import flask
import pytest
import pytz

from copilot.clients import SendGridNotificationsClient
from copilot.clients.ers_v3 import ERSClientV3
from copilot.clients.lambdas import LambdaClient
from copilot.logic.generated_notes import (
    generate_note_for_recommendation,
    generate_note_for_submission_lost,
)
from copilot.logic.pds.file_handler import FileHandler
from copilot.models import (
    File,
    ReportV2,
    Submission,
    SubmissionBusiness,
    SubmissionHistory,
    VerificationCheckResult,
    db,
)
from copilot.models.metric_preferences import MetricPreference
from copilot.models.metrics import MetricV2
from copilot.models.organization import Organization
from copilot.models.reports import (
    Coverage,
    RecommendationSubmissionNoteRequest,
    ReportShadowDependency,
    SubmissionClientId,
)
from copilot.models.secura_uw_mappings import SecuraUWMapping
from copilot.models.submission_processing import SubmissionProcessing
from copilot.models.submission_relations import SubmissionRelation
from copilot.models.types import (
    CoverageType,
    PermissionType,
    ReportShadowType,
    SubmissionActionType,
    VerificationCheckStages,
)
from copilot.models.verification_check import ResultStatus
from copilot.services.orgs.secura_mapping.utils import SECURA_PILOT_UWS
from copilot.v3.controllers.submission_verification import (
    MANUAL_VERIFY_CUTOFF,
    VERIFYING_SUBMISSIONS_CUTOFF_TIME,
    WAITING_FOR_AUTO_VERIFY_CUTOFF,
    _is_verification_idle,
    get_verification_candidates,
    verify,
)
from copilot.v3.utils.verification_checks import (
    REQUIRED_FACTS_GC,
    REQUIRED_PROPERTY_FACTS,
    CheckResult,
    _check_aru_property_description_or_occupancy,
    _check_data_from_supplementals,
    _check_entity_with_most_facts,
    _check_fact_over_threshold,
    _check_has_at_least_one_fni,
    _check_has_more_than_one_fni,
    _check_news_and_lawsuits,
    _check_there_is_no_loss_more_than_25_million,
    _has_limit_and_attachment_point_for_excess,
    _has_property_information,
)
from tests.integration.factories import (
    broker_fixture,
    brokerage_fixture,
    coverage_fixture,
    email_fixture,
    file_fixture,
    loss_fixture,
    metric_preference_fixture,
    metric_v2_fixture,
    organization_fixture,
    report_and_submission_fixture,
    report_email_correspondence_fixture,
    report_fixture,
    report_permission_fixture,
    report_with_submissions_fixture,
    settings_fixture,
    shadow_submission_fixture,
    submission_business_fixture,
    submission_client_id_fixture,
    submission_coverage_fixture,
    submission_files_data_fixture,
    submission_fixture,
    submission_history_fixture,
    submission_user_fixture,
    user_fixture,
)
from tests.integration.logic.test_clearing import UUID_1
from tests.integration.utils import AnonObj

VERIFICATION_TYPES = [
    SubmissionActionType.PDS_VERIFICATION_COMPLETED,
    SubmissionActionType.PDS_AUTO_VERIFICATION_COMPLETED,
    SubmissionActionType.PDS_MANUAL_VERIFICATION_COMPLETED,
]

DEFAULT_NUMBER_OF_CHECKS = 28
DEFAULT_NUMBER_OF_CHECKS_FOR_CS_MANAGER = 29


@pytest.fixture
def setup_common_mocks(mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            is_internal_machine_user=True,
            has_submission_permission=lambda type, id: True,
            email="<EMAIL>",
            is_being_impersonated=False,
            is_cs_manager_or_internal_machine_user=False,
            is_tier_2_or_internal_machine_user=False,
            has_report_permission=MagicMock(return_value=True),
        ),
    )
    flask.current_app.notifications_client = MagicMock(spec=SendGridNotificationsClient)
    flask.current_app.notifications_handler_v2 = MagicMock()
    flask.current_app.knock_client = MagicMock()

    mocker.patch.dict(os.environ, {"IS_TEST_ENV": "True", "INTERACT_WITH_EXTERNAL_RESOURCES": "False"})
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )


def _set_agent_agency(submission: Submission) -> None:
    brokerage = brokerage_fixture(organization_id=submission.organization_id)
    db.session.flush()
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=submission.organization_id)
    db.session.flush()

    submission.brokerage_id = brokerage.id
    submission.broker_id = broker.id
    submission.brokerage_contact_id = broker.id


def test_verify_submission(app_context, request_context, setup_common_mocks):
    organization_fixture()
    user_fixture()
    report = report_with_submissions_fixture()
    submission = report.submissions[0]
    capi_business_id = uuid.uuid4()

    submission_business = SubmissionBusiness(id=capi_business_id, submission_id=submission.id)
    db.session.add(submission_business)
    db.session.commit()

    result, status = verify({}, str(submission.id), False)

    submission = Submission.query.get(submission.id)
    check_results = result["check_results"]

    assert not submission.is_verified
    assert not submission.is_auto_verified
    assert not submission.is_manual_verified
    assert not result["verified"]
    assert len(check_results) == DEFAULT_NUMBER_OF_CHECKS
    assert next(r for r in check_results if r["name"] == "Existing Businesses")["status"] == ResultStatus.SUCCESS
    assert next(r for r in check_results if r["name"] == "Confirmed Businesses")["status"] == ResultStatus.ERROR
    assert (
        next(r for r in check_results if r["name"] == "Submission Level NAICS Populated")["status"]
        == ResultStatus.ERROR
    )

    __verify_zero_submission_history_record_added(submission.id, SubmissionActionType.PDS_VERIFICATION_COMPLETED)


def test_get_verify_candidates(app_context, request_context, mocker):
    organization_fixture()
    user_fixture()
    report_and_submission_fixture()
    _, f1 = report_and_submission_fixture(is_auto_processed=True, is_verification_required=True)
    _, f2 = report_and_submission_fixture(is_auto_processed=True, is_verified=True, is_verification_required=True)
    _, f3 = report_and_submission_fixture(is_auto_processed=True, is_verified=False, is_verification_required=True)
    _, f4 = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=False,
        is_manual_verified=False,
        is_auto_verified=True,
        is_verification_required=True,
    )
    _, f5 = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=False,
        is_manual_verified=False,
        processing_state=SubmissionProcessingState.ENTITY_MAPPING,
        is_verification_required=True,
    )
    _, f6 = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=True,
        is_manual_verified=True,
        is_auto_verified=False,
        is_verification_require=True,
    )
    _, f7 = report_and_submission_fixture(
        is_auto_processed=False,
        is_verified=False,
        is_manual_verified=True,
        is_auto_verified=False,
        is_verification_required=True,
    )
    _, f8 = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=False,
        is_manual_verified=False,
        processing_state=SubmissionProcessingState.COMPLETED,
        is_verification_required=True,
    )
    _, f9 = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=False,
        is_manual_verified=True,
        is_auto_verified=False,
        is_verification_required=False,
    )
    _, f10 = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=False,
        is_manual_verified=True,
        is_auto_verified=False,
        is_verification_required=True,
        processing_state=SubmissionProcessingState.DATA_ONBOARDING,
    )
    _, s1 = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=False,
        is_manual_verified=True,
        is_auto_verified=False,
        is_verification_required=True,
        processing_state=SubmissionProcessingState.COMPLETED,
    )

    db.session.commit()
    submission_ids = get_verification_candidates()[0]["submission_ids"]
    for f in [f1, f2, f3, f4, f5, f6, f7, f8, f9, f10]:
        assert str(f.id) not in submission_ids
    for s in [s1]:
        assert str(s.id) in submission_ids


def test_get_verify_candidates_for_all_orgs(app_context, request_context, mocker):
    kalepa_org_id = next(iter(Organization.kalepa_organizations()))
    client_user_id = 2

    organization_fixture()
    organization_fixture(id=kalepa_org_id)
    user_fixture(organization_id=1)
    user_fixture(organization_id=kalepa_org_id, id=client_user_id)
    _, client_submission = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=False,
        is_manual_verified=True,
        is_auto_verified=False,
        is_verification_required=True,
        processing_state=SubmissionProcessingState.COMPLETED,
    )
    _, kalepa_submission = report_and_submission_fixture(
        is_auto_processed=True,
        is_verified=False,
        is_manual_verified=True,
        is_auto_verified=False,
        is_verification_required=True,
        processing_state=SubmissionProcessingState.COMPLETED,
        owner_id=client_user_id,
        organization_id=kalepa_org_id,
    )
    db.session.commit()
    submission_ids = get_verification_candidates()[0]["submission_ids"]
    assert str(client_submission.id) in submission_ids
    assert str(kalepa_submission.id) in submission_ids


def test_verify_saved(app_context, request_context, setup_common_mocks):
    organization_fixture()
    user_fixture()
    report = report_fixture()
    s = submission_fixture(report=report)
    db.session.flush()
    db.session.commit()
    verify({}, str(s.id), manual=False, force_verify=True, force=False)
    query = db.session.query(VerificationCheckResult).filter_by(submission_id=s.id)
    results = query.all()
    assert len(results) > 0
    check = results[0]
    assert not check.force_manual
    assert check.force_verify
    assert not check.manual
    assert check.stage == VerificationCheckStages.COMPLETED


def test_force_verify(app_context, request_context, setup_common_mocks):
    organization_fixture()
    user_fixture()
    coverage = coverage_fixture()
    report = report_fixture()
    submission = submission_fixture(is_verified=True, is_auto_verified=True, report=report)
    submission_business_fixture(
        submission_id=submission.id,
        business_id=uuid.uuid4(),
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    submission.primary_naics_code = "NAICS_111110"
    submission.coverages = [submission_coverage_fixture(submission_id=submission.id, coverage_id=coverage.id)]
    _set_agent_agency(submission)
    db.session.flush()
    db.session.commit()
    with pytest.raises(Conflict):
        verify({}, str(submission.id), force_verify=True, manual=False, force=False)
    submission.is_verified = False
    db.session.commit()
    with pytest.raises(Conflict):
        verify({}, str(submission.id), manual=False)
    verify({}, str(submission.id), manual=False, force_verify=True, force=False)

    submission = Submission.query.get(submission.id)
    report = submission.report
    assert submission.primary_naics_code
    assert submission.is_verified
    assert report.organization_permission_level == PermissionType.EDITOR

    with pytest.raises(Conflict):
        verify({}, str(submission.id), manual=False, force_verify=True)


def test_auto_verify_submission(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], True, False, []))

    organization_fixture()
    user = user_fixture(email="<EMAIL>")
    report = report_fixture()
    submission = submission_fixture(report=report)
    submission_user_fixture(user.id, submission.id)
    db.session.flush()
    db.session.commit()

    assert submission.active_submission_processing is None

    submission.is_auto_processed = True
    submission.processing_state = SubmissionProcessingState.COMPLETED
    db.session.commit()

    assert submission.active_submission_processing is not None
    assert submission.active_submission_processing.is_active
    assert not submission.active_submission_processing.is_verified

    verify({}, str(submission.id), manual=True, force=False)
    submission = Submission.query.get(submission.id)
    assert submission.is_manual_verified
    assert submission.active_submission_processing.is_manual_verified
    assert not submission.is_auto_verified
    assert not submission.is_verified
    assert not submission.active_submission_processing.is_verified

    verify({}, str(submission.id), manual=False, force=False)
    verify({}, str(submission.id), manual=False, force=False, force_verify=True)

    submission = Submission.query.get(submission.id)
    assert submission.is_auto_verified
    assert submission.is_verified
    assert submission.is_manual_verified
    assert submission.active_submission_processing is None
    submission_processing = SubmissionProcessing.query.filter_by(submission_id=submission.id).first()
    assert submission_processing.is_verified
    __verify_one_submission_history_record_added(submission.id, SubmissionActionType.PDS_AUTO_VERIFICATION_COMPLETED)
    __verify_one_submission_history_record_added(submission.id, SubmissionActionType.PDS_VERIFICATION_COMPLETED)
    __verify_one_submission_history_record_added(submission.id, SubmissionActionType.PDS_MANUAL_VERIFICATION_COMPLETED)
    report = ReportV2.query.get(report.id)
    assert report.organization_permission_level == PermissionType.EDITOR

    flask.current_app.knock_client.maybe_publish_report_shared_to_recipient.assert_called()


def test_manual_verify_submission(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(report=report)
    db.session.flush()
    db.session.commit()

    verify({}, str(submission.id), manual=True, force=False)

    submission = Submission.query.get(submission.id)
    assert not submission.is_auto_verified
    assert not submission.is_verified
    assert not submission.is_manual_verified
    for action_type in VERIFICATION_TYPES:
        __verify_zero_submission_history_record_added(submission.id, action_type)
    report = ReportV2.query.get(report.id)
    assert not report.organization_permission_level == PermissionType.EDITOR

    verify({}, str(submission.id), manual=True, force=True)
    for action_type in VERIFICATION_TYPES:
        if action_type == SubmissionActionType.PDS_MANUAL_VERIFICATION_COMPLETED:
            __verify_one_submission_history_record_added(submission.id, action_type)
        else:
            __verify_zero_submission_history_record_added(submission.id, action_type)
    report = ReportV2.query.get(report.id)
    assert not report.organization_permission_level == PermissionType.EDITOR


def test_manual_verify_after_auto_v2_submission(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], True, False, []))

    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(report=report)
    db.session.flush()
    db.session.commit()

    verify({}, str(submission.id), manual=False, force=False)
    verify({}, str(submission.id), manual=False, force=False, force_verify=True)

    submission = Submission.query.get(submission.id)
    report = ReportV2.query.get(report.id)
    assert report.organization_permission_level == PermissionType.EDITOR
    report.organization_permission_level = PermissionType.VIEWER
    db.session.commit()

    verify({}, str(submission.id), manual=True, force=True)
    __verify_one_submission_history_record_added(submission.id, SubmissionActionType.PDS_MANUAL_VERIFICATION_COMPLETED)
    submission = Submission.query.get(submission.id)
    assert not report.organization_permission_level == PermissionType.EDITOR
    assert submission.is_manual_verified


def test_force_verify_manual_v2(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(
        is_manual_verified=True,
        manual_verified_at=datetime.utcnow() - MANUAL_VERIFY_CUTOFF - relativedelta(minutes=1),
        report=report,
    )

    db.session.flush()
    db.session.commit()
    verify({}, str(submission.id), manual=False, force=False)
    submission = Submission.query.get(submission.id)

    assert submission.is_auto_verified
    verify({}, str(submission.id), manual=False, force=False, force_verify=True)
    assert submission.is_verified


def test_force_verify_is_waiting_for_auto_verify(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    now = datetime.utcnow().replace(tzinfo=pytz.UTC)
    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(is_manual_verified=False, is_waiting_for_auto_verify=True, report=report)
    _set_agent_agency(submission)
    submission.updated_at = now - WAITING_FOR_AUTO_VERIFY_CUTOFF - relativedelta(minutes=1)
    db.session.flush()
    db.session.commit()

    assert submission.is_waiting_for_auto_verify
    assert _is_verification_idle(submission, now)

    verify({}, str(submission.id), manual=False, force=False)
    submission = Submission.query.get(submission.id)

    assert submission.is_auto_verified
    assert submission.is_waiting_for_auto_verify
    assert not submission.is_verified
    verify({}, str(submission.id), manual=False, force=False, force_verify=True)
    assert submission.is_verified


def test_force_verify_is_waiting_for_auto_verify_before_cutoff(
    app_context, request_context, mocker, setup_common_mocks
):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(
        is_manual_verified=False,
        is_waiting_for_auto_verify=True,
        updated_at=datetime.utcnow() - WAITING_FOR_AUTO_VERIFY_CUTOFF + relativedelta(minutes=1),
        report=report,
    )
    db.session.flush()
    db.session.commit()
    verify({}, str(submission.id), manual=False, force=False)
    submission = Submission.query.get(submission.id)

    assert not submission.is_verified
    assert submission.is_waiting_for_auto_verify


def test_verify_submission_force(app_context, request_context, setup_common_mocks):
    organization_fixture()
    user_fixture()
    coverage = coverage_fixture()
    report = report_fixture()
    submission = submission_fixture(report=report)
    submission_business_fixture(
        submission_id=submission.id,
        business_id=uuid.uuid4(),
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    submission.coverages = [submission_coverage_fixture(submission_id=submission.id, coverage_id=coverage.id)]
    submission.primary_naics_code = "NAICS_111110"
    _set_agent_agency(submission)
    db.session.flush()
    capi_business_id = uuid.uuid4()

    submission_business = SubmissionBusiness(id=capi_business_id, submission_id=submission.id, business_id=uuid.uuid4())
    db.session.add(submission_business)
    db.session.commit()

    result, status = verify({}, str(submission.id), True)

    submission = Submission.query.get(submission.id)

    assert not submission.is_verified
    assert not submission.is_auto_verified
    assert submission.is_manual_verified
    assert not result["verified"]
    assert not result["auto_verified"]
    assert result["manual_verified"]

    result, status = verify({}, str(submission.id), force=False, manual=False)
    check_results = result["check_results"]
    assert len(check_results) == DEFAULT_NUMBER_OF_CHECKS
    assert next(r for r in check_results if r["name"] == "Existing Businesses")["status"] == ResultStatus.SUCCESS
    assert next(r for r in check_results if r["name"] == "Confirmed Businesses")["status"] == ResultStatus.SUCCESS

    __verify_one_submission_history_record_added(submission.id, SubmissionActionType.PDS_MANUAL_VERIFICATION_COMPLETED)


def test_verify_submission_with_confirmed_businesses(app_context, request_context, setup_common_mocks):
    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(mode=SubmissionMode.GC_PROJECT, primary_naics_code="NAICS_810000", report=report)
    capi_business_id = uuid.uuid4()
    business_id = uuid.uuid4()

    facts_client_mock = MagicMock(spec=FactsClient)
    facts_client_mock.count_facts.return_value = [AnonObj(parent_id=business_id, count=20)]
    flask.current_app.facts_client = facts_client_mock

    flask.current_app.lambda_client = MagicMock(spec=LambdaClient)
    flask.current_app.lambda_client.invoke_select_scrapers_bulk_lambda.return_value = {
        business_id: {"scrape_buildzoom": True}
    }

    submission_business = SubmissionBusiness(id=capi_business_id, business_id=business_id, submission_id=submission.id)
    db.session.add(submission_business)
    db.session.commit()

    result, status = verify({}, str(submission.id), False)

    check_results = result["check_results"]
    assert not result["verified"]
    assert len(check_results) == DEFAULT_NUMBER_OF_CHECKS
    assert (
        next(r for r in check_results if r["name"] == "Vehicles Populated (if needed)")["status"]
        == ResultStatus.SUCCESS
    )

    __verify_zero_submission_history_record_added(submission.id, SubmissionActionType.PDS_VERIFICATION_COMPLETED)


def test_verify_submission_without_permits_with_bz_profile(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.utils.verification_checks.check_submission_permits_exists", return_value=False)

    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(
        mode=SubmissionMode.GC_PROJECT,
        primary_naics_code="NAICS_810000",
        report=report,
        processing_state=SubmissionProcessingState.COMPLETED,
    )
    capi_business_id = uuid.uuid4()
    business_id = uuid.uuid4()

    facts_client_mock = MagicMock(spec=FactsClient)
    facts_client_mock.count_facts.return_value = [AnonObj(parent_id=business_id, count=20)]
    flask.current_app.facts_client = facts_client_mock

    flask.current_app.lambda_client = MagicMock(spec=LambdaClient)
    flask.current_app.lambda_client.invoke_select_scrapers_bulk_lambda.return_value = {
        str(business_id): {"scrape_buildzoom": True}
    }

    submission_business = SubmissionBusiness(id=capi_business_id, business_id=business_id, submission_id=submission.id)
    db.session.add(submission_business)
    db.session.commit()

    result, status = verify({}, str(submission.id), False)

    check_results = result["check_results"]
    assert not result["verified"]
    assert len(check_results) == DEFAULT_NUMBER_OF_CHECKS
    assert VerificationCheckResult.query.count() == DEFAULT_NUMBER_OF_CHECKS_FOR_CS_MANAGER + 5
    assert (
        VerificationCheckResult.query.filter_by(
            status=ResultStatus.ERROR, name="Submission does not have permits although there is a buildzoom profile"
        ).count()
        == 1
    )


def test_verify_submission_with_confirmed_businesses_without_naics(app_context, request_context, setup_common_mocks):
    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(mode=SubmissionMode.GC_PROJECT, report=report)
    capi_business_id = uuid.uuid4()
    business_id = uuid.uuid4()

    facts_client_mock = MagicMock(spec=FactsClient)
    facts_client_mock.count_facts.return_value = [AnonObj(parent_id=business_id, count=20)]
    flask.current_app.facts_client = facts_client_mock

    flask.current_app.lambda_client = MagicMock(spec=LambdaClient)
    flask.current_app.lambda_client.invoke_select_scrapers_bulk_lambda.return_value = {
        business_id: {"scrape_buildzoom": True}
    }

    submission_business = SubmissionBusiness(id=capi_business_id, business_id=business_id, submission_id=submission.id)
    db.session.add(submission_business)
    db.session.commit()

    result, status = verify({}, str(submission.id), False)

    check_results = result["check_results"]
    assert not result["verified"]
    assert len(check_results) == DEFAULT_NUMBER_OF_CHECKS
    assert (
        next(r for r in check_results if r["name"] == "Submission Level NAICS Populated")["status"]
        == ResultStatus.ERROR
    )

    __verify_zero_submission_history_record_added(submission.id, SubmissionActionType.PDS_VERIFICATION_COMPLETED)


def __verify_one_submission_history_record_added(
    submission_id: uuid.UUID, submission_action_type: SubmissionActionType
):
    submission_history = (
        SubmissionHistory.query.filter(SubmissionHistory.submission_id == submission_id)
        .filter(SubmissionHistory.submission_action_type == submission_action_type)
        .all()
    )
    assert len(submission_history) == 1
    assert submission_history[0].submission_id == submission_id


def __verify_zero_submission_history_record_added(
    submission_id: uuid.UUID, submission_action_type: SubmissionActionType
):
    submission_history = (
        SubmissionHistory.query.filter(SubmissionHistory.submission_id == submission_id)
        .filter(SubmissionHistory.submission_action_type == submission_action_type)
        .all()
    )
    assert len(submission_history) == 0


def test_verify_submission_with_exceptions(app_context, request_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=True,
            has_submission_permission=lambda type, id: True,
            organization_id=1,
            email="<EMAIL>",
            is_being_impersonated=False,
            is_cs_manager_or_internal_machine_user=True,
        ),
    )
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": True})
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )
    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(mode=SubmissionMode.GC_PROJECT, primary_naics_code="NAICS_480000", report=report)
    capi_business_id = uuid.uuid4()
    business_id = uuid.uuid4()

    submission_business = SubmissionBusiness(id=capi_business_id, business_id=business_id, submission_id=submission.id)
    db.session.add(submission_business)
    db.session.commit()

    mocker.patch("flask.current_app.ers_client_v3.get_entities", side_effect=KeyError)

    flask.current_app.facts_client = MagicMock(spec=FactsClient)
    flask.current_app.facts_client.get_facts.return_value = []

    result, status = verify({}, str(submission.id), False)

    check_results = result["check_results"]
    assert not result["verified"]
    assert len(check_results) == DEFAULT_NUMBER_OF_CHECKS_FOR_CS_MANAGER
    assert (
        next(r for r in check_results if r["name"] == "Vehicles Populated (if needed)")["status"] == ResultStatus.ERROR
    )


def test_hard_checks_failing(app_context, request_context, setup_common_mocks):
    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(is_verified=False, is_auto_verified=False, report=report)
    db.session.commit()

    with pytest.raises(UnprocessableEntity):
        verify({}, str(submission.id), force_verify=False, manual=True, force=True)
    verify({}, str(submission.id), force_verify=True, manual=False, force=False)

    assert not submission.is_verified
    assert submission.stuck_reason is not None


def test_waiting_for_auto_verify(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission = submission_fixture(
        is_verified=False, is_auto_verified=False, is_waiting_for_auto_verify=True, report=report
    )
    db.session.commit()

    verify({}, str(submission.id), force_verify=True, manual=False, force=False)

    assert submission.is_auto_verified
    assert submission.is_waiting_for_auto_verify

    verify({}, str(submission.id), force_verify=True, manual=False, force=False)
    assert submission.is_verified
    assert submission.is_waiting_for_auto_verify is False
    assert submission.is_auto_processed


def test_verify_shadow_report(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()
    shadow_submission = shadow_submission_fixture(submission)
    shadow_submission.processing_state = SubmissionProcessingState.COMPLETED
    shadow_report = shadow_submission.report
    original_sub_client_id = submission_client_id_fixture(
        client_submission_id="just some test id", submission_id=submission.id, source=SubmissionClientIdSource.API
    )
    submission.client_submission_ids.append(original_sub_client_id)
    db.session.commit()

    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    assert shadow_submission.is_auto_verified
    assert not shadow_submission.is_verified
    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    db.session.commit()

    shadow_submission = Submission.query.get(shadow_submission.id)
    shadowed_submission = Submission.query.get(submission.id)
    report_shadow_dependency = ReportShadowDependency.query.filter(
        ReportShadowDependency.report_id == shadowed_submission.report_id
    ).first()

    assert shadow_submission.is_auto_verified
    assert shadow_submission.report_id == report.id
    assert shadowed_submission.report_id == shadow_report.id
    assert shadowed_submission.report.is_deleted
    assert shadowed_submission.report.shadow_type == ReportShadowType.IS_SHADOW_ORIGIN
    assert shadow_submission.is_verified
    assert shadow_submission.report.shadow_type is None
    assert report_shadow_dependency.is_active is False
    assert len(shadow_submission.client_submission_ids) == 1
    assert shadow_submission.client_submission_ids[0].client_submission_id == "just some test id"


def test_verify_shadow_report_when_client_ids_are_cleaned_in_existing_sub(
    app_context, request_context, mocker, setup_common_mocks
):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture()
    # Create original submission with client ids and shadow will get them as well
    report, submission = report_and_submission_fixture()
    original_sub_client_id = submission_client_id_fixture(
        client_submission_id="just some test id", submission_id=submission.id, source=SubmissionClientIdSource.API
    )
    submission.client_submission_ids.append(original_sub_client_id)
    shadow_submission = shadow_submission_fixture(submission)
    shadow_submission.processing_state = SubmissionProcessingState.COMPLETED
    shadow_report = shadow_submission.report
    # Then clean the client ids from the original submission (shadow still has them)
    submission.client_submission_ids.clear()
    db.session.commit()

    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    db.session.commit()

    shadow_submission = Submission.query.get(shadow_submission.id)
    shadowed_submission = Submission.query.get(submission.id)

    # After verification, shadow should have cleared client ids
    assert shadow_submission.report_id == report.id
    assert shadowed_submission.report_id == shadow_report.id
    assert shadowed_submission.report.is_deleted
    assert shadowed_submission.report.shadow_type == ReportShadowType.IS_SHADOW_ORIGIN
    assert shadow_submission.is_verified
    assert shadow_submission.report.shadow_type is None
    assert len(shadow_submission.client_submission_ids) == 0


def test_verify_shadow_report_with_submission_notes(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()

    shadow_submission = shadow_submission_fixture(submission)
    shadow_submission.processing_state = SubmissionProcessingState.COMPLETED
    shadow_report = shadow_submission.report

    submission.submission_notes.append(
        generate_note_for_recommendation(
            RecommendationSubmissionNoteRequest(submission_id=submission.id, text="sub note", rule_id=uuid.uuid4())
        )
    )
    submission.submission_notes.append(
        generate_note_for_submission_lost(submission_id=shadow_submission.id, submission_change=submission)
    )

    shadow_submission.submission_notes.append(
        generate_note_for_recommendation(
            RecommendationSubmissionNoteRequest(
                submission_id=shadow_submission.id, text="shadow note", rule_id=uuid.uuid4()
            )
        )
    )

    db.session.commit()

    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    db.session.commit()

    shadow_submission = Submission.query.get(shadow_submission.id)
    shadowed_submission = Submission.query.get(submission.id)

    assert shadow_submission.report_id == report.id
    assert shadowed_submission.report_id == shadow_report.id
    assert shadowed_submission.report.is_deleted
    assert shadowed_submission.report.shadow_type == ReportShadowType.IS_SHADOW_ORIGIN
    assert shadow_submission.is_verified
    assert shadow_submission.report.shadow_type is None
    # After verification, shadow should have synced notes from original submission except those from recommendations
    assert len(shadow_submission.submission_notes) == 2
    assert any(note.text_content == "shadow note" for note in shadow_submission.submission_notes)


@pytest.mark.parametrize("uw_with_manual_source", [True, False])
def test_verify_shadow_report_with_assigned_underwriters(
    app_context, request_context, mocker, setup_common_mocks, uw_with_manual_source
):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture(id=1)
    user_fixture(id=2)
    user_fixture(id=3)
    user_fixture(id=4)
    report, submission = report_and_submission_fixture()

    shadow_submission = shadow_submission_fixture(submission)
    shadow_submission.processing_state = SubmissionProcessingState.COMPLETED
    shadow_report = shadow_submission.report

    submission.assigned_underwriters.append(
        submission_user_fixture(submission_id=submission.id, user_id=2, source=SubmissionUserSource.EMAIL)
    )
    submission.assigned_underwriters.append(
        submission_user_fixture(
            submission_id=submission.id,
            user_id=3,
            source=SubmissionUserSource.MANUAL if uw_with_manual_source else SubmissionUserSource.AUTO,
        )
    )

    shadow_submission.assigned_underwriters.append(
        submission_user_fixture(submission_id=shadow_submission.id, user_id=2, source=SubmissionUserSource.EMAIL)
    )
    shadow_submission.assigned_underwriters.append(
        submission_user_fixture(submission_id=shadow_submission.id, user_id=4, source=SubmissionUserSource.AUTO)
    )

    db.session.commit()

    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    db.session.commit()

    shadow_submission = Submission.query.get(shadow_submission.id)
    shadowed_submission = Submission.query.get(submission.id)

    assert shadow_submission.report_id == report.id
    assert shadowed_submission.report_id == shadow_report.id
    assert shadowed_submission.report.is_deleted
    assert shadowed_submission.report.shadow_type == ReportShadowType.IS_SHADOW_ORIGIN
    assert shadow_submission.is_verified
    assert shadow_submission.report.shadow_type is None
    # After verification, shadow should have synced assigned UW from original submission if they are manually assigned
    assert len(shadow_submission.assigned_underwriters) == 2
    expected_user_ids = {2, 3} if uw_with_manual_source else {2, 4}
    assert {uw.user_id for uw in shadow_submission.assigned_underwriters} == expected_user_ids


def test_verify_shadow_report_when_files_are_uploaded_to_boss(app_context, request_context, mocker, setup_common_mocks):
    files_added = []
    files_deleted = []

    def file_added(s: Submission, f: File, start_file_processing: bool = True) -> None:
        files_added.append(
            {"report_id": str(s.report_id), "file_id": str(f.id), "start_file_processing": start_file_processing}
        )
        return None

    def file_deleted(s: Submission, f: File) -> None:
        files_deleted.append({"report_id": str(s.report_id), "file_id": str(f.id)})
        return None

    mocked_file_added = MagicMock(side_effect=file_added)
    mocked_file_delete = MagicMock(side_effect=file_deleted)
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event",
        mocked_file_added,
    )
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_deleted_event",
        mocked_file_delete,
    )
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    org = organization_fixture(id=6)
    user_fixture(organization_id=org.id)
    # Create original submission with client ids and shadow will get them as well
    report, submission = report_and_submission_fixture(organization_id=org.id, origin="API")
    shadow_submission = shadow_submission_fixture(submission, with_files=False)
    shadow_submission.processing_state = SubmissionProcessingState.COMPLETED
    shadow_report = shadow_submission.report
    external_id1 = uuid.uuid4()
    external_id2 = uuid.uuid4()
    raw_email_id = uuid.uuid4()
    email_child_id = uuid.uuid4()
    deleted_file_id = uuid.uuid4()
    submission_files = [
        file_fixture(
            id=uuid.uuid4(),
            name="Uploaded to BOSS",
            submission_id=submission.id,
            is_internal=False,
            checksum="1234",
            external_identifier=external_id1,
            client_file_type="Submission",
            client_file_tags={"boss_doc_id": "1234", "tag": "value1"},
        ),
        file_fixture(
            id=uuid.uuid4(),
            name="Uploaded to BOSS second file",
            submission_id=submission.id,
            is_internal=False,
            checksum="4321",
            external_identifier=external_id2,
            client_file_type="Submission",
            client_file_tags={"boss_doc_id": "4321", "tag": "value2"},
        ),
        file_fixture(
            id=uuid.uuid4(),
            name="File not uploaded to BOSS",
            submission_id=submission.id,
            is_internal=False,
            checksum="12344325",
        ),
        file_fixture(
            id=deleted_file_id,
            name="File uploaded to BOSS that should be deleted",
            submission_id=submission.id,
            is_internal=False,
            checksum="1122",
            client_file_type="Submission",
            client_file_tags={"boss_doc_id": "1122", "tag": "value2"},
            external_identifier=deleted_file_id,
        ),
    ]
    shadow_submission_files = [
        file_fixture(
            id=uuid.uuid4(),
            name="Uploaded to BOSS",
            submission_id=shadow_submission.id,
            is_internal=False,
            checksum="1234",
            external_identifier=external_id1,
            client_file_type="Submission",
            client_file_tags={"boss_doc_id": "1234"},
        ),
        file_fixture(
            id=raw_email_id,
            name="Should be uploaded to BOSS it is an email",
            submission_id=shadow_submission.id,
            is_internal=False,
            checksum="1234231",
            file_type=FileType.RAW_EMAIL,
        ),
        file_fixture(
            id=email_child_id,
            name="Should be uploaded to BOSS",
            submission_id=shadow_submission.id,
            is_internal=False,
            checksum="12342313",
            parent_file_id=raw_email_id,
        ),
        file_fixture(
            id=uuid.uuid4(),
            name="Uploaded to BOSS second file",
            submission_id=shadow_submission.id,
            is_internal=False,
            checksum="4321",
        ),
    ]
    for sf in submission_files:
        submission.files.append(sf)
    for ssf in shadow_submission_files:
        shadow_submission.files.append(ssf)

    db.session.commit()

    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    verify({}, str(shadow_submission.id), force_verify=True, manual=False, force=False)
    db.session.commit()

    shadow_submission = Submission.query.get(shadow_submission.id)
    shadowed_submission = Submission.query.get(submission.id)

    # After verification, shadow should have cleared client ids
    assert shadow_submission.report_id == report.id
    assert shadowed_submission.report_id == shadow_report.id
    assert shadowed_submission.report.is_deleted
    assert shadowed_submission.report.shadow_type == ReportShadowType.IS_SHADOW_ORIGIN
    assert shadow_submission.is_verified
    assert shadow_submission.report.shadow_type is None

    shadow_submission_files = File.query.filter(File.submission_id == shadow_submission.id).all()
    checksum_to_file: dict[str, File] = {f.checksum: f for f in shadow_submission_files}

    assert checksum_to_file["1234"].client_file_type == "Submission"
    assert checksum_to_file["1234"].client_file_tags == {"boss_doc_id": "1234", "tag": "value1"}
    assert checksum_to_file["1234"].external_identifier == external_id1

    assert checksum_to_file["4321"].client_file_type == "Submission"
    assert checksum_to_file["4321"].client_file_tags == {"boss_doc_id": "4321", "tag": "value2"}
    assert checksum_to_file["4321"].external_identifier == external_id2

    assert files_added == [
        {"report_id": str(report.id), "file_id": str(raw_email_id), "start_file_processing": False},
        {"report_id": str(report.id), "file_id": str(email_child_id), "start_file_processing": False},
    ]

    assert files_deleted == [{"report_id": str(report.id), "file_id": str(deleted_file_id)}]

    assert len(shadow_submission.client_submission_ids) == 0


def test_check_news_and_lawsuits(app_context, request_context, mocker):
    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture(mode=SubmissionMode.GC_PROJECT)

    facts_client_mock = MagicMock(spec=FactsClient)
    facts_client_mock.get_document_counts.return_value = []
    flask.current_app.facts_client = facts_client_mock

    # Skipped, no LR
    assert _check_news_and_lawsuits(submission) == CheckResult(True, skipped=True)

    # Small loss
    loss_fixture(sum_of_total_net_incurred=50 * 1000, submission_id=submission.id, claim_number="1")
    loss_fixture(sum_of_total_net_incurred=1, submission_id=submission.id, claim_number="2")
    assert _check_news_and_lawsuits(submission) == CheckResult(True, skipped=True)

    # Bigger loss
    loss_fixture(sum_of_total_net_incurred=50 * 1000, submission_id=submission.id, claim_number="1")
    assert not _check_news_and_lawsuits(submission).success

    # Has documents
    facts_client_mock.reset_mock()
    facts_client_mock.get_document_counts.return_value = [DocumentCount(count=100)]
    assert _check_news_and_lawsuits(submission).success


def test_check_entity_with_most_facts(app_context, request_context, mocker):
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": True})
    flask.current_app.facts_client = MagicMock(spec=FactsClient)

    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture(mode=SubmissionMode.GC_PROJECT)
    sb1 = SubmissionBusiness(
        id=uuid.uuid4(),
        submission_id=submission.id,
        entity_role=SubmissionBusinessEntityRole.GENERAL_CONTRACTOR,
        business_id=uuid.uuid4(),
    )
    sb2 = SubmissionBusiness(id=uuid.uuid4(), submission_id=submission.id, business_id=uuid.uuid4())
    db.session.add(sb1)
    db.session.add(sb2)
    db.session.commit()
    gc_id = str(sb1.business_id)
    other_id = str(sb2.business_id)
    flask.current_app.facts_client.count_facts.return_value = [
        AnonObj(parent_id=gc_id, count=20),
        AnonObj(parent_id=other_id, count=21),
    ]

    assert not _check_entity_with_most_facts(submission).success

    g.pop("facts_counts_first_party")
    flask.current_app.facts_client.count_facts.return_value = [
        AnonObj(parent_id=gc_id, count=21),
        AnonObj(parent_id=other_id, count=20),
    ]
    assert _check_entity_with_most_facts(submission).success

    g.pop("facts_counts_first_party")
    flask.current_app.facts_client.count_facts.return_value = [
        AnonObj(parent_id=gc_id, count=20),
        AnonObj(parent_id=other_id, count=20),
    ]
    assert _check_entity_with_most_facts(submission).success


def test_check_data_from_supplementals(app_context, request_context, mocker):
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": True})
    flask.current_app.facts_client = MagicMock(spec=FactsClient)

    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture(mode=SubmissionMode.GC_PROJECT)
    sb1 = SubmissionBusiness(
        id=uuid.uuid4(),
        submission_id=submission.id,
        entity_role=SubmissionBusinessEntityRole.GENERAL_CONTRACTOR,
        business_id=uuid.uuid4(),
    )
    db.session.add(sb1)
    db.session.commit()
    gc_id = str(sb1.business_id)

    flask.current_app.facts_client.count_facts.return_value = [
        AnonObj(parent_id=gc_id, count=20, fact_subtype_ids=REQUIRED_FACTS_GC),
    ]
    assert _check_data_from_supplementals(submission).success

    g.pop("facts_counts")
    flask.current_app.facts_client.count_facts.return_value = [
        AnonObj(parent_id=gc_id, count=20, fact_subtype_ids=REQUIRED_FACTS_GC.copy().pop(0)),
    ]
    assert not _check_data_from_supplementals(submission).success

    sb2 = SubmissionBusiness(
        id=uuid.uuid4(),
        submission_id=submission.id,
        entity_role=SubmissionBusinessEntityRole.PROJECT,
        business_id=uuid.uuid4(),
    )
    db.session.add(sb2)
    db.session.commit()
    project_id = str(sb2.business_id)

    assert _check_data_from_supplementals(submission).success


def test_has_property_information(app_context, request_context, mocker):
    mocker.patch.object(os, "environ", {"IS_TEST_ENV": True, "INTERACT_WITH_EXTERNAL_RESOURCES": True})
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, is_being_impersonated=False, email="<EMAIL>"),
    )
    flask.current_app.facts_client = MagicMock(spec=FactsClient)

    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture(mode=SubmissionMode.GC_PROJECT)
    sb1 = SubmissionBusiness(id=uuid.uuid4(), submission_id=submission.id, business_id=uuid.uuid4())
    coverage = coverage_fixture(name="property")
    submission.coverages = [submission_coverage_fixture(submission_id=submission.id, coverage_id=coverage.id)]
    db.session.add(sb1)
    db.session.commit()

    mocker.patch("flask.current_app.ers_client_v3.get_entities", return_value={})
    flask.current_app.facts_client.count_facts.return_value = []
    assert not _has_property_information(submission).success

    g.pop("premises_structures_counts")
    flask.current_app.facts_client.count_facts.return_value = [
        AnonObj(parent_id=uuid.uuid4(), count=20, fact_subtype_ids=[REQUIRED_PROPERTY_FACTS[0]]),
        AnonObj(parent_id=uuid.uuid4(), count=20, fact_subtype_ids=[REQUIRED_PROPERTY_FACTS[1]]),
    ]
    assert _has_property_information(submission).success


def test_hard_check_only_support(app_context, request_context, setup_common_mocks, mocker):
    internal = AnonObj(
        is_internal_machine_user=True,
        has_submission_permission=lambda type, id: True,
        is_cs_manager_or_internal_machine_user=True,
    )
    non_manager = AnonObj(
        is_internal_machine_user=False,
        has_submission_permission=lambda type, id: True,
        is_cs_manager_or_internal_machine_user=False,
        is_tier_2_or_internal_machine_user=False,
        is_being_impersonated=False,
        email="<EMAIL>",
    )
    mocker.patch("flask_login.utils._get_user", return_value=non_manager)

    organization_fixture()
    user_fixture()
    coverage = coverage_fixture()
    report = report_fixture()
    submission = submission_fixture(report=report)
    submission_business_fixture(
        submission_id=submission.id,
        business_id=uuid.uuid4(),
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    submission.primary_naics_code = "NAICS_111110"
    submission.coverages = [submission_coverage_fixture(submission_id=submission.id, coverage_id=coverage.id)]
    db.session.flush()
    db.session.commit()

    with pytest.raises(UnprocessableEntity):
        verify({}, str(submission.id), manual=True, force_verify=False, force=True)
    assert not submission.is_manual_verified
    assert not submission.is_auto_verified

    mocker.patch("flask_login.utils._get_user", return_value=internal)
    verify({}, str(submission.id), manual=False, force_verify=True, force=False)
    assert not submission.stuck_reason
    assert submission.is_auto_verified


def test_has_limit_and_attachment_point_for_excess(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    _, sub = report_and_submission_fixture()
    coverage = coverage_fixture(organization_id=1, display_name="Spiderman")
    sub_covergae = submission_coverage_fixture(
        coverage_type=CoverageType.EXCESS, submission_id=sub.id, coverage_id=coverage.id
    )

    db.session.commit()

    assert _has_limit_and_attachment_point_for_excess(Submission.query.get(sub.id)).success is False
    assert (
        _has_limit_and_attachment_point_for_excess(Submission.query.get(sub.id)).error_message
        == "One of excess coverages (Spiderman) does not specify limit or attachment point"
    )

    sub_covergae.attachment_point = 100
    sub_covergae.limit = 10
    db.session.commit()

    assert _has_limit_and_attachment_point_for_excess(Submission.query.get(sub.id)).success is True


def test_check_there_is_no_loss_more_than_5_million(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    _, sub = report_and_submission_fixture()
    db.session.commit()
    loss = loss_fixture(submission_id=sub.id, sum_of_total_net_incurred=25000001)
    db.session.commit()

    assert _check_there_is_no_loss_more_than_25_million(Submission.query.get(sub.id)).success is False

    loss.sum_of_total_net_incurred = 25000000
    db.session.commit()

    assert _check_there_is_no_loss_more_than_25_million(Submission.query.get(sub.id)).success is True


def test_check_fact_over_threshold(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    _, sub = report_and_submission_fixture(processing_state=SubmissionProcessingState.COMPLETED)
    db.session.commit()
    fact_subtype_id = FactSubtypeID.TOTAL_SALES
    threshold = 5000
    loaded_data = {
        "files": [],
        "fields": [
            {
                "name": "Building",
                "unit": "USD",
                "values": [
                    {"value": "1669500", "file_idx": 1, "entity_idx": 0},
                    {"value": "1344000", "file_idx": 1, "entity_idx": 1},
                ],
                "value_type": "INTEGER",
                "fact_subtype_id": "BUILDING_VALUE",
            },
            {
                "name": "Total Sales",
                "unit": "USD",
                "values": [
                    {"value": "2380", "file_idx": 1, "entity_idx": 0},
                    {"value": "229.1", "file_idx": 1, "entity_idx": 1},
                    {
                        "value": (
                            f'{{"units":"USD","interval":"ANNUAL","times":["2022-12-31T00:00:00"],"values":[{threshold + 1}]}}'
                        ),
                        "entity_idx": 0,
                    },
                ],
                "value_type": "INTEGER",
                "fact_subtype_id": fact_subtype_id.value,
            },
        ],
        "entities": [],
        "entity_information": [],
    }
    sfd = submission_files_data_fixture(submission_id=sub.id, loaded_data=loaded_data)
    db.session.commit()

    assert _check_fact_over_threshold(fact_subtype_id, threshold, Submission.query.get(sub.id)).success is False

    loaded_data["fields"][1]["values"][2][
        "value"
    ] = f'{{"units":"USD","interval":"ANNUAL","times":["2022-12-31T00:00:00"],"values":[{threshold - 1}]}}'
    sfd.loaded_data = loaded_data
    db.session.commit()

    assert _check_fact_over_threshold(fact_subtype_id, threshold, Submission.query.get(sub.id)).success is True


def test_check_fact_over_threshold_invalid_data(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    _, sub = report_and_submission_fixture(processing_state=SubmissionProcessingState.COMPLETED)
    db.session.commit()
    fact_subtype_id = FactSubtypeID.TOTAL_SALES
    threshold = 5000
    loaded_data = {
        "files": [
            "ea2ae8db-4ea3-4483-9425-7c821c1adff7",
        ],
        "fields": [
            {
                "name": "Total Sales",
                "unit": "USD",
                "values": [
                    {"value": "bruh", "file_idx": None, "entity_idx": None},
                    {"value": "czerwone korale", "file_idx": None, "entity_idx": None},
                    {
                        "value": (
                            f'{{"units":"USD","interval":"ANNUAL","times":["2022-12-31T00:00:00"],"values":["bruh"]}}'
                        ),
                        "entity_idx": None,
                    },
                ],
                "value_type": "INTEGER",
                "fact_subtype_id": fact_subtype_id.value,
            },
        ],
        "entities": [
            {"type": "Primary Insured", "id": "meskel & associates engineering, pllc, "},
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [{"value": "Meskel & Associates Engineering, PLLC", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
        ],
    }
    sfd = submission_files_data_fixture(submission_id=sub.id, loaded_data=loaded_data)
    db.session.commit()
    response = _check_fact_over_threshold(fact_subtype_id, threshold, Submission.query.get(sub.id))

    assert response.success is False
    assert "Invalid value was found; value" in response.error_message


def test_check_has_at_least_one_fni(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    _, sub = report_and_submission_fixture()
    submission_business_fixture(
        submission_id=sub.id,
        business_id=uuid.uuid4(),
        named_insured=SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED,
    )
    db.session.commit()

    # Fail when no first named insured
    assert _check_has_at_least_one_fni(Submission.query.get(sub.id)).success is False

    # Add one and we should succeed
    submission_business_fixture(
        submission_id=sub.id,
        business_id=uuid.uuid4(),
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    db.session.commit()
    assert _check_has_at_least_one_fni(Submission.query.get(sub.id)).success


def test_check_has_more_than_one_fni(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    _, sub = report_and_submission_fixture()
    submission_business_fixture(
        submission_id=sub.id,
        business_id=uuid.uuid4(),
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    db.session.commit()

    # Succeed when have only one first named insured
    assert _check_has_more_than_one_fni(Submission.query.get(sub.id)).success

    # Add one more and we should fail
    submission_business_fixture(
        submission_id=sub.id,
        business_id=uuid.uuid4(),
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    db.session.commit()
    assert _check_has_more_than_one_fni(Submission.query.get(sub.id)).success is False


def test_verify_pre_renewal_report(app_context, request_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], False, False, []))

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED, is_pre_renewal=True
    )

    db.session.commit()

    verify({}, str(submission.id), force_verify=True, manual=False, force=False)
    verify({}, str(submission.id), force_verify=True, manual=False, force=False)
    db.session.commit()

    relation = SubmissionRelation.query.filter(
        SubmissionRelation.to_submission_id == submission.id,
        SubmissionRelation.type == SubmissionRelationType.RENEWAL,
    ).all()

    assert len(relation) == 1

    renewal_shell = Submission.query.get(relation[0].from_submission_id)

    assert renewal_shell.is_verified_shell
    assert renewal_shell.is_renewal_shell
    assert renewal_shell.is_renewal
    assert submission.stage == SubmissionStage.QUOTED_BOUND


def test_check_either_property_description_or_occupancy(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    mocker.patch(
        "copilot.models.Organization.is_aru_for_id",
        return_value=True,
    )
    _, sub = report_and_submission_fixture()
    db.session.commit()
    onboarded_data = {
        "files": [],
        "fields": [
            {
                "name": "Building",
                "unit": "USD",
                "values": [
                    {"value": "1669500", "file_idx": 1, "entity_idx": 0},
                    {"value": "1344000", "file_idx": 1, "entity_idx": 1},
                ],
                "value_type": "INTEGER",
                "fact_subtype_id": "BUILDING_VALUE",
            },
            {
                "name": "Total Sales",
                "unit": "USD",
                "values": [
                    {"value": "2380", "file_idx": 1, "entity_idx": 0},
                    {"value": "229.1", "file_idx": 1, "entity_idx": 1},
                ],
                "value_type": "INTEGER",
            },
        ],
        "entities": [],
        "entity_information": [],
    }
    sfd = submission_files_data_fixture(submission_id=sub.id, onboarded_data=onboarded_data)
    db.session.commit()

    assert _check_aru_property_description_or_occupancy(Submission.query.get(sub.id)).success is False

    onboarded_data["fields"].append(
        {
            "name": "Description",
            "values": [
                {"value": "Some description", "file_idx": 1, "entity_idx": 0},
                {"value": "Some other description", "file_idx": 1, "entity_idx": 1},
            ],
            "value_type": "TEXT",
            "fact_subtype_id": "PROPERTY_DESCRIPTION",
        }
    )
    sfd.onboarded_data = onboarded_data
    db.session.commit()

    assert _check_aru_property_description_or_occupancy(Submission.query.get(sub.id)).success


def test_handle_org_group_split(app_context, mocker, setup_common_mocks):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], True, False, []))
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))

    # Setup organization and user for BowheadSpecialty
    bowhead_id = ExistingOrganizations.BowheadSpecialty.value
    organization = organization_fixture(id=bowhead_id)
    user = user_fixture(organization_id=bowhead_id)
    settings = settings_fixture(
        organization_id=bowhead_id,
        org_groups=[
            OrganizationGroups.BOWHEAD_PRIMARY.value,
            OrganizationGroups.BOWHEAD_XS.value,
            OrganizationGroups.BOWHEAD_ENVIRONMENTAL.value,
        ],
    )
    coverage_fixture(
        name=Coverage.ExistingNames.Liability,
        coverage_types=[CoverageType.EXCESS, CoverageType.PRIMARY],
        organization_id=organization.id,
    )
    coverage_fixture(
        name=Coverage.ExistingNames.professionalLiability,
        coverage_types=[CoverageType.EXCESS, CoverageType.PRIMARY],
        organization_id=organization.id,
    )
    db.session.commit()

    # Create email correspondence using fixture
    correspondence = report_email_correspondence_fixture(email_account="<EMAIL>")
    correspondence_id = correspondence.id
    email = email_fixture(
        correspondence_id=correspondence_id,
        email_body="Test email body",
        email_to="<EMAIL>",
        email_cc=["<EMAIL>", "<EMAIL>"],
    )

    # Create report and submission for BowheadSpecialty
    report = report_fixture(
        org_group=OrganizationGroups.BOWHEAD_PRIMARY.value,
        correspondence_id=correspondence_id,
        organization_id=bowhead_id,
        owner_id=user.id,
        email_body="Test email body",
    )
    submission = submission_fixture(
        report=report,
        organization_id=bowhead_id,
        is_verified=False,
        origin="EMAIL",
        processing_state=SubmissionProcessingState.COMPLETED,
        owner_id=user.id,
        is_auto_processed=True,  # Set the submission as auto processed to pass the dependency check
    )

    # Add files to the submission with valid s3_keys
    file1 = file_fixture(
        submission_id=submission.id,
        name="test_file_1",
        s3_key="uploads/files/test_file_1.pdf",
        classification=ClassificationDocumentType.EMAIL,
        file_type=FileType.EMAIL,
    )
    file2 = file_fixture(submission_id=submission.id, name="test_file_2", s3_key="uploads/files/test_file_2.pdf")

    db.session.commit()

    verify({}, str(submission.id), manual=False, force=False)
    verify({}, str(submission.id), manual=False, force=False, force_verify=True)

    # Check that two new reports were created
    new_reports = ReportV2.query.filter(
        ReportV2.organization_id == bowhead_id,
        ReportV2.id != report.id,
        ReportV2.correspondence_id == correspondence_id,
    ).all()

    # Assertions
    assert len(new_reports) == 2

    # Verify that the org groups were assigned correctly
    expected_org_groups = {OrganizationGroups.BOWHEAD_XS.value, OrganizationGroups.BOWHEAD_ENVIRONMENTAL.value}
    assert {r.org_group for r in new_reports} == expected_org_groups
    # Verify coverages were assigned
    xs_report = [r for r in new_reports if r.org_group == OrganizationGroups.BOWHEAD_XS.value][0]
    env_report = [r for r in new_reports if r.org_group == OrganizationGroups.BOWHEAD_ENVIRONMENTAL.value][0]
    assert xs_report.submission.coverages[0].coverage_type == CoverageType.EXCESS
    assert env_report.submission.coverages == []

    # Verify that files were copied to the new submissions
    for new_report in new_reports:
        assert new_report.processing_depends_on_report.report_id == report.id
        new_submission = new_report.submissions[0] if new_report.submissions else None
        assert new_submission is not None
        new_submission_files = File.query.filter_by(submission_id=new_submission.id).all()
        assert len(new_submission_files) == 2  # Should have copied both files

        # Check that the file processing states are correct
        for new_file in new_submission_files:
            expected_state = (
                FileProcessingState.NOT_CLASSIFIED
                if new_file.classification is None
                else FileProcessingState.CLASSIFIED
            )
            assert new_file.processing_state == expected_state


def _run_secura_verification_test(mocker, mapping_id, expect_notification: bool):
    mocker.patch("copilot.v3.controllers.submission_verification.verify_checks", return_value=([], True, False, []))
    mocker.patch(
        "copilot.v3.controllers.submission_verification.SecuraService.find_kalepa_mapping",
        return_value=SecuraUWMapping(mapped_kalepa_user_id=mapping_id),
    )

    secura_org_id = ExistingOrganizations.SECURA.value
    organization_fixture(id=secura_org_id)
    user = user_fixture(email="<EMAIL>", organization_id=secura_org_id)
    report = report_fixture(organization_id=secura_org_id)
    submission = submission_fixture(report=report)
    submission_user_fixture(user.id, submission.id)
    db.session.flush()
    db.session.commit()

    assert submission.active_submission_processing is None

    submission.is_auto_processed = True
    submission.processing_state = SubmissionProcessingState.COMPLETED
    db.session.commit()

    # Manual -> Auto -> final full verification
    verify({}, str(submission.id), manual=True, force=False)
    verify({}, str(submission.id), manual=False, force=False)
    verify({}, str(submission.id), manual=False, force=False, force_verify=True)

    submission = Submission.query.get(submission.id)
    assert submission.is_auto_verified
    assert submission.is_verified
    assert submission.is_manual_verified
    assert submission.active_submission_processing is None
    submission_processing = SubmissionProcessing.query.filter_by(submission_id=submission.id).first()
    assert submission_processing.is_verified
    __verify_one_submission_history_record_added(submission.id, SubmissionActionType.PDS_AUTO_VERIFICATION_COMPLETED)
    __verify_one_submission_history_record_added(submission.id, SubmissionActionType.PDS_VERIFICATION_COMPLETED)
    __verify_one_submission_history_record_added(submission.id, SubmissionActionType.PDS_MANUAL_VERIFICATION_COMPLETED)
    report = ReportV2.query.get(report.id)
    assert report.organization_permission_level == PermissionType.EDITOR

    knock_client = flask.current_app.knock_client
    if expect_notification:
        knock_client.maybe_publish_report_shared_to_recipient.assert_called()
    else:
        knock_client.maybe_publish_report_shared_to_recipient.assert_not_called()


def test_secura_verify_for_pilot_uw(app_context, request_context, mocker, setup_common_mocks):
    pilot_uw_id = list(SECURA_PILOT_UWS.keys())[0]  # one of pilot UWs
    _run_secura_verification_test(mocker, mapping_id=pilot_uw_id, expect_notification=True)


def test_secura_verify_for_non_pilot_uw(app_context, request_context, mocker, setup_common_mocks):
    non_pilot_uw_id = 1  # Not in SECURA_PILOT_UWS
    _run_secura_verification_test(mocker, mapping_id=non_pilot_uw_id, expect_notification=False)
