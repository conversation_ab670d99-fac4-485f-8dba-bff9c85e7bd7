from http import HTTPStatus
from uuid import uuid4
import json

from llm_common.models.llm_model import LLMModel
from more_itertools import flatten
from static_common.enums.classification import ExtractionType, InputProcessingType
from werkzeug.exceptions import NotFound
import pytest

from copilot.models import (
    ClassifierConfig,
    ClassifierToConfigVersion,
    CustomizableClassifierV2,
    TaskModel,
)
from copilot.models._private import db
from copilot.schemas.customizable_classifiers import (
    ClassifierConfigSchema,
    CustomizableClassifierV2Schema,
)
from copilot.v3.controllers.customizable_classifiers import (
    change_classifier_version_active,
    create_customizable_classifier_v2,
    delete_classifier_version,
    delete_customizable_classifier_v2,
    get_customizable_classifier_v2_by_id,
    get_customizable_classifiers_v2,
    update_customizable_classifier_v2,
)
from tests.integration.coverages.fixtures import set_up_trusted_user
from tests.integration.factories import (
    classifier_config_fixture,
    classifier_filter_rule_fixture,
    classifier_phrase_fixture,
    classifier_to_config_version_fixture,
    classifier_version_fixture,
    customizable_classifier_v2_fixture,
    llm_config_version_fixture,
    phrases_config_version_fixture,
    task_model_fixture,
)

customizable_classifier_schema = CustomizableClassifierV2Schema()


@pytest.fixture
def setup_classifiers(app_context):
    # Create classifier for document input type
    classifier = customizable_classifier_v2_fixture(
        name="Test Document Classifier",
        fact_subtype_id="test_document",
        input_types=["document"],
        organization_ids=[1],
        run_in_pds=True,
        is_internal=False,
    )

    # Create version 1 (inactive)
    version1 = classifier_version_fixture(
        classifier_id=classifier.id, name="v1", classifier_description="First version", is_active=False
    )

    # Create version 2 (active)
    version2 = classifier_version_fixture(
        classifier_id=classifier.id, name="v2", classifier_description="Second version", is_active=True
    )

    # Create filter rules
    filter_rule = classifier_filter_rule_fixture(classifier_id=classifier.id)

    # Create config for document input type
    config = classifier_config_fixture(input_types=["document"])

    # Create phrases config version for v1
    phrases_config_v1 = phrases_config_version_fixture(
        classifier_config_id=config.id, input_processing_type=InputProcessingType.OCR_TEXT
    )

    # Create phrase for v1
    phrase_v1 = classifier_phrase_fixture(
        classifier_config_version_id=phrases_config_v1.id, phrase="Test phrase v1", weight=0.8
    )

    # Link phrases config to version 1
    classifier_to_config_version_fixture(
        classifier_version_id=version1.id,
        classifier_config_id=config.id,
        classifier_config_version_id=phrases_config_v1.id,
    )

    # Create LLM config version for v2
    llm_config_v2 = llm_config_version_fixture(
        classifier_config_id=config.id,
        input_processing_type=InputProcessingType.OCR_TEXT,
        llm_model="gpt-4",
        prompt="Test prompt v2",
    )

    # Link LLM config to version 2
    classifier_to_config_version_fixture(
        classifier_version_id=version2.id, classifier_config_id=config.id, classifier_config_version_id=llm_config_v2.id
    )

    # Create classifier for text input with multiple configs
    text_classifier = customizable_classifier_v2_fixture(
        name="Test Text Classifier",
        fact_subtype_id="test_text",
        input_types=["text", "document"],
        organization_ids=[1, 2],
        run_in_pds=True,
        is_internal=True,
    )

    # Create active version for text classifier
    text_version = classifier_version_fixture(
        classifier_id=text_classifier.id, name="v1", classifier_description="Text classifier version", is_active=True
    )

    # Create text config
    text_config = classifier_config_fixture(input_types=["text"])

    # Create document config for text classifier
    doc_config = classifier_config_fixture(input_types=["document"])

    # Create phrases config version for text
    text_phrases_config = phrases_config_version_fixture(
        classifier_config_id=text_config.id, input_processing_type=InputProcessingType.OCR_TEXT
    )

    # Create phrase for text config
    text_phrase = classifier_phrase_fixture(
        classifier_config_version_id=text_phrases_config.id, phrase="Test phrase for text", weight=0.9
    )

    # Create LLM config version for document config
    doc_llm_config = llm_config_version_fixture(
        classifier_config_id=doc_config.id,
        input_processing_type=InputProcessingType.OCR_TEXT,
        llm_model="gpt-3.5",
        prompt="Test prompt for document",
    )

    # Link configs to text classifier version
    classifier_to_config_version_fixture(
        classifier_version_id=text_version.id,
        classifier_config_id=text_config.id,
        classifier_config_version_id=text_phrases_config.id,
    )

    classifier_to_config_version_fixture(
        classifier_version_id=text_version.id,
        classifier_config_id=doc_config.id,
        classifier_config_version_id=doc_llm_config.id,
    )

    # Commit all changes
    db.session.commit()

    return {"document_classifier": classifier, "text_classifier": text_classifier}


@pytest.fixture
def consolidation_task_model(app_context) -> TaskModel:
    model = task_model_fixture(
        id="148840f2-1bef-4bc5-8bbe-4dfb0fcbdf07",
        name="Consolidate Classifiers (GPT 4.1)",
        execution_config="""{"handler_class": "ClassifiersConsolidationHandler"}""",
        execution_type="LAMBDA",
        processing_type="CONSOLIDATION",
        llm_model=LLMModel.OPENAI_GPT_4_1.value,
        use_task_output_processor=True,
    )
    db.session.commit()
    return model


def test_get_classifiers_by_input_type_document(setup_classifiers, set_up_trusted_user):
    # Get classifiers for document input type and organization 1
    classifiers, status = get_customizable_classifiers_v2(1, ["document"], active_only=True)

    # Assert basic response properties
    assert status == HTTPStatus.OK
    assert isinstance(classifiers, list)

    # Check that we get both classifiers (document and text)
    classifier_names = [c["name"] for c in classifiers]
    assert "Test Document Classifier" in classifier_names
    assert "Test Text Classifier" in classifier_names

    # Check classifier details
    doc_classifier = next(c for c in classifiers if c["name"] == "Test Document Classifier")
    assert doc_classifier["fact_subtype_id"] == "test_document"
    assert "document" in doc_classifier["input_types"]
    assert 1 in doc_classifier["organization_ids"]
    assert doc_classifier["run_in_pds"] is True

    # Check active version details
    assert doc_classifier["active_version"]["name"] == "v2"
    assert doc_classifier["active_version"]["is_active"] is True

    # Check config details
    assert len(doc_classifier["active_version"]["configs"]) == 1
    config = doc_classifier["active_version"]["configs"][0]
    assert config["input_types"] == ["document"]

    # Check config version details
    assert len(config["versions"]) == 1
    config_version = config["versions"][0]
    assert config_version["extraction_type"] == ExtractionType.LLM
    assert config_version["llm_model"] == "gpt-4"
    assert config_version["prompt"] == "Test prompt v2"


def test_get_classifiers_by_input_type_text(setup_classifiers, set_up_trusted_user):
    # Get classifiers for text input type and organization 1
    classifiers, status = get_customizable_classifiers_v2(1, ["text"], active_only=True)

    # Assert basic response properties
    assert status == HTTPStatus.OK
    assert isinstance(classifiers, list)

    # Should only get the text classifier
    assert len(classifiers) == 1
    assert classifiers[0]["name"] == "Test Text Classifier"

    # Check classifier details
    text_classifier = classifiers[0]
    assert text_classifier["fact_subtype_id"] == "test_text"
    assert "text" in text_classifier["input_types"]
    assert "document" in text_classifier["input_types"]
    assert 1 in text_classifier["organization_ids"]
    assert 2 in text_classifier["organization_ids"]

    # Check active version details
    assert text_classifier["active_version"]["name"] == "v1"
    assert text_classifier["active_version"]["is_active"] is True

    # Check configs
    assert len(text_classifier["active_version"]["configs"]) == 1
    config = text_classifier["active_version"]["configs"][0]
    assert config["input_types"] == ["text"]

    # Check config version
    assert len(config["versions"]) == 1
    config_version = config["versions"][0]
    assert config_version["extraction_type"] == ExtractionType.PHRASES
    assert len(config_version["phrases"]) == 1
    assert config_version["phrases"][0]["phrase"] == "Test phrase for text"
    assert config_version["phrases"][0]["weight"] == 0.9


def test_get_classifiers_different_organization(setup_classifiers, set_up_trusted_user):
    # Get classifiers for organization 3 (should return just the internal one)
    classifiers, status = get_customizable_classifiers_v2(3, ["document"], active_only=True)

    assert status == HTTPStatus.OK
    assert len(classifiers) == 1
    assert classifiers[0]["name"] == "Test Text Classifier"
    assert classifiers[0]["is_internal"] is True


def test_get_classifiers_pds_only(setup_classifiers, set_up_trusted_user):
    # Get classifiers for pds_only=True (both should be included)
    classifiers, status = get_customizable_classifiers_v2(1, ["document"], pds_only=True, active_only=True)

    assert status == HTTPStatus.OK
    assert len(classifiers) == 2

    # Create non-PDS classifier
    non_pds = customizable_classifier_v2_fixture(
        name="Non-PDS Classifier",
        fact_subtype_id="non_pds",
        input_types=["document"],
        organization_ids=[1],
        run_in_pds=False,
        is_internal=False,
    )

    version = classifier_version_fixture(
        classifier_id=non_pds.id, name="v1", classifier_description="Non-PDS version", is_active=True
    )

    db.session.commit()

    # Get classifiers again - non-PDS one should be filtered out
    classifiers, status = get_customizable_classifiers_v2(1, ["document"], pds_only=True, active_only=True)

    classifier_names = [c["name"] for c in classifiers]
    assert "Non-PDS Classifier" not in classifier_names


def test_active_versions_relationship(setup_classifiers, app_context):
    """Test that the active_versions relationship works correctly"""

    # Get the document config for the first classifier
    version_mapping = ClassifierToConfigVersion.query.filter_by(
        classifier_version_id=setup_classifiers["document_classifier"].active_version.id
    ).first()
    doc_config = ClassifierConfig.query.filter_by(id=version_mapping.classifier_config_id).first()

    # It should have one active version (the LLM config version linked to v2)
    assert len(doc_config.active_versions) == 1
    active_version = doc_config.active_versions[0]
    assert active_version.extraction_type == ExtractionType.LLM

    # Create another version for this classifier that is not active

    inactive_version = classifier_version_fixture(
        classifier_id=setup_classifiers["document_classifier"].id,
        name="v3",
        classifier_description="Third version",
        is_active=False,
    )

    # Create a new config version
    another_config = phrases_config_version_fixture(
        classifier_config_id=doc_config.id, input_processing_type=InputProcessingType.OCR_TEXT
    )

    # Link it to the inactive version
    ClassifierToConfigVersion(
        classifier_version_id=inactive_version.id,
        classifier_config_id=doc_config.id,
        classifier_config_version_id=another_config.id,
    )

    db.session.commit()

    # Refresh the config from DB
    db.session.refresh(doc_config)

    # It should still have only one active version
    assert len(doc_config.active_versions) == 1
    assert doc_config.active_versions[0].extraction_type == ExtractionType.LLM

    # But it should have multiple versions total
    assert len(doc_config.versions) == 3

    # Test the schema reflects this correctly
    config_schema = ClassifierConfigSchema().dump(doc_config)
    assert len(config_schema["active_versions"]) == 1
    assert config_schema["active_versions"][0]["extraction_type"] == ExtractionType.LLM

    # The versions field should include both active an  d inactive versions
    expected_versions_count = 3
    assert len(config_schema["versions"]) == expected_versions_count


def test_delete_classifier_endpoint(app_context, setup_classifiers, set_up_trusted_user):
    classifier = setup_classifiers["document_classifier"]

    db_classifier = db.session.query(CustomizableClassifierV2).get(classifier.id)
    # 3 configs - 1 for first classifier, 2 for second classifier
    assert db.session.query(ClassifierConfig).count() == 3
    assert db_classifier is not None

    delete_customizable_classifier_v2(classifier.id)

    db_classifier = db.session.query(CustomizableClassifierV2).get(classifier.id)
    assert db_classifier is None
    # config for the second classifier should still exist
    assert db.session.query(ClassifierConfig).count() == 2


def test_create_classifier_endpoint(app_context, mocker, set_up_trusted_user, consolidation_task_model):
    classifier_id = uuid4()
    request = {
        "id": classifier_id,
        "name": "Test Document Classifier",
        "fact_subtype_id": "test_document",
        "extracted_value_name": None,
        "output_type": "BOOLEAN",
        "output_unit": None,
        "input_types": ["document"],
        "organization_ids": [1],
        "run_in_pds": True,
        "is_internal": False,
        "versions": [
            {
                "name": "v1",
                "comment": None,
                "classifier_description": "First version",
                "is_active": False,
                "configs": [
                    {
                        "input_types": ["document"],
                        "versions": [
                            {
                                "input_processing_type": "OCR_TEXT",
                                "extraction_type": "PHRASES",
                                "is_autogenerated": False,
                                "task_model_id": None,
                                "phrases": [
                                    {
                                        "phrase": "Test phrase v1",
                                        "weight": 0.8,
                                        "excludes": [],
                                    }
                                ],
                            },
                            {
                                "input_processing_type": "OCR_TEXT",
                                "extraction_type": "LLM",
                                "is_autogenerated": False,
                                "task_model_id": None,
                                "llm_model": "gpt-4",
                                "prompt": "Test prompt v2",
                            },
                        ],
                        "active_versions": [
                            {
                                "input_processing_type": "OCR_TEXT",
                                "extraction_type": "LLM",
                                "is_autogenerated": False,
                                "task_model_id": None,
                                "llm_model": "gpt-4",
                                "prompt": "Test prompt v2",
                            }
                        ],
                    }
                ],
                "classifier_task_definitions": [],
            },
            {
                "name": "v2",
                "comment": None,
                "classifier_description": "Second version",
                "is_active": True,
                "configs": [
                    {
                        "input_types": ["document"],
                        "versions": [
                            {
                                "input_processing_type": "OCR_TEXT",
                                "extraction_type": "PHRASES",
                                "is_autogenerated": False,
                                "task_model_id": None,
                                "phrases": [
                                    {
                                        "phrase": "Test phrase v1",
                                        "weight": 0.8,
                                        "excludes": [],
                                    }
                                ],
                            },
                            {
                                "input_processing_type": "OCR_TEXT",
                                "extraction_type": "LLM",
                                "is_autogenerated": False,
                                "task_model_id": None,
                                "llm_model": "gpt-4",
                                "prompt": "Test prompt v2",
                            },
                        ],
                        "active_versions": [
                            {
                                "input_processing_type": "OCR_TEXT",
                                "extraction_type": "LLM",
                                "is_autogenerated": False,
                                "task_model_id": None,
                                "llm_model": "gpt-4",
                                "prompt": "Test prompt v2",
                            }
                        ],
                    }
                ],
                "classifier_task_definitions": [],
            },
        ],
        "active_version": {
            "name": "v2",
            "comment": None,
            "classifier_description": "Second version",
            "is_active": True,
            "configs": [
                {
                    "input_types": ["document"],
                    "versions": [
                        {
                            "input_processing_type": "OCR_TEXT",
                            "extraction_type": "PHRASES",
                            "is_autogenerated": False,
                            "task_model_id": None,
                            "phrases": [
                                {
                                    "phrase": "Test phrase v1",
                                    "weight": 0.8,
                                    "excludes": [],
                                }
                            ],
                        },
                        {
                            "input_processing_type": "OCR_TEXT",
                            "extraction_type": "LLM",
                            "is_autogenerated": False,
                            "task_model_id": None,
                            "llm_model": "gpt-4",
                            "prompt": "Test prompt v2",
                        },
                    ],
                    "active_versions": [
                        {
                            "input_processing_type": "OCR_TEXT",
                            "extraction_type": "LLM",
                            "is_autogenerated": False,
                            "task_model_id": None,
                            "llm_model": "gpt-4",
                            "prompt": "Test prompt v2",
                        }
                    ],
                }
            ],
            "classifier_task_definitions": [],
        },
        "filter_rules": [
            {
                "filter_type": "NAICS",
                "negated": False,
                "values": ["2137"],
            }
        ],
    }

    assert db.session.query(CustomizableClassifierV2).get(classifier_id) is None

    with mocker.patch("copilot.v3.controllers.customizable_classifiers.configure_classifier"):
        create_customizable_classifier_v2(request)

    create_classifier = db.session.query(CustomizableClassifierV2).get(classifier_id)
    assert create_classifier is not None


def test_get_customizable_classifier_v2_by_id(app_context, setup_classifiers, set_up_trusted_user):
    classifier = setup_classifiers["document_classifier"]
    result_classifier, status = get_customizable_classifier_v2_by_id(classifier.id)

    assert status == HTTPStatus.OK
    assert result_classifier == customizable_classifier_schema.dump(classifier)

    with pytest.raises(NotFound):
        _, _ = get_customizable_classifier_v2_by_id(str(uuid4()))


def test_change_classifier_version_active(app_context, setup_classifiers, set_up_trusted_user):
    classifier = setup_classifiers["document_classifier"]

    v1 = next((v for v in classifier.versions if v.name == "v1"), None)
    v2 = next((v for v in classifier.versions if v.name == "v2"), None)

    assert v1
    assert v1.is_active == False
    assert v2
    assert v2.is_active == True
    assert classifier.active_version == v2

    change_classifier_version_active(classifier.id, v1.id, is_active=True)

    assert v1
    assert v1.is_active == True
    assert v2
    assert v2.is_active == False
    assert classifier.active_version == v1


def test_change_classifier_version_inactive(app_context, setup_classifiers, set_up_trusted_user):
    classifier = setup_classifiers["document_classifier"]

    v1 = next((v for v in classifier.versions if v.name == "v1"), None)
    v2 = next((v for v in classifier.versions if v.name == "v2"), None)

    assert v1
    assert v1.is_active == False
    assert v2
    assert v2.is_active == True
    assert classifier.active_version == v2

    change_classifier_version_active(classifier.id, v2.id, is_active=False)

    assert v1
    assert v1.is_active == False
    assert v2
    assert v2.is_active == False
    assert classifier.active_version is None


def test_delete_classifier_version(app_context, setup_classifiers, set_up_trusted_user):
    classifier = setup_classifiers["document_classifier"]

    # Get all versions
    active_version = next(v for v in classifier.versions if v.is_active)
    inactive_version = next(v for v in classifier.versions if not v.is_active)

    # Create a new inactive version to delete
    new_version = classifier_version_fixture(
        classifier_id=classifier.id,
        name="test_delete_version",
        classifier_description="Version to delete",
        is_active=False,
    )
    db.session.commit()

    # Get the total version count before deletion
    versions_before = len(classifier.versions)

    # Delete the inactive version
    response, status = delete_classifier_version(classifier.id, new_version.id)

    # Check response
    assert status == HTTPStatus.NO_CONTENT
    assert response is None

    # Refresh classifier from DB
    db.session.refresh(classifier)

    # Verify version was deleted
    assert len(classifier.versions) == versions_before - 1
    assert new_version.id not in [v.id for v in classifier.versions]

    delete_classifier_version(classifier.id, active_version.id)
    delete_classifier_version(classifier.id, inactive_version.id)

    assert CustomizableClassifierV2.query.filter_by(id=classifier.id).count() == 0


def test_update_classifier(app_context, mocker, setup_classifiers, set_up_trusted_user, consolidation_task_model):
    classifier = setup_classifiers["document_classifier"]
    assert classifier.run_in_pds is True
    assert classifier.input_types == ["document"]

    assert len(classifier.versions) == 2

    request = {
        "id": str(classifier.id),
        "name": "Test Document Classifier",
        "fact_subtype_id": "test_document",
        "extracted_value_name": None,
        "output_type": "BOOLEAN",
        "output_unit": None,
        "input_types": ["document", "document2", "document3"],
        "organization_ids": [1],
        "run_in_pds": False,
        "is_internal": False,
        "versions": [
            {
                "name": "v3",
                "comment": None,
                "classifier_description": "Third version",
                "is_active": False,
                "configs": [
                    {
                        "input_types": ["document"],
                        "versions": [
                            {
                                "input_processing_type": "OCR_TEXT",
                                "extraction_type": "PHRASES",
                                "is_autogenerated": False,
                                "task_model_id": None,
                                "phrases": [
                                    {
                                        "phrase": "Test phrase v1",
                                        "weight": 0.8,
                                        "excludes": [],
                                    }
                                ],
                            },
                            {
                                "input_processing_type": "OCR_TEXT",
                                "extraction_type": "LLM",
                                "is_autogenerated": False,
                                "task_model_id": None,
                                "llm_model": "gpt-4",
                                "prompt": "Test prompt v2",
                            },
                        ],
                    }
                ],
            },
        ],
    }

    with mocker.patch("copilot.v3.controllers.customizable_classifiers.configure_classifier"):
        update_customizable_classifier_v2(str(classifier.id), request)

    db.session.refresh(classifier)
    assert len(classifier.versions) == 3

    assert classifier.run_in_pds is False
    assert classifier.input_types == ["document", "document2", "document3"]

    new_version_added = next((v for v in classifier.versions if v.name == "v3"), None)
    assert len(new_version_added.configs) == 1
    assert new_version_added.classifier_task_definitions is not None
    config_versions = flatten([c.versions for c in new_version_added.configs])
    assert all(cv.task_model_id is not None for cv in config_versions)
