import time

from static_common.enums.origin import Origin

from copilot.models import db
from copilot.models.integration_logs import IntegrationLog
from copilot.v3.controllers.integrations import (
    bulk_get_integration_error_logs,
    create_new_integration_log,
    get_by_id,
    get_latest_integration_log_by_report_and_operation,
    update_integration_log_by_id,
)
from tests.integration.factories import (
    organization_fixture,
    report_and_submission_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj


def _setup_report(mocker):
    organization = organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture(origin=Origin.EMAIL)
    db.session.commit()

    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=user.id,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=lambda x, y: True,
            is_being_impersonated=False,
            organization_id=organization.id,
        ),
    )
    return report, submission, user


def _create_integration_log_and_assert(body):
    response = create_new_integration_log(body)
    assert response[1] == 201
    data = response[0]
    assert data["id"] is not None
    assert data["created_at"] is not None
    return data


def _init_db_and_prepare_base_body(mocker):
    report, submission, user = _setup_report(mocker)
    base_body = {
        "report_id": str(report.id),
        "submission_id": str(submission.id),
        "organization_id": report.organization_id,
        "executing_user_email": user.email,
        "operation": "ConiferFinysSubmissionCreate",
    }
    return base_body, report, user


def test_create_new_integration_log_with_success(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)

    body_updates = {
        "flow": "Egress",
        "status": "Success",
        "payload_json": {"key": "value"},
        "response_json": {"rkey": "rvalue"},
    }

    body = {**base_body, **body_updates}

    data = _create_integration_log_and_assert(body)
    assert data["payload_json_hash"] == "e43abcf3375244839c012f9633f95862d232a95b00d5bc7348b3098b9fed7f32"
    assert data["response_json_hash"] == "b42b9e4928b1657d5148c6696b0f48c84ab0f10f9789e19e8d1bb39e94d5656c"

    db_integration_log = db.session.query(IntegrationLog).get(data["id"])
    assert db_integration_log.report_id == report.id
    assert db_integration_log.submission_id == report.submission.id
    assert db_integration_log.organization_id == 1
    assert db_integration_log.flow == "Egress"
    assert db_integration_log.status == "Success"
    assert db_integration_log.operation == "ConiferFinysSubmissionCreate"
    assert db_integration_log.payload_json == {"key": "value"}
    assert db_integration_log.response_json == {"rkey": "rvalue"}
    assert db_integration_log.payload_json_hash == "e43abcf3375244839c012f9633f95862d232a95b00d5bc7348b3098b9fed7f32"
    assert db_integration_log.response_json_hash == "b42b9e4928b1657d5148c6696b0f48c84ab0f10f9789e19e8d1bb39e94d5656c"
    assert db_integration_log.error_details is None
    assert db_integration_log.additional_data is None
    assert db_integration_log.logical_identifier is None
    assert db_integration_log.attempts == 1
    assert db_integration_log.executing_user_email == "<EMAIL>"


def test_create_new_integration_log_with_error_and_more_details(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)

    body_updates = {
        "flow": "Ingress",
        "status": "Failure",
        "payload_json": {"key": "value"},
        "response_json": {"rkey": "rvalue"},
        "error_details": {"error": "details"},
        "additional_data": {"additional": "data"},
        "logical_identifier": "logical_id",
        "attempts": 3,
        "executing_user_email": "<EMAIL>",
    }

    body = {**base_body, **body_updates}

    data = _create_integration_log_and_assert(body)
    assert data["payload_json_hash"] == "e43abcf3375244839c012f9633f95862d232a95b00d5bc7348b3098b9fed7f32"
    assert data["response_json_hash"] == "b42b9e4928b1657d5148c6696b0f48c84ab0f10f9789e19e8d1bb39e94d5656c"

    db_integration_log = db.session.query(IntegrationLog).get(data["id"])
    assert db_integration_log.report_id == report.id
    assert db_integration_log.submission_id == report.submission.id
    assert db_integration_log.organization_id == 1
    assert db_integration_log.flow == "Ingress"
    assert db_integration_log.status == "Failure"
    assert db_integration_log.operation == "ConiferFinysSubmissionCreate"
    assert db_integration_log.payload_json == {"key": "value"}
    assert db_integration_log.response_json == {"rkey": "rvalue"}
    assert db_integration_log.payload_json_hash == "e43abcf3375244839c012f9633f95862d232a95b00d5bc7348b3098b9fed7f32"
    assert db_integration_log.response_json_hash == "b42b9e4928b1657d5148c6696b0f48c84ab0f10f9789e19e8d1bb39e94d5656c"
    assert db_integration_log.error_details == {"error": "details"}
    assert db_integration_log.additional_data == {"additional": "data"}
    assert db_integration_log.logical_identifier == "logical_id"
    assert db_integration_log.attempts == 3
    assert db_integration_log.executing_user_email == "<EMAIL>"


def test_get_latest_integration_log_success(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)
    operation_to_find = "ConiferFinysSubmissionCreate"
    initial_body = {
        **base_body,
        "operation": operation_to_find,
        "status": "Success",
        "flow": "Egress",
        "payload_json": {"request": 1},
        "response_json": {"result": "abc"},
    }
    created_log_data = _create_integration_log_and_assert(initial_body)

    response = get_latest_integration_log_by_report_and_operation(
        report_id=str(report.id), operation=operation_to_find  # Pass the string value
    )

    assert response[1] == 200
    retrieved_data = response[0]
    assert retrieved_data["id"] == created_log_data["id"]
    assert retrieved_data["created_at"] is not None
    assert retrieved_data["status"] == "Success"


def test_get_latest_integration_log_really_returns_latest(app_context, mocker):
    # 1. Setup: Create two logs for the same report/operation
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)
    operation_to_find = "ConiferFinysSubmissionCreate"

    # First log
    _create_integration_log_and_assert(
        {
            **base_body,
            "operation": operation_to_find,
            "status": "Pending",
            "flow": "Egress",
            "payload_json": {"step": 1},
        }
    )
    # Ensure timestamp difference if DB resolution is low
    time.sleep(0.01)

    # Second (latest) log
    latest_log_data = _create_integration_log_and_assert(
        {
            **base_body,
            "operation": operation_to_find,
            "status": "Success",
            "flow": "Egress",
            "payload_json": {"step": 2},
            "response_json": {"final": True},
        }
    )

    # 2. Action: Call the GET endpoint
    response = get_latest_integration_log_by_report_and_operation(report_id=str(report.id), operation=operation_to_find)

    # 3. Assertions
    assert response[1] == 200
    retrieved_data = response[0]
    # Crucially, check it's the ID of the *second* log created
    assert retrieved_data["id"] == latest_log_data["id"]
    assert retrieved_data["status"] == "Success"


def test_get_latest_integration_log_not_found_report(app_context, mocker):
    _init_db_and_prepare_base_body(mocker)
    non_existent_report_id = "00000000-0000-0000-0000-000000000000"

    response = get_latest_integration_log_by_report_and_operation(
        report_id=non_existent_report_id, operation="ConiferFinysSubmissionCreate"
    )

    assert response[1] == 404
    assert response[0] == {}


def test_update_integration_log_success(app_context, mocker):
    # 1. Setup: Create an initial log
    base_body, report, user = _init_db_and_prepare_base_body(mocker)
    initial_operation = "ConiferFinysSubmissionCreate"
    initial_body = {
        **base_body,
        "operation": initial_operation,
        "status": "Pending",
        "flow": "Egress",
        "attempts": 1,
        "payload_json": {"initial": "data"},
        "response_json": None,
        "error_details": None,
    }
    created_log_data = _create_integration_log_and_assert(initial_body)
    log_id_to_update = created_log_data["id"]

    # 2. Action: Define updates and call the PATCH endpoint
    update_body = {
        "status": "Success",  # Required field for update
        "response_json": {"result": "ok", "value": 123},
        "error_details": None,  # Explicitly setting to null if needed
        "attempts": 2,
        "executing_user_email": "<EMAIL>",  # Required field for update
        # Not updating: payload_json, logical_identifier, additional_data
    }
    response = update_integration_log_by_id(integration_log_id=log_id_to_update, body=update_body)

    # 3. Assertions (Response)
    assert response[1] == 200
    updated_response_data = response[0]
    assert updated_response_data["id"] == log_id_to_update
    assert updated_response_data["created_at"] is not None  # Should be original creation time
    # Hashes should reflect updated/existing jsons
    assert updated_response_data["payload_json_hash"] == created_log_data["payload_json_hash"]  # Unchanged
    assert (
        updated_response_data["response_json_hash"]
        == "1cf995782e4e28de00ab5013a8283dcf838d864f55b995be9a04fd59bd3120de"
    )

    # 4. Assertions (Database state)
    db.session.expire_all()  # Ensure we fetch fresh data
    updated_db_log = db.session.query(IntegrationLog).get(log_id_to_update)
    assert updated_db_log is not None
    assert str(updated_db_log.id) == log_id_to_update
    assert updated_db_log.status == "Success"  # Updated
    assert updated_db_log.response_json == {"result": "ok", "value": 123}  # Updated
    assert updated_db_log.error_details is None  # Updated
    assert updated_db_log.attempts == 2  # Updated
    assert updated_db_log.executing_user_email == "<EMAIL>"  # Updated
    # Check unchanged fields
    assert updated_db_log.report_id == report.id
    assert updated_db_log.operation == initial_operation  # Not updatable via this schema/endpoint
    assert updated_db_log.flow == "Egress"  # Not updated
    assert updated_db_log.payload_json == {"initial": "data"}  # Not updated
    assert updated_db_log.payload_json_hash == created_log_data["payload_json_hash"]  # Consistent
    assert (
        updated_db_log.response_json_hash == "1cf995782e4e28de00ab5013a8283dcf838d864f55b995be9a04fd59bd3120de"
    )  # Consistent


def test_update_integration_log_not_found(app_context, mocker):
    _init_db_and_prepare_base_body(mocker)
    non_existent_log_id = "11111111-1111-1111-1111-111111111111"

    update_body = {"status": "Failure", "executing_user_email": "<EMAIL>", "error_details": {"code": "NF"}}
    response = update_integration_log_by_id(integration_log_id=non_existent_log_id, body=update_body)

    assert response[1] == 404
    assert response[0] == {}


def test_get_by_id_success(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)
    initial_body = {
        **base_body,
        "operation": "ConiferFinysSubmissionCreate",
        "status": "Success",
        "flow": "Egress",
        "payload_json": {"request": 1},
        "response_json": {"result": "abc"},
        "error_details": {"error": "details"},
        "additional_data": {"additional": "data"},
        "logical_identifier": "logical_id",
    }
    created_log_data = _create_integration_log_and_assert(initial_body)

    response = get_by_id(integration_log_id=created_log_data["id"])

    assert response[1] == 200
    retrieved_data = response[0]
    assert retrieved_data["id"] == created_log_data["id"]
    assert retrieved_data["created_at"] is not None
    assert retrieved_data["updated_at"] is None
    assert retrieved_data["report_id"] == str(report.id)
    assert retrieved_data["submission_id"] == str(report.submission.id)
    assert retrieved_data["status"] == "Success"
    assert retrieved_data["flow"] == "Egress"
    assert retrieved_data["payload_json"] == {"request": 1}
    assert retrieved_data["response_json"] == {"result": "abc"}
    assert retrieved_data["error_details"] == {"error": "details"}
    assert retrieved_data["additional_data"] == {"additional": "data"}
    assert retrieved_data["logical_identifier"] == "logical_id"
    assert retrieved_data["attempts"] == 1
    assert retrieved_data["executing_user_email"] == "<EMAIL>"


def test_bulk_get_error_logs_not_found(app_context, mocker):
    _init_db_and_prepare_base_body(mocker)
    body = {
        "organization_id": 1,
        "report_ids": ["00000000-0000-0000-0000-000000000000"],
        "operation": "ConiferFinysSubmissionCreate",
    }
    res, code = bulk_get_integration_error_logs(body)

    assert res == []
    assert code == 200


def test_bulk_get_error_logs_wrong_org(app_context, mocker):
    _init_db_and_prepare_base_body(mocker)
    body = {
        "organization_id": 2,
        "report_ids": ["00000000-0000-0000-0000-000000000000"],
        "operation": "ConiferFinysSubmissionCreate",
    }
    res, code = bulk_get_integration_error_logs(body)

    assert res == {}
    assert code == 403


def test_bulk_get_error_logs_success(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)
    base_body["created_at"] = "2023-10-01T00:00:00Z"  # Set a fixed timestamp for consistency
    operation_to_find = "ConiferFinysSubmissionCreate"

    # Create two logs for the same report/operation
    initial_body = {
        **base_body,
        "operation": operation_to_find,
        "status": "Failure",
        "flow": "Egress",
        "payload_json": {"step": 1},
        "response_json": {"result": "abc"},
    }
    _create_integration_log_and_assert(initial_body)

    report_2, _ = report_and_submission_fixture(origin=Origin.EMAIL)
    report_2_base_body = {
        **base_body,
        "report_id": str(report_2.id),
        "submission_id": str(report_2.submission.id),
        "created_at": "2023-10-01T00:10:00Z",  # Ensure same timestamp for consistency
    }
    report_2_body = {
        **report_2_base_body,
        "operation": operation_to_find,
        "status": "Failure",
        "flow": "Ingress",
        "payload_json": {"step": 1},
    }
    _create_integration_log_and_assert(report_2_body)

    latest_body = {
        **base_body,
        "operation": operation_to_find,
        "status": "Failure",
        "flow": "Egress",
        "payload_json": {"step": 2},
        "response_json": {"final": True},
    }
    _create_integration_log_and_assert(latest_body)

    body = {
        "organization_id": 1,
        "report_ids": [str(report.id), str(report_2.id)],
        "operation": operation_to_find,
    }
    res, code = bulk_get_integration_error_logs(body)

    assert code == 200
    assert isinstance(res, list)
    assert len(res) == 3
    assert all(r["report_id"] in [str(report.id), str(report_2.id)] for r in res)
