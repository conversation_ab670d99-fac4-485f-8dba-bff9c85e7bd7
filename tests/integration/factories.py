from datetime import datetime, timedelta, timezone
from typing import Any, Optional
import hashlib
import uuid

from static_common.enums.brokerage_employee_roles import BrokerageEmployeeRoles
from static_common.enums.classification import (
    ClassificationBusinessRuleType,
    ClassifierOutputType,
    ClassifierUnits,
    CustomizableClassifierType,
    EmailClassifierType,
    ExtractionType,
    FilterRuleType,
    InputProcessingType,
)
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.emails import EmailType
from static_common.enums.file_enhancement_type import FileEnhancementType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.metric import MetricType
from static_common.enums.origin import Origin
from static_common.enums.sensible import SensibleUploadStatus
from static_common.enums.submission import SubmissionMode, SubmissionStage
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.models.submission_level_data import SourceDetails
import dateutil.parser
import pytz

from copilot.constants import PARAGON_ES_INBOX
from copilot.models import (
    AskQuestions,
    AuditTrail,
    Brokerage,
    BrokerageEmployee,
    ClientApplication,
    CustomizableClassifier,
    Feedback,
    File,
    HubTemplate,
    Loss,
    MetricGroup,
    MetricPreferencesTemplates,
    MetricTemplate,
    NotebookMessage,
    NotebookThread,
    Organization,
    Policy,
    ReportPermission,
    Settings,
    StuckSubmissionFeedback,
    SubmissionAudit,
    TenantFeedback,
    User,
    UserAction,
    VerificationCheckResult,
    db,
)
from copilot.models.admiral_uw_mappings import (
    AdmiralIndividualAssignment,
    AdmiralAssignmentLog,
    AdmiralUWMapping,
    AdmiralUWMappingAssignment,
)
from copilot.models.aig_uw_mapping import AIGUwMapping
from copilot.models.auto_qa_checks import AutoQACheck
from copilot.models.boss_uw_mappings import BossUWMapping
from copilot.models.bowhead_uw_mappings import BowheadUWMapping
from copilot.models.broker_client_codes import BrokerClientCode
from copilot.models.broker_groups import BrokerGroup, BrokerGroupMapping
from copilot.models.brokerage_client_codes import BrokerageClientCode
from copilot.models.brokerages_v2 import (
    SubmissionBrokerage,
    SubmissionBrokerageEmployee,
)
from copilot.models.businesses import UpdateBusinessLog
from copilot.models.conifer_uw_mappings import ConiferUWMapping
from copilot.models.customizable_classifiers import (
    ClassifierConfig,
    ClassifierConfigVersion,
    ClassifierPhrase,
    ClassifierToConfigVersion,
    ClassifierVersion,
    ClassifierVersionTaskDefinition,
    CustomizableClassifierV2,
    FilterRule,
    LLMConfigVersion,
    PhrasesConfigVersion,
    PhrasesWithLLMConfigVersion,
)
from copilot.models.email_classification import EmailClassifier
from copilot.models.email_template import EmailTemplate, ScheduledEmail
from copilot.models.emails import (
    Email,
    EmailClassification,
    EmailClassificationLabel,
    EmailStatus,
    ReportEmailCorrespondence,
    SendgridEmailData,
)
from copilot.models.execution_events import CopilotWorkerExecutionEvent
from copilot.models.experiments import Experiment, ExperimentRun, ExperimentSample
from copilot.models.files import (
    CustomFileType,
    EnhancedFile,
    FileMetric,
    ProcessedFile,
    SubmissionFilesData,
)
from copilot.models.files_extracted_metadata import FileExtractedMetadata
from copilot.models.ifta import IFTAData
from copilot.models.label_studio import FileLabelStudio
from copilot.models.lob import Lob
from copilot.models.metrics import MetricPreference, MetricSource, MetricV2
from copilot.models.org_metric_configs import OrgMetricConfig
from copilot.models.paragon import ParagonCompanyLine, ParagonUnderwriter
from copilot.models.paragon_wc_uw_mapping import ParagonWCUWMapping
from copilot.models.policy import LOBCarrier, LossPolicy
from copilot.models.quality_audit_question import QualityAuditQuestion
from copilot.models.reports import (
    ClientSubmissionStageConfig,
    Coverage,
    ExecutionEvent,
    NAICSCode,
    ReadSubmission,
    ReportAlert,
    ReportBundle,
    ReportLink,
    ReportProcessingDependency,
    ReportShadowDependency,
    ReportSummaryPreference,
    ReportTriage,
    ReportV2,
    StuckDetails,
    Submission,
    SubmissionBookmark,
    SubmissionBusiness,
    SubmissionClearingIssue,
    SubmissionClientId,
    SubmissionCoverage,
    SubmissionDeductible,
    SubmissionFieldSource,
    SubmissionIdentifier,
    SubmissionNote,
    SubmissionPremises,
    SubmissionPriority,
    SubmissionRecommendationResult,
    SubmissionUser,
    SummaryPreference,
)
from copilot.models.routing_rule import RoutingRule
from copilot.models.secura_uw_mappings import SecuraUWMapping
from copilot.models.sensible_cache import SensibleDocumentResponseCache
from copilot.models.sensible_calls import SensibleCalls, SensibleQuota
from copilot.models.sensible_extraction import (
    LossRunSensibleExtractionDocument,
    SensibleExtraction,
    SensibleExtractionDocument,
)
from copilot.models.shareholders import Shareholder
from copilot.models.submission_history import SubmissionHistory
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.submission_processing import SubmissionProcessing
from copilot.models.submission_relations import SubmissionRelation
from copilot.models.submission_sync import (
    SubmissionIdentifiersSuggestion,
    SubmissionSync,
    SubmissionSyncIdentifiers,
    SubmissionSyncMatcherData,
    SubmissionSyncReport,
    SyncMatchingResult,
    SyncMatchingRun,
)
from copilot.models.support_user import SupportUser, SupportUserFile, SupportUserReport
from copilot.models.task_dataset import (
    TaskDataset,
    TaskDatasetExecution,
    TaskDatasetGroundTruth,
    TaskDatasetInput,
    TaskDatasetModelOutcome,
)
from copilot.models.task_metrics import TaskDefinitionMetrics, TaskModelMetrics
from copilot.models.task_scoring import TaskScoring, TaskScoringExecution
from copilot.models.tasks import (
    Task,
    TaskCostLimit,
    TaskDefinition,
    TaskDefinitionModel,
    TaskExecution,
    TaskModel,
)
from copilot.models.taxonomy import TaxonomyDescription, TaxonomyMapping
from copilot.models.types import (
    CoverageType,
    ExecutionEventType,
    HubTemplateType,
    LineOfBusinessType,
    PermissionType,
    ReportDependencyType,
    ReportShadowType,
    SubmissionPremisesType,
)
from copilot.models.underlying_policy import UnderlyingPolicy
from copilot.models.user import UserGroup, user_open_reports
from copilot.models.workers_comp_experience import (
    WorkersCompExperience,
    WorkersCompStateRatingInfo,
)
from copilot.services.orgs.secura_service import SecuraLobs


def _parse_date_if_needed(string_as_date):
    if string_as_date and isinstance(string_as_date, str):
        return dateutil.parser.parse(string_as_date)
    return string_as_date


def organization_fixture(**kwargs):
    id = kwargs.get("id", 1)
    if org := Organization.query.get(id):
        return org
    organization = Organization(
        id=id,
        name=kwargs.get("name", f"organization {id}"),
        email_domain=kwargs.get("email_domain", f"organization{id}.com"),
        description=kwargs.get("description"),
        appetite=kwargs.get("appetite"),
        renewal_creation_interval=timedelta(days=9 * 30),
        identity_provider=kwargs.get("identity_provider"),
        for_analysis=kwargs.get("for_analysis", True),
        shared_notification_address=kwargs.get("shared_notification_address"),
    )
    db.session.add(organization)
    return organization


def submission_audit_fixture(submission_id, change_type, data_before, data_after, actual_user) -> SubmissionAudit:
    audit = SubmissionAudit(
        occurred_at=datetime.utcnow(),
        submission_id=submission_id,
        change_type=change_type,
        data_before=data_before,
        data_after=data_after,
        actual_user=actual_user,
    )
    db.session.add(audit)
    return audit


def client_submission_stage_config_fixture(**kwargs) -> ClientSubmissionStageConfig:
    client_submission_stage_config = ClientSubmissionStageConfig(
        id=kwargs.get("id"),
        organization_id=kwargs.get("organization_id", 1),
        client_stage=kwargs.get("client_stage", "Stage 1"),
        tags=kwargs.get("tags", []),
        copilot_stage=kwargs.get("copilot_stage", "ON_MY_PLATE"),
        tag_labels=kwargs.get("tag_labels", []),
        with_comment=kwargs.get("with_comment"),
        not_selectable=kwargs.get("not_selectable"),
        default=kwargs.get("default"),
    )
    db.session.add(client_submission_stage_config)
    return client_submission_stage_config


def user_fixture(**kwargs) -> User:
    id = kwargs.get("id", kwargs.get("user_id", 1))
    email = kwargs.get("email", "<EMAIL>")
    if user := User.query.get(id):
        return user
    user = User(
        id=id,
        email=email,
        settings=kwargs.get("settings"),
        pass_hash=kwargs.get("pass_hash"),
        name=kwargs.get("name"),
        organization_id=kwargs.get("organization_id", 1),
        role=kwargs.get("role"),
        external_id=kwargs.get("external_id"),
        cross_organization_access=kwargs.get("cross_organization_access", False),
        is_enabled=kwargs.get("is_enabled", True),
        registered_in_knock=kwargs.get("registered_in_knock", False),
        timezone=kwargs.get("timezone"),
        photo_s3_file_path=kwargs.get("photo_s3_file_path"),
        created_at=kwargs.get("created_at"),
        updated_at=kwargs.get("updated_at"),
        is_read_only_account=kwargs.get("is_read_only_account"),
        email_aliases=kwargs.get("email_aliases"),
        redirect_user_id=kwargs.get("redirect_user_id"),
        last_opened_report=kwargs.get("last_opened_report"),
        tac_file_id=kwargs.get("tac_file_id"),
        tac_signed_on=kwargs.get("tac_signed_on"),
        signup_data=kwargs.get("signup_data"),
        open_reports=kwargs.get("open_reports", []),
    )
    db.session.add(user)
    return user


def submission_fixture(**kwargs) -> Submission:
    id = kwargs.get("id") or uuid.uuid4()

    policy_expiration_date = _parse_date_if_needed(kwargs.get("policy_expiration_date"))
    proposed_effective_date = _parse_date_if_needed(kwargs.get("proposed_effective_date"))

    mode = kwargs.get("mode")
    if mode and isinstance(mode, str) and mode in SubmissionMode:
        mode = SubmissionMode[mode]

    submission = Submission(
        id=id,
        name=kwargs.get("name", str(id)),
        owner_id=kwargs.get("owner_id", 1),
        due_date=kwargs.get("due_date"),
        proposed_effective_date=proposed_effective_date,
        stage=kwargs.get("stage") or SubmissionStage.ON_MY_PLATE,
        policy_expiration_date=policy_expiration_date,
        created_at=kwargs.get("created_at"),
        updated_at=kwargs.get("updated_at"),
        processing_state=kwargs.get("processing_state"),
        decline_email_sent=False,
        is_verification_required=kwargs.get("is_verification_required"),
        is_verified=kwargs.get("is_verified"),
        is_verified_shell=kwargs.get("is_verified_shell"),
        primary_naics_code=kwargs.get("primary_naics_code"),
        iso_gl_code=kwargs.get("iso_gl_code"),
        sic_code=kwargs.get("sic_code"),
        icc_code=kwargs.get("icc_code"),
        is_naics_verified=kwargs.get("is_naics_verified"),
        recommendation_v2_score=kwargs.get("recommendation_v2_score"),
        recommendation_v2_action=kwargs.get("recommendation_v2_action"),
        mode=mode,
        account_id=kwargs.get("account_id"),
        is_renewal=kwargs.get("is_renewal"),
        origin=kwargs.get("origin"),
        is_auto_processed=kwargs.get("is_auto_processed"),
        is_processing=kwargs.get("is_processing"),
        is_manual_verified=kwargs.get("is_manual_verified"),
        is_auto_verified=kwargs.get("is_auto_verified"),
        is_waiting_for_auto_verify=kwargs.get("is_waiting_for_auto_verify"),
        manual_verified_at=kwargs.get("manual_verified_at"),
        report=kwargs.get("report"),
        target_premium=kwargs.get("target_premium"),
        expired_premium=kwargs.get("expired_premium"),
        sales=kwargs.get("sales"),
        coverage_type=kwargs.get("coverage_type"),
        manual_naics_assignment_required=kwargs.get("manual_naics_assignment_required"),
        missing_data_status=kwargs.get("missing_data_status"),
        stuck_reason=kwargs.get("stuck_reason"),
        synced_at=kwargs.get("synced_at"),
        light_cleared=kwargs.get("light_cleared"),
        auto_verified_at=kwargs.get("auto_verified_at"),
        is_for_audit=kwargs.get("is_for_audit"),
        audited_at=kwargs.get("audited_at"),
        audited_by=kwargs.get("audited_by"),
        is_stuck_engineering=kwargs.get("is_stuck_engineering"),
        is_shadow_processed=kwargs.get("is_shadow_processed"),
        recommendation_v2_is_refer=kwargs.get("recommendation_v2_is_refer"),
        stage_details=kwargs.get("stage_details"),
        is_deleted=kwargs.get("is_deleted"),
        declined_date=kwargs.get("declined_date"),
        declined_user_id=kwargs.get("declined_user_id"),
        clearing_assignee_id=kwargs.get("clearing_assignee_id"),
        parent_id=kwargs.get("parent_id"),
        renewal_creation_date=kwargs.get("renewal_creation_date"),
        lob_id=kwargs.get("lob_id"),
        received_date=kwargs.get("received_date"),
        is_metrics_set_manually=kwargs.get("is_metrics_set_manually"),
        businesses_resolving_state=kwargs.get("businesses_resolving_state"),
        recommendation_v2_priority=kwargs.get("recommendation_v2_priority"),
        recommendation_result=kwargs.get("recommendation_result"),
        send_decline_email=kwargs.get("send_decline_email"),
        send_decline_email_error=kwargs.get("send_decline_email_error"),
        decline_email_tracking_id=kwargs.get("decline_email_tracking_id"),
        decline_email_delivered=kwargs.get("decline_email_delivered"),
        verified_at=kwargs.get("verified_at"),
        email_project_description=kwargs.get("email_project_description"),
        decline_email_recipient_address=kwargs.get("decline_email_recipient_address"),
        pds_special_note=kwargs.get("pds_special_note"),
        sent_rule_email=kwargs.get("sent_rule_email"),
        description_of_operations=kwargs.get("description_of_operations"),
        missing_documents=kwargs.get("missing_documents"),
        decline_email_cc=kwargs.get("decline_email_cc"),
        decline_custom_template=kwargs.get("decline_custom_template"),
        decline_attachments=kwargs.get("decline_attachments"),
        reason_for_declining=kwargs.get("reason_for_declining"),
        lost_reasons=kwargs.get("lost_reasons"),
        generated_description_of_operations=kwargs.get("generated_description_of_operations"),
        account_name=kwargs.get("account_name"),
        notes=kwargs.get("notes"),
        decline_custom_subject=kwargs.get("decline_custom_subject"),
        email_description=kwargs.get("email_description"),
        active_email_template_id=kwargs.get("active_email_template_id"),
        brokerage_id=kwargs.get("brokerage_id"),
        broker_id=kwargs.get("broker_id"),
        brokerage_contact_id=kwargs.get("brokerage_contact_id"),
        quoted_date=kwargs.get("quoted_date"),
        bound_date=kwargs.get("bound_date"),
        cleared_date=kwargs.get("cleared_date"),
        frozen_as_of=kwargs.get("frozen_as_of"),
        contractor_submission_type=kwargs.get("contractor_submission_type"),
        project_insurance_type=kwargs.get("project_insurance_type"),
        incumbent_carrier=kwargs.get("incumbent_carrier"),
        fni_state=kwargs.get("fni_state"),
        is_enhanced_shell=kwargs.get("is_enhanced_shell"),
        fni_fein=kwargs.get("fni_fein"),
        adjusted_tiv=kwargs.get("adjusted_tiv"),
        tiv=kwargs.get("tiv"),
        primary_state=kwargs.get("primary_state"),
        client_stage_comment=kwargs.get("client_stage_comment"),
        client_clearing_status=kwargs.get("client_clearing_status"),
        clearing_status=kwargs.get("clearing_status"),
        brokerage_office=kwargs.get("brokerage_office"),
        sub_producer_name=kwargs.get("sub_producer_name"),
        sub_producer_email=kwargs.get("sub_producer_email"),
        prevent_clearing_updates=kwargs.get("prevent_clearing_updates"),
        client_recommendations_score=kwargs.get("client_recommendations_score"),
        is_pre_renewal=kwargs.get("is_pre_renewal"),
        is_renewal_shell=kwargs.get("is_renewal_shell"),
        facts_config=kwargs.get("facts_config"),
        policy_status=kwargs.get("policy_status"),
        lookalike_bind_rate=kwargs.get("lookalike_bind_rate"),
        is_stub=kwargs.get("is_stub"),
    )
    if client_stage_id := kwargs.get("client_stage_id"):
        submission.client_stage_id = client_stage_id

    db.session.add(submission)
    return submission


def submission_user_fixture(user_id: int, submission_id: int, **kwargs) -> SubmissionUser:
    submission_user: SubmissionUser = SubmissionUser(
        user_id=user_id, submission_id=submission_id, source=kwargs.get("source"), created_at=kwargs.get("created_at")
    )
    db.session.add(submission_user)
    return submission_user


def submission_client_id_fixture(**kwargs) -> SubmissionClientId:
    id = kwargs.get("id") or uuid.uuid4()
    submission_client_id = SubmissionClientId(
        id=id,
        client_submission_id=kwargs.get("client_submission_id"),
        submission_id=kwargs.get("submission_id"),
        source=kwargs.get("source"),
    )
    db.session.add(submission_client_id)
    return submission_client_id


def submission_sync_fixture(**kwargs) -> SubmissionSync:
    policy_number = kwargs.get("policy_number", "TEST_POLICY")
    organization_id = kwargs.get("organization_id", 1)
    ss_id = uuid.UUID(hashlib.md5(f"{policy_number}-{organization_id}".encode("utf8", "surrogatepass")).hexdigest())
    submission_sync = SubmissionSync(
        id=ss_id,
        policy_number=policy_number,
        submission_name=kwargs.get("submission_name", "A submission name"),
        underwriter_email=kwargs.get("underwriter_email", "<EMAIL>"),
        stage=kwargs.get("stage", SubmissionStage.ON_MY_PLATE),
        received_date=kwargs.get("received_date", datetime.now()),
        effective_date=kwargs.get("effective_date", datetime.now()),
        coverage_name=kwargs.get("coverage_name", "liability"),
        coverage_type=kwargs.get("coverage_type", CoverageType.EXCESS),
        quoted_premium=kwargs.get("quoted_premium"),
        bound_premium=kwargs.get("quoted_premium"),
        organization_id=organization_id,
        applied_changes=kwargs.get("applied_changes", []),
        pending_changes=kwargs.get("applied_changes", []),
        applied=kwargs.get("applied", False),
        attempt=kwargs.get("attempt", 0),
        declined_date=kwargs.get("declined_date"),
        source=kwargs.get("source", "some excel spreadsheet.xls"),
        account_id=kwargs.get("account_id"),
        is_renewal=kwargs.get("is_renewal"),
        direct_match_only=kwargs.get("direct_match_only"),
        coverages=kwargs.get("coverages"),
        broker=kwargs.get("broker"),
        brokerage=kwargs.get("brokerage"),
        created_date=kwargs.get("created_date"),
        quoted_date=kwargs.get("quoted_date"),
        bound_date=kwargs.get("bound_date"),
        contractor_submission_type=kwargs.get("contractor_submission_type"),
        project_insurance_type=kwargs.get("project_insurance_type"),
        client_id_being_renewed=kwargs.get("client_id_being_renewed"),
        broker_email=kwargs.get("broker_email"),
        premium=kwargs.get("premium"),
        expired_premium=kwargs.get("expired_premium"),
        primary_state=kwargs.get("primary_state"),
        client_stage_id=kwargs.get("client_stage_id"),
        user_groups_to_match=kwargs.get("user_groups_to_match"),
        policy_number_being_renewed=kwargs.get("policy_number_being_renewed"),
        policy_status=kwargs.get("policy_status"),
        policy_expiration_date=kwargs.get("policy_expiration_date"),
        first_seen=kwargs.get("first_seen"),
        replaces_client_id=kwargs.get("replaces_client_id"),
        replaced_by_client_id=kwargs.get("replaced_by_client_id"),
        is_deleted=kwargs.get("is_deleted", False),
        deleted_at=kwargs.get("deleted_at"),
        parent_client_id=kwargs.get("parent_client_id"),
    )
    db.session.add(submission_sync)
    return submission_sync


def submission_sync_report_fixture(**kwargs) -> SubmissionSyncMatcherData:
    id = kwargs.get("id") or uuid.uuid4()
    submission_sync_report = SubmissionSyncReport(
        id=id,
        org_group=kwargs.get("org_group", []),
        submission_sync_id=kwargs.get("submission_sync_id"),
    )
    db.session.add(submission_sync_report)
    return submission_sync_report


def submission_sync_identifiers_fixture(**kwargs) -> SubmissionSyncMatcherData:
    id = kwargs.get("id") or uuid.uuid4()
    submission_sync = SubmissionSyncIdentifiers(
        id=id,
        quote_numbers=kwargs.get("quote_numbers", []),
        policy_numbers=kwargs.get("policy_numbers", []),
        submission_sync_id=kwargs.get("submission_sync_id"),
    )
    db.session.add(submission_sync)
    return submission_sync


def submission_sync_matcher_data_fixture(**kwargs) -> SubmissionSyncMatcherData:
    id = kwargs.get("id") or uuid.uuid4()
    submission_sync = SubmissionSyncMatcherData(
        id=id,
        named_insured=kwargs.get("named_insured", "Named insured"),
        named_insured_address=kwargs.get("named_insured_address", "Named insured address"),
        submission_sync_id=kwargs.get("submission_sync_id"),
        additional_data=kwargs.get("additional_data", {}),
    )
    db.session.add(submission_sync)
    return submission_sync


def submission_business_fixture(**kwargs) -> SubmissionBusiness:
    id = kwargs.get("id") or uuid.uuid4()
    submission_business = SubmissionBusiness(
        id=id,
        requested_name=kwargs.get("requested_name"),
        requested_legal_name=kwargs.get("requested_legal_name"),
        requested_address=kwargs.get("requested_address"),
        requested_phone_number=kwargs.get("requested_phone_number"),
        requested_industries=kwargs.get("requested_industries"),
        entity_role=kwargs.get("entity_role"),
        named_insured=kwargs.get("named_insured"),
        description_of_operations=kwargs.get("description_of_operations"),
        business_id=kwargs.get("business_id"),
        is_user_confirmed=kwargs.get("is_user_confirmed"),
        aliases=kwargs.get("aliases"),
        selected_alias=kwargs.get("selected_alias"),
        submission_id=kwargs.get("submission_id", str(uuid.uuid4())),
        serial_id_seq=kwargs.get("serial_id_seq"),
        serial_id=kwargs.get("serial_id"),
        ers_snapshot_id=kwargs.get("ers_snapshot_id"),
        project_insurance_type=kwargs.get("project_insurance_type"),
        hide_property_facts=kwargs.get("hide_property_facts"),
        hide=kwargs.get("hide"),
        additional_info=kwargs.get("additional_info"),
        file_id_sources=kwargs.get("file_id_sources"),
    )
    db.session.add(submission_business)
    return submission_business


def file_fixture(**kwargs) -> File:
    id = kwargs.get("id") or uuid.uuid4()
    file = File(
        id=id,
        created_at=kwargs.get("created_at"),
        updated_at=kwargs.get("updated_at"),
        name=kwargs.get("name", "Missing Name"),
        size=kwargs.get("size"),
        submission_id=kwargs.get("submission_id"),
        processing_state=kwargs.get("processing_state"),
        classification=kwargs.get("classification"),
        s3_key=kwargs.get("s3_key", None),
        file_type=kwargs.get("file_type", FileType.UNKNOWN),
        origin=kwargs.get("origin", Origin.COPILOT),
        checksum=kwargs.get("checksum"),
        additional_info=kwargs.get("additional_info", None),
        sensible_status=kwargs.get("sensible_status"),
        initial_processing_state=kwargs.get("initial_processing_state"),
        is_required_shadow_processing=kwargs.get("is_required_shadow_processing"),
        parent_file_id=kwargs.get("parent_file_id"),
        initial_classification_confidence=kwargs.get("initial_classification_confidence"),
        is_internal=kwargs.get("is_internal"),
        replaced_by_file_ids=kwargs.get("replaced_by_file_ids"),
        submission_business_id=kwargs.get("submission_business_id"),
        user_id=kwargs.get("user_id"),
        organization_id=kwargs.get("organization_id"),
        user_file_type=kwargs.get("user_file_type"),
        initial_classification=kwargs.get("initial_classification"),
        comment=kwargs.get("comment"),
        issues=kwargs.get("issues"),
        internal_notes=kwargs.get("internal_notes"),
        processing_state_updated_at=kwargs.get("processing_state_updated_at"),
        retry_count=kwargs.get("retry_count"),
        execution_arn=kwargs.get("execution_arn"),
        client_file_tags=kwargs.get("client_file_tags"),
        client_file_type=kwargs.get("client_file_type"),
        external_identifier=kwargs.get("external_identifier"),
        additional_file_names=kwargs.get("additional_file_names"),
        is_locked=kwargs.get("is_locked"),
        is_deleted=kwargs.get("is_deleted"),
        custom_file_type_id=kwargs.get("custom_file_type_id"),
        custom_classification=kwargs.get("custom_classification"),
        content_type=kwargs.get("content_type"),
    )
    db.session.add(file)
    return file


def custom_file_type_fixture(**kwargs) -> CustomFileType:
    custom_file_type = CustomFileType(
        id=kwargs.get("id") or uuid.uuid4(),
        file_type_name=kwargs.get("file_type_name"),
        organization_id=kwargs.get("organization_id"),
        is_enabled=kwargs.get("is_enabled", True),
    )
    db.session.add(custom_file_type)
    return custom_file_type


def processed_file_fixture(**kwargs) -> ProcessedFile:
    id = kwargs.get("id") or uuid.uuid4()
    file = ProcessedFile(
        id=id,
        file_id=kwargs.get("file_id"),
        raw_processed_data=kwargs.get("raw_processed_data", {}),
        processed_data=kwargs.get("processed_data", {}),
        entity_mapped_data=kwargs.get("entity_mapped_data"),
        onboarded_data=kwargs.get("onboarded_data"),
        business_resolution_data=kwargs.get("business_resolution_data"),
    )
    db.session.add(file)
    return file


def file_extracted_metadata_fixture(**kwargs) -> FileExtractedMetadata:
    id = kwargs.get("id") or uuid.uuid4()
    file_extracted_metadata = FileExtractedMetadata(
        id=id,
        file_id=kwargs.get("file_id"),
        metadata_type=kwargs.get("metadata_type"),
        metadata_extract=kwargs.get("metadata_extract", {}),
        status=kwargs.get("status"),
        checksum=kwargs.get("checksum"),
        pending_info=kwargs.get("pending_info", {}),
        retry_count=kwargs.get("retry_count", 0),
        s3_key=kwargs.get("s3_key"),
    )
    db.session.add(file_extracted_metadata)
    return file_extracted_metadata


def submission_files_data_fixture(**kwargs) -> SubmissionFilesData:
    id_ = kwargs.get("id") or uuid.uuid4()
    submission_data = SubmissionFilesData(
        id=id_,
        submission_id=kwargs.get("submission_id"),
        entity_mapped_data=kwargs.get("entity_mapped_data"),
        onboarded_data=kwargs.get("onboarded_data"),
        loaded_data=kwargs.get("loaded_data"),
        raw_em_data=kwargs.get("raw_em_data"),
        raw_do_data=kwargs.get("raw_do_data"),
    )
    db.session.add(submission_data)
    return submission_data


def policy_creation_fixture(**kwargs) -> Policy:
    id = kwargs.get("id") or uuid.uuid4()
    policy = Policy(
        id=id,
        organization_id=kwargs.get("organization_id"),
        submission_id=kwargs.get("submission_id"),
        external_id=kwargs.get("external_id"),
    )
    db.session.add(policy)
    return policy


def coverage_fixture(**kwargs) -> Coverage:
    coverage = Coverage(
        id=kwargs.get("id", uuid.uuid4()),
        name=kwargs.get("name", "coverage"),
        display_name=kwargs.get("display_name", "Coverage"),
        organization_id=kwargs.get("organization_id", 1),
        coverage_types=kwargs.get("coverage_types", []),
        is_disabled=kwargs.get("is_disabled", False),
        logical_group=kwargs.get("logical_group"),
    )
    db.session.add(coverage)
    return coverage


def brokerage_fixture(**kwargs) -> Brokerage:
    org = None
    organizations = []
    if organization_id := kwargs.get("organization_id", None):
        if not Organization.query.get(organization_id):
            org = organization_fixture(id=organization_id)

    if not org:
        org = Organization.query.get(organization_id)
    if org:
        organizations.append(org)
    db.session.commit()
    agency = Brokerage(
        id=kwargs.get("id", uuid.uuid4()),
        name=kwargs.get("name", "Test Brokerage"),
        organizations=organizations,
        aliases=kwargs.get("aliases", []),
        is_confirmed=kwargs.get("is_confirmed", True),
        domains=kwargs.get("domains", []),
    )
    db.session.add(agency)
    return agency


def broker_fixture(**kwargs) -> BrokerageEmployee:
    org = None
    organizations = []
    if organization_id := kwargs.get("organization_id", None):
        if not Organization.query.get(organization_id):
            org = organization_fixture(id=organization_id)

    if not org:
        org = Organization.query.get(organization_id)
    if org:
        organizations.append(org)
    db.session.commit()
    agent = BrokerageEmployee(
        id=kwargs.get("id", uuid.uuid4()),
        name=kwargs.get("name", "Test Agency"),
        email=kwargs.get("email", "<EMAIL>"),
        brokerage_id=kwargs.get("brokerage_id", None),
        aliases=kwargs.get("aliases", []) or [],
        organizations=organizations,
        roles=kwargs.get("roles", ["AGENT", "CORRESPONDENCE_CONTACT"]),
    )
    db.session.add(agent)
    return agent


def email_template_fixture(**kwargs) -> EmailTemplate:
    id = kwargs.get("id") or uuid.uuid4()
    email_template = EmailTemplate(
        id=id,
        external_template_id=kwargs.get("external_template_id", "d-2ceff4f5fcb44f9c9ed0b35a3b17600d"),
        external_version_id=uuid.uuid4(),
        name=kwargs.get("name", "Test"),
        subject=kwargs.get("subject", "Subject"),
        owner_id=kwargs.get("owner_id"),
        is_shared_with_organization=kwargs.get("is_shared_with_organization", False),
        type=kwargs.get("type"),
        to_address=kwargs.get("to_address"),
        cc_addresses=kwargs.get("cc_addresses"),
        reply_to=kwargs.get("reply_to"),
        html_content=kwargs.get("html_content"),
    )
    db.session.add(email_template)
    return email_template


def report_with_submissions_fixture(db_flush=True, **kwargs) -> ReportV2:
    report = report_fixture(**kwargs)
    submission = submission_fixture(**kwargs, report=report)
    if db_flush:
        db.session.flush()
    return report


def report_processing_dependency_fixture(
    report_id: uuid.UUID,
    dependent_report_id: uuid.UUID,
    dependency_type: ReportDependencyType = ReportDependencyType.SAME_ORG,
    id: uuid.UUID | None = None,
) -> ReportProcessingDependency:
    rpd_id = id or uuid.uuid4()
    dependency = ReportProcessingDependency(
        id=rpd_id,
        report_id=report_id,
        dependent_report_id=dependent_report_id,
        dependency_type=dependency_type,
    )
    db.session.add(dependency)
    return dependency


def report_shadow_dependency_fixture(
    report_id: uuid.UUID,
    shadow_report_id: uuid.UUID,
    is_active: bool = True,
    id: uuid.UUID | None = None,
) -> ReportShadowDependency:
    rsd_id = id or uuid.uuid4()
    dependency = ReportShadowDependency(
        id=rsd_id,
        report_id=report_id,
        shadow_report_id=shadow_report_id,
        is_active=is_active,
    )
    db.session.add(dependency)
    return dependency


def shadow_submission_fixture(original_submission: Submission, with_files: bool = True) -> Submission:
    shadow_report, shadow_submission = report_and_submission_fixture(
        organization_id=original_submission.organization_id,
        origin=original_submission.origin,
    )

    original_submission.report.shadow_type = ReportShadowType.HAS_ACTIVE_SHADOW
    shadow_submission.is_shadow_processed = True
    shadow_submission.is_auto_processed = True
    shadow_submission.processing_state = SubmissionProcessingState.DATA_ONBOARDING
    shadow_report.shadow_type = ReportShadowType.IS_ACTIVE_SHADOW

    report_shadow_dependency_fixture(original_submission.report_id, shadow_report.id)

    if with_files:
        sov_file = file_fixture(
            processing_state=FileProcessingState.CLASSIFIED,
            file_type=FileType.SOV,
            classification=ClassificationDocumentType.SOV_SPREADSHEET,
            submission_id=original_submission.id,
            s3_key="sov",
            is_required_shadow_processing=True,
            checksum="sov",
        )

        shadow_sov_file = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
            file_type=FileType.SOV,
            classification=ClassificationDocumentType.SOV_SPREADSHEET,
            submission_id=shadow_submission.id,
            s3_key="sov",
            is_required_shadow_processing=False,
            checksum="sov",
        )

    return shadow_submission


def report_and_submission_fixture(**kwargs):
    report = kwargs["report"] if "report" in kwargs else report_fixture(**kwargs)
    submission = kwargs["submission"] if "submission" in kwargs else submission_fixture(**kwargs)
    submission.report = report
    submission.report_id = report.id

    db.session.commit()

    return report, submission


def recommendation_result_fixture(**kwargs):
    return SubmissionRecommendationResult(
        submission_id=kwargs.get("submission_id"),
        action=kwargs.get("action"),
        score=kwargs.get("score"),
        score_ml=kwargs.get("score_ml"),
        pm_rules_modifier=kwargs.get("pm_rules_modifier"),
        priority=kwargs.get("priority"),
        is_refer=kwargs.get("is_refer"),
    )


def user_open_report_fixture(**kwargs):
    insert_query = f"""
        insert into user_open_reports (user_id, report_id, created_at) values (:user_id, :report_id, :created_at);
    """
    db.session.execute(
        insert_query,
        {
            "user_id": kwargs.get("user_id"),
            "report_id": kwargs.get("report_id"),
            "created_at": kwargs.get("created_at", datetime.now()),
        },
    )
    return


def report_fixture(**kwargs) -> ReportV2:
    report = ReportV2(
        id=kwargs.get("id", uuid.uuid4()),
        owner_id=kwargs.get("owner_id", 1),
        name=kwargs.get("name", "report"),
        email_message_id=kwargs.get("email_message_id", "test@co"),
        email_body=kwargs.get("email_body"),
        correspondence_id=kwargs.get("correspondence_id"),
        created_at=kwargs.get("created_at", pytz.UTC.localize(datetime.now())),
        organization_id=kwargs.get("organization_id", 1),
        tier=kwargs.get("tier", 1),
        additional_data=kwargs.get("additional_data", {}),
        is_deleted=kwargs.get("is_deleted", False),
        deletion_reason=kwargs.get("deletion_reason"),
        organization_permission_level=kwargs.get("organization_permission_level"),
        is_archived=kwargs.get("is_archived", False),
        routing_tags=kwargs.get("routing_tags", {}),
        shadow_type=kwargs.get("shadow_type"),
        is_copy=kwargs.get("is_copy"),
        is_rush=kwargs.get("is_rush"),
        report_bundle_id=kwargs.get("report_bundle_id"),
        email_subject=kwargs.get("email_subject"),
        email_references=kwargs.get("email_references"),
        rush_source=kwargs.get("rush_source"),
        org_group=kwargs.get("org_group"),
        is_user_waiting_for_shadow=kwargs.get("is_user_waiting_for_shadow"),
        is_email_classification_enabled=kwargs.get("is_email_classification_enabled"),
    )

    db.session.add(report)
    return report


def report_with_business_fixture(
    business_id: uuid.UUID | None = None, snapshot_id: uuid.UUID | None = None
) -> ReportV2:
    business_id = business_id or uuid.uuid4()
    report = report_fixture()
    submission = submission_fixture(report=report)
    submission_business_fixture(business_id=business_id, submission_id=submission.id, ers_snapshot_id=snapshot_id)
    db.session.commit()

    return report


def loss_fixture(**kwargs) -> Loss:
    loss_policy = kwargs.get("loss_policy")
    if not loss_policy and "loss_policy_id" in kwargs:
        loss_policy = LossPolicy.query.get(kwargs.get("loss_policy_id"))
    loss = Loss(
        id=kwargs.get("id", uuid.uuid4()),
        submission_id=kwargs.get("submission_id", uuid.uuid4()),
        coverage_id=kwargs.get("coverage_id", None),
        organization_id=kwargs.get("organization_id", 1),
        claim_number=kwargs.get("claim_number", str(uuid.uuid4())),
        loss_date=kwargs.get("loss_date", datetime.today()),
        claim_date=kwargs.get("claim_date", datetime.today()),
        sum_of_total_net_incurred=kwargs.get("sum_of_total_net_incurred", 0.0),
        carrier=kwargs.get("carrier", "Progressive"),
        lob_raw=kwargs.get("lob_raw", "businessAuto"),
        original_line_of_business=kwargs.get("original_line_of_business", None),
        line_of_business=kwargs.get("line_of_business", LineOfBusinessType.BUSINESS_AUTO),
        loss_address=kwargs.get("loss_address", "PA"),
        claim_status=kwargs.get("claim_status", "CLOSED"),
        sum_of_net_paid_alae=kwargs.get("sum_of_net_paid_alae", 0.0),
        sum_of_net_outstanding_alae=kwargs.get("sum_of_net_outstanding_alae", 0.0),
        policy_effective_date=kwargs.get("policy_effective_date"),
        policy_expiration_date=kwargs.get("policy_expiration_date"),
        file_id=kwargs.get("file_id"),
        policy_id=kwargs.get("policy_id"),
        loss_policy_id=loss_policy.id if loss_policy else kwargs.get("loss_policy_id"),
        loss_policy=loss_policy,
        is_manual=kwargs.get("is_manual", False),
        is_duplicate=kwargs.get("is_duplicate", False),
        claim_description=kwargs.get("claim_description"),
        submission_business_id=kwargs.get("submission_business_id"),
        net_of_deductible=kwargs.get("net_of_deductible"),
        recoveries=kwargs.get("recoveries"),
        total_paid=kwargs.get("total_paid"),
        report_generated_date=kwargs.get("report_generated_date"),
        report_date=kwargs.get("report_date"),
        exposure_close_date=kwargs.get("exposure_close_date"),
        sum_of_loss_reserve=kwargs.get("sum_of_loss_reserve"),
        sum_of_alae_reserve=kwargs.get("sum_of_alae_reserve"),
        sum_of_net_paid_loss=kwargs.get("sum_of_net_paid_loss"),
        sum_of_net_outstanding_loss=kwargs.get("sum_of_net_outstanding_loss"),
        insured_name=kwargs.get("insured_name"),
        claimant_name=kwargs.get("claimant_name"),
        loss_type_raw=kwargs.get("loss_type_raw"),
        loss_type=kwargs.get("loss_type"),
        ligitation_status=kwargs.get("ligitation_status"),
        coverage_type=kwargs.get("coverage_type"),
        partial_hash=kwargs.get("partial_hash"),
        full_hash=kwargs.get("full_hash"),
        evidence=kwargs.get("evidence"),
    )
    db.session.add(loss)
    if loss_policy:
        loss_policy.claims.append(loss)
    return loss


def loss_policy_fixture(**kwargs) -> LossPolicy:
    loss_policy = LossPolicy(
        id=kwargs.get("id", uuid.uuid4()),
        number=kwargs.get("number", "123456789"),
        submission_id=kwargs.get("submission_id", uuid.uuid4()),
        organization_id=kwargs.get("organization_id", uuid.uuid4()),
        effective_date=kwargs.get("effective_date"),
        expiration_date=kwargs.get("expiration_date"),
        line_of_business=kwargs.get("line_of_business"),
        original_line_of_business=kwargs.get("original_line_of_business"),
        raw_line_of_business=kwargs.get("raw_line_of_business"),
        was_created_artificially=kwargs.get("was_created_artificially"),
        evidence=kwargs.get("evidence"),
    )
    db.session.add(loss_policy)
    return loss_policy


def settings_fixture(**kwargs):
    id = kwargs.get("id") or uuid.uuid4()
    settings = Settings(
        id=id,
        organization_id=kwargs.get("organization_id"),
        user_id=kwargs.get("user_id"),
        loss_runs_enabled=kwargs.get("loss_runs_enabled", False),
        loss_runs_manual=kwargs.get("loss_runs_manual", False),
        show_management_dashboard=kwargs.get("show_management_dashboard", False),
        email_domains=kwargs.get("email_domains"),
        is_map_enabled_by_default=kwargs.get("is_map_enabled_by_default"),
        default_coverages=kwargs.get("default_coverages"),
        can_resolve_clearing_issues=kwargs.get("can_resolve_clearing_issues"),
        is_clearing_enabled=kwargs.get("is_clearing_enabled"),
        should_be_notified_for_clearing_issues=kwargs.get("should_be_notified_for_clearing_issues"),
        show_coverage_filter=kwargs.get("show_coverage_filter"),
        is_tac_acceptance_required=kwargs.get("is_tac_acceptance_required"),
        process_submission_by_default=kwargs.get("process_submission_by_default"),
        allowed_submission_lobs=kwargs.get("allowed_submission_lobs"),
        allow_multiple_assigned_underwriters_per_submission=kwargs.get(
            "allow_multiple_assigned_underwriters_per_submission"
        ),
        allow_multiple_client_submissions_per_submission=kwargs.get("allow_multiple_client_submissions_per_submission"),
        allow_multiple_policies_per_submission=kwargs.get("allow_multiple_policies_per_submission"),
        show_hazard_hub_source=kwargs.get("show_hazard_hub_source"),
        is_support=kwargs.get("is_support"),
        total_credits=kwargs.get("total_credits"),
        allow_bundled_submissions=kwargs.get("allow_bundled_submissions"),
        distribution_channel_employed=kwargs.get("distribution_channel_employed"),
        is_operations=kwargs.get("is_operations"),
        clear_oldest_by_default=kwargs.get("clear_oldest_by_default"),
        show_underwriter_dashboard=kwargs.get("show_underwriter_dashboard"),
        show_recommendations_rating=kwargs.get("show_recommendations_rating"),
        show_account_id=kwargs.get("show_account_id"),
        reply_emails_enabled=kwargs.get("reply_emails_enabled"),
        is_light_clearing_enabled=kwargs.get("is_light_clearing_enabled"),
        default_tier=kwargs.get("default_tier"),
        max_tier_for_auto_processing=kwargs.get("max_tier_for_auto_processing"),
        is_cs_manager=kwargs.get("is_cs_manager"),
        show_internal_account_id=kwargs.get("show_internal_account_id"),
        support_email=kwargs.get("support_email"),
        allowed_submission_stages=kwargs.get("allowed_submission_stages"),
        whitelisted_emails=kwargs.get("whitelisted_emails"),
        default_regular_email_template_id=kwargs.get("default_regular_email_template_id"),
        default_decline_email_template_id=kwargs.get("default_decline_email_template_id"),
        classify_files_for_shells=kwargs.get("classify_files_for_shells"),
        enhanced_shells_enabled=kwargs.get("enhanced_shells_enabled"),
        use_client_file_types=kwargs.get("use_client_file_types"),
        client_file_types_config=kwargs.get("client_file_types_config"),
        transitions=kwargs.get("transitions"),
        allow_client_submission_stage=kwargs.get("allow_client_submission_stage"),
        submission_tab_stages=kwargs.get("submission_tab_stages"),
        tier_for_night_shift_processing=kwargs.get("tier_for_night_shift_processing"),
        max_submissions_for_night_shift_processing=kwargs.get("max_submissions_for_night_shift_processing"),
        org_groups=kwargs.get("org_groups"),
        user_notifications_enabled_after=kwargs.get("user_notifications_enabled_after"),
        client_clearing_status_config=kwargs.get("client_clearing_status_config"),
        client_id_required_for_clearing=kwargs.get("client_id_required_for_clearing"),
        submission_number_label=kwargs.get("submission_number_label"),
        allow_edit_quote_number=kwargs.get("allow_edit_quote_number"),
        allow_edit_policy_number=kwargs.get("allow_edit_policy_number"),
        highly_accurate_preferred=kwargs.get("highly_accurate_preferred"),
        md_default_to_client_id_subs=kwargs.get("md_default_to_client_id_subs"),
        clearing_fields=kwargs.get("clearing_fields"),
        include_oni_in_news=kwargs.get("include_oni_in_news"),
        di_disabled_file_types=kwargs.get("di_disabled_file_types"),
        use_sic_codes=kwargs.get("use_sic_codes"),
        is_email_classification_enabled=kwargs.get("is_email_classification_enabled"),
        is_document_ingestion_enabled=kwargs.get("is_document_ingestion_enabled"),
        show_inbox=kwargs.get("show_inbox"),
        can_adjust_email_classification=kwargs.get("can_adjust_email_classification"),
        strict_clearance_controls=kwargs.get("strict_clearance_controls"),
    )
    db.session.add(settings)
    return settings


def email_fixture(**kwargs: Any) -> Email:
    correspondence_id = kwargs.get("correspondence_id")
    if not correspondence_id:
        correspondence = ReportEmailCorrespondence(
            id=kwargs.get("correspondence_id", uuid.uuid4()), email_account=kwargs.get("email_account", "test@co")
        )
        db.session.add(correspondence)
        correspondence_id = correspondence.id

    email = Email(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        correspondence_id=correspondence_id,
        email_account=kwargs.get("email_account", "test@co"),
        email_subject=kwargs.get("email_subject", "test"),
        email_from=kwargs.get("email_from", "test@co"),
        email_to=kwargs.get("email_to", "test@co"),
        email_cc=kwargs.get("email_cc", ["test@co"]),
        email_body=kwargs.get("email_body", "test"),
        type=kwargs.get("type", EmailType.ROOT),
        attachments=kwargs.get("attachments"),
        email_sent_at=kwargs.get("email_sent_at"),
        email_reply_to=kwargs.get("email_reply_to"),
        email_attachments_count=len(kwargs.get("attachments", [])),
        was_sent=kwargs.get("was_sent", True),
        email_tracking_id=kwargs.get("email_tracking_id", uuid.uuid4()),
        email_delivered=kwargs.get("email_delivered"),
        send_email_error=kwargs.get("send_email_error"),
        is_processed=kwargs.get("is_processed"),
        in_reply_to=kwargs.get("in_reply_to"),
        message_id=kwargs.get("message_id"),
        normalized_subject=kwargs.get("normalized_subject"),
        email_references=kwargs.get("email_references"),
        email_sender=kwargs.get("email_sender"),
        is_embedded=kwargs.get("is_embedded"),
        is_deleted=kwargs.get("is_deleted"),
    )

    db.session.add(email)
    return email


def email_status_fixture(**kwargs) -> EmailStatus:
    id = kwargs.get("id") or uuid.uuid4()
    email_status = EmailStatus(
        id=id,
        email_id=kwargs["email_id"],
        status=kwargs.get("status", "Unknown"),
        status_details=kwargs.get("status_details"),
        update_timestamp=kwargs.get("update_timestamp", datetime.utcnow()),
    )
    db.session.add(email_status)
    return email_status


def lob_carrier_fixture(**kwargs):
    id = kwargs.get("id") or uuid.uuid4()
    lob_carrier = LOBCarrier(
        id=id,
        line_of_business=kwargs.get("line_of_business"),
        lob_raw=kwargs.get("lob_raw"),
        carrier=kwargs.get("carrier"),
    )
    db.session.add(lob_carrier)
    return lob_carrier


def submission_history_fixture(**kwargs):
    submission_history = SubmissionHistory(
        submission_id=kwargs["submission_id"],
        submission_action_type=kwargs["submission_action_type"],
        created_at=kwargs.get("created_at", datetime.utcnow()),
        occurred_at=kwargs.get("occurred_at", datetime.utcnow()),
        additional_data=kwargs.get("additional_data", {}),
        actual_user=kwargs.get("actual_user"),
    )
    db.session.add(submission_history)
    return submission_history


def metric_preference_fixture(**kwargs) -> MetricPreference:
    metric_preference: MetricPreference = MetricPreference(
        id=str(kwargs.get("id", uuid.uuid4())),
        report_id=str(kwargs.get("report_id", uuid.uuid4())),
        parent_id=str(kwargs.get("parent_id")),
        parent_type=kwargs.get("parent_type"),
        children_type=kwargs.get("children_type"),
        summary_config_id=kwargs.get("summary_config_id"),
        is_applicable=kwargs.get("is_applicable", False),
        is_enabled=kwargs.get("is_enabled", False),
        is_submitted_data=kwargs.get("is_submitted_data", False),
        display_name=kwargs.get("display_name"),
        metric_group_name=kwargs.get("metric_group_name"),
        only_discovered_in=kwargs.get("only_discovered_in"),
        only_from_relationships=kwargs.get("only_from_relationships"),
    )
    db.session.add(metric_preference)
    return metric_preference


def metric_v2_fixture(**kwargs) -> MetricV2:
    metric: MetricV2 = MetricV2(
        id=str(kwargs.get("id", uuid.uuid4())),
        report_v2_id=str(kwargs.get("report_v2_id")),
        execution_id=str(kwargs.get("execution_id", uuid.uuid4())),
        metric_type=kwargs.get("metric_type", MetricType.MEAN),
        name=kwargs.get("name", "test metric"),
        parent_id=kwargs.get("parent_id", None),
        parent_type=kwargs.get("parent_type", None),
        children_type=kwargs.get("children_type", "BUSINESS"),
        summary_config_id=kwargs.get("summary_config_id", None),
        value_business_ids=kwargs.get("value_business_ids", None),
        value_parent_ids=kwargs.get("value_parent_ids", None),
        value_parent_types=kwargs.get("value_parent_types", None),
        float_values=kwargs.get("float_values", None),
        datetime_values=kwargs.get("datetime_values", None),
        string_values=kwargs.get("string_values", None),
        units=kwargs.get("units", None),
        list_item_type=kwargs.get("list_item_type", None),
        minimums=kwargs.get("minimums", None),
        maximums=kwargs.get("maximums", None),
        distance_threshold=kwargs.get("distance_threshold", None),
        submission_business_id=kwargs.get("submission_business_id", None),
    )

    db.session.add(metric)
    return metric


def metric_source_fixture(**kwargs) -> MetricSource:
    id = kwargs.get("id") or uuid.uuid4()
    metric_source: MetricSource = MetricSource(
        id=id,
        metric_v2_id=str(kwargs.get("metric_v2_id")),
        type=kwargs.get("type", "test_type"),
        properties=kwargs.get("properties", {}),
    )
    db.session.add(metric_source)
    return metric_source


def submission_coverage_fixture(**kwargs) -> SubmissionCoverage:
    id = kwargs.get("id") or uuid.uuid4()
    submission_coverage: SubmissionCoverage = SubmissionCoverage(
        id=id,
        coverage_id=str(kwargs["coverage_id"]),
        estimated_premium=kwargs.get("estimated_premium"),
        is_quoted=kwargs.get("is_quoted", False),
        quoted_premium=kwargs.get("quoted_premium"),
        rating_premium=kwargs.get("rating_premium"),
        bound_premium=kwargs.get("bound_premium"),
        limit=kwargs.get("limit"),
        submission_id=str(kwargs["submission_id"]),
        coverage_type=kwargs.get("coverage_type"),
        total_premium=kwargs.get("total_premium"),
        source=kwargs.get("source"),
        attachment_point=kwargs.get("attachment_point"),
        self_insurance_retention=kwargs.get("self_insurance_retention"),
        source_details=kwargs.get("source_details"),
        period=kwargs.get("period"),
        other_terms=kwargs.get("other_terms"),
        additional_data=kwargs.get("additional_data", {}),
        stage=kwargs.get("stage"),
    )
    if kwargs.get("coverage"):
        submission_coverage.coverage = kwargs["coverage"]
    db.session.add(submission_coverage)
    return submission_coverage


def underlying_policy_fixture(**kwargs) -> UnderlyingPolicy:
    id = kwargs.get("id") or uuid.uuid4()
    underlying_policy: UnderlyingPolicy = UnderlyingPolicy(
        id=id,
        submission_id=str(kwargs["submission_id"]),
        details=kwargs.get("details", {}),
    )
    db.session.add(underlying_policy)
    return underlying_policy


def support_user_fixture(**kwargs) -> SupportUser:
    item_id = kwargs.get("id") or uuid.uuid4()
    user_id = int(kwargs.get("user_id")) or user_fixture().id
    support_user = SupportUser(
        id=item_id,
        user_id=user_id,
        last_action_at=kwargs.get("last_action_at", datetime.now()),
        hourly_rate=kwargs.get("hourly_rate", 10.0),
        can_load_construction=kwargs.get("can_load_construction", True),
        current_report_id=kwargs.get("current_report_id"),
        can_load_submissions=kwargs.get("can_load_submissions", True),
        can_assign_naics=kwargs.get("can_assign_naics"),
        is_tier_2=kwargs.get("is_tier_2"),
        is_highly_accurate=kwargs.get("is_highly_accurate"),
    )
    db.session.add(support_user)
    return support_user


def support_user_report_fixture(**kwargs) -> SupportUserReport:
    id = kwargs.get("id") or uuid.uuid4()
    support_user_report = SupportUserReport(
        id=id,
        support_user_id=kwargs.get("support_user_id"),
        report_id=kwargs.get("report_id"),
        report_assigned_by_id=kwargs.get("report_assigned_by_id"),
        created_at=kwargs.get("created_at", datetime.now()),
    )
    db.session.add(support_user_report)
    return support_user_report


def execution_event_fixture(**kwargs) -> ExecutionEvent:
    item_id = kwargs.get("id") or uuid.uuid4()
    execution_event = ExecutionEvent(
        id=item_id,
        report_id=kwargs.get("report_id", uuid.uuid4()),
        event_type=kwargs.get("event_type", ExecutionEventType.SUCCEEDED),
        occurred_at=kwargs.get("occurred_at", datetime.now()),
        execution_id=kwargs.get("execution_id", uuid.uuid4()),
    )
    db.session.add(execution_event)
    return execution_event


def file_metric_fixture(**kwargs) -> FileMetric:
    file_metric = FileMetric(
        id=kwargs.get("id", uuid.uuid4()),
        file_id=kwargs.get("file_id", uuid.uuid4()),
        metric_name=kwargs.get("metric_name", uuid.uuid4()),
        metric_value=kwargs.get("metric_value", uuid.uuid4()),
        score_calculation=kwargs.get("score_calculation"),
    )
    db.session.add(file_metric)
    return file_metric


def user_group_fixture(**kwargs) -> UserGroup:
    user_group = UserGroup(
        id=kwargs.get("id", uuid.uuid4()),
        name=kwargs.get("name", uuid.uuid4()),
        organization_id=kwargs.get("organization_id", 1),
    )
    db.session.add(user_group)
    return user_group


def submission_note_fixture(**kwargs) -> SubmissionNote:
    id = kwargs.get("id") or uuid.uuid4()
    note = SubmissionNote(
        id=id,
        submission_id=kwargs.get("submission_id", uuid.uuid4()),
        rule_id=kwargs.get("rule_id", uuid.uuid4()),
        author_id=kwargs.get("author_id", 1),
        text=kwargs.get("note", "test note"),
        last_edit_by_id=kwargs.get("last_edit_by_id"),
        is_editable=kwargs.get("is_editable"),
        referred_to_user_ids=kwargs.get("referred_to_user_ids"),
        referrals_closed_to_user_ids=kwargs.get("referrals_closed_to_user_ids"),
        hashed_text=hash(kwargs.get("note", "test note")),
        canonical_id=kwargs.get("canonical_id"),
        is_note=kwargs.get("is_note"),
        html_content=kwargs.get("html_content"),
    )

    db.session.add(note)
    return note


def bowhead_uw_mapping_fixture(**kwargs) -> BowheadUWMapping:
    id = kwargs.get("id") or uuid.uuid4()
    mapping = BowheadUWMapping(
        id=id,
        broker_name=kwargs.get("broker_name", "Test Brokerage"),
        broker_email=kwargs.get("broker_email"),
        user_id=kwargs["user_id"],
        broker_id=kwargs.get("broker_id"),
        org_group=kwargs["org_group"],
    )

    db.session.add(mapping)
    return mapping


def conifer_uw_mapping_fixture(**kwargs) -> BossUWMapping:
    id = kwargs.get("id") or uuid.uuid4()
    mapping = ConiferUWMapping(
        id=id,
        brokerage_name=kwargs.get("extract_broker_name", "Test Broker"),
        broker_name=kwargs.get("extract_brokerage_name", "Test Brokerage"),
        broker_email=kwargs.get("broker_email"),
        org_group=kwargs.get("org_group"),
        user_id=kwargs["user_id"],
        broker_id=kwargs.get("broker_id"),
        brokerage_domain=kwargs.get("brokerage_domain"),
    )

    db.session.add(mapping)
    return mapping


def boss_uw_mapping_fixture(**kwargs) -> BossUWMapping:
    id = kwargs.get("id") or uuid.uuid4()
    mapping = BossUWMapping(
        id=id,
        extract_broker_name=kwargs.get("extract_broker_name", "Test Broker"),
        extract_brokerage_name=kwargs.get("extract_brokerage_name", "Test Brokerage"),
        broker_v2_id=kwargs.get("broker_v2_id"),
        state=kwargs.get("state"),
        naics_code=kwargs["naics_code"],
        coverage_type=kwargs.get("coverage_type", CoverageType.PRIMARY),
        extract_uw_name=kwargs.get("extract_uw_name", "Test Underwriter"),
        user_id=kwargs["user_id"],
    )

    db.session.add(mapping)
    return mapping


def paragon_wc_uw_mapping_fixture(**kwargs) -> ParagonWCUWMapping:
    id = kwargs.get("id") or uuid.uuid4()
    mapping = ParagonWCUWMapping(
        id=id,
        sheet_broker_name=kwargs.get("sheet_broker_name", "Test Broker"),
        sheet_broker_email=kwargs.get("sheet_broker_email", "<EMAIL>"),
        sheet_domain=kwargs.get("sheet_domain", "@kalepa.com"),
        sheet_brokerage_name=kwargs.get("sheet_brokerage_name", "Test Brokerage"),
        sheet_agency_family=kwargs.get("sheet_agency_family"),
        sheet_uw_name=kwargs.get("sheet_uw_name"),
        user_id=kwargs.get("user_id"),
        is_confirmed=kwargs.get("is_confirmed", True),
    )

    db.session.add(mapping)
    return mapping


def admiral_uw_mapping_fixture(**kwargs) -> AdmiralUWMapping:
    id_ = kwargs.get("id") or uuid.uuid4()
    mapping = AdmiralUWMapping(
        id=id_,
        broker_first_name=kwargs.get("broker_first_name", "Frater"),
        broker_last_name=kwargs.get("broker_last_name", "Imperator"),
        broker_email=kwargs.get("broker_email", "<EMAIL>"),
        broker_id=kwargs.get("broker_id", None),
        brokerage_name=kwargs.get("brokerage_name", "The Clergy"),
        producer_no=kwargs.get("producer_no", "G666"),
        producer_sequence_no=kwargs.get("producer_sequence_no", "SEQ69"),
    )
    db.session.add(mapping)
    return mapping


def admiral_uw_mapping_assignment_fixture(**kwargs) -> AdmiralUWMappingAssignment:
    id_ = kwargs.get("id") or uuid.uuid4()
    assignment = AdmiralUWMappingAssignment(
        id=id_,
        uw_mapping_id=kwargs["uw_mapping_id"],
        user_email=kwargs["user_email"],
        for_renewals=kwargs["for_renewals"],
        inbox=kwargs["inbox"],
    )
    db.session.add(assignment)
    return assignment


def secura_uw_mapping_fixture(**kwargs) -> SecuraUWMapping:
    id = kwargs.get("id") or uuid.uuid4()
    mapping = SecuraUWMapping(
        id=id,
        secura_excel_agency_code_raw=kwargs.get("secura_excel_agency_code_raw", "123456"),
        secura_excel_master_code_raw=kwargs.get("secura_excel_master_code_raw", "987"),
        secura_excel_lob_raw=kwargs.get("secura_excel_lob_raw", SecuraLobs.COMMERCIAL_CP),
        secura_excel_underwriter_raw=kwargs.get("secura_excel_underwriter_raw", "Test Underwriter"),
        secura_excel_agency_name_raw=kwargs.get("secura_excel_agency_name_raw", "Test Agency"),
        secura_excel_agency_city_raw=kwargs.get("secura_excel_agency_city_raw", "Test City"),
        secura_excel_agency_state_raw=kwargs.get("secura_excel_agency_state_raw", "CA"),
        secura_excel_agency_phone_raw=kwargs.get("secura_excel_agency_phone_raw", "(123) 4567890"),
        normalized_secura_underwriter=kwargs.get("normalized_secura_underwriter", "test underwriter"),
        normalized_secura_agency_name=kwargs.get("normalized_secura_agency_name", "test agency"),
        normalized_secura_agency_phone=kwargs.get("normalized_secura_agency_phone", "1234567890"),
        mapped_kalepa_user_id=kwargs.get("mapped_kalepa_user_id"),
        mapped_kalepa_brokerage_id=kwargs.get("mapped_kalepa_brokerage_id"),
        agency_domains=kwargs.get("agency_domains"),
        agency_domains_updated_at=kwargs.get("agency_domains_updated_at"),
        is_manual_override=kwargs.get("is_manual_override", False),
    )

    db.session.add(mapping)
    return mapping


def admiral_assignment_log_fixture(**kwargs) -> AdmiralAssignmentLog:
    id_ = kwargs.get("id") or uuid.uuid4()
    log = AdmiralAssignmentLog(
        id=id_,
        producer_no=kwargs["producer_no"],
        submission_id=kwargs["submission_id"],
        broker_email=kwargs["broker_email"],
        assigned_underwriter_email=kwargs["assigned_underwriter_email"],
    )
    db.session.add(log)
    return log


def admiral_individual_assignment_fixture(**kwargs) -> AdmiralIndividualAssignment:
    assignment = AdmiralIndividualAssignment(
        broker_email=kwargs.get("broker_email", "<EMAIL>"),
        inbox=kwargs.get("inbox", "<EMAIL>"),
        uw_email=kwargs.get("uw_email", "<EMAIL>"),
    )
    db.session.add(assignment)
    return assignment


def workers_comp_state_rating_info_fixture(**kwargs) -> WorkersCompStateRatingInfo:
    rating_info = WorkersCompStateRatingInfo(
        id=kwargs.get("id", uuid.uuid4()),
        state=kwargs.get("state", "CA"),
        location_number=kwargs.get("location_number", 1),
        class_code=kwargs.get("class_code", "1234-5"),
        description_code=kwargs.get("description_code", "A"),
        category=kwargs.get("category", "Test category"),
        full_time_employees=kwargs.get("full_time_employees", 3),
        part_time_employees=kwargs.get("part_time_employees", 5),
        sic=kwargs.get("sic", "SIC"),
        naics=kwargs.get("naics", "123456"),
        payroll=kwargs.get("payroll", 2312.32),
        rate=kwargs.get("rate", 2.3),
        est_annual_manual_premium=kwargs.get("est_annual_manual_premium", 10000.32),
        file_id=kwargs["file_id"],
        submission_id=kwargs["submission_id"],
        number_of_employees=kwargs.get("number_of_employees"),
        submission_business_id=kwargs.get("submission_business_id"),
    )
    db.session.add(rating_info)
    return rating_info


def ifta_data_fixture(**kwargs) -> IFTAData:
    ifta_data = IFTAData(
        id=str(kwargs.get("id", uuid.uuid4())),
        jurisdiction=kwargs.get("jurisdiction", "CA"),
        date=kwargs.get("date", datetime.utcnow()),
        total_miles=kwargs.get("total_miles"),
        taxable_gallons=kwargs.get("taxable_gallons"),
        file_id=str(kwargs["file_id"]),
        submission_id=str(kwargs["submission_id"]),
        matched_total=kwargs.get("matched_total"),
    )
    db.session.add(ifta_data)
    return ifta_data


def sensible_extraction_fixture(**kwargs) -> SensibleExtraction:
    sensible_extraction = SensibleExtraction(
        id=str(kwargs.get("id", uuid.uuid4())),
        submission_id=str(kwargs["submission_id"]),
        file_id=str(kwargs["file_id"]),
        status=kwargs.get("status", SensibleUploadStatus.RESPONSE_RECEIVED),
        upload_error=kwargs.get("upload_error"),
        extraction_id=kwargs.get("extraction_id"),
        extraction_status=str(kwargs.get("extraction_status")),
        number_of_pages=kwargs.get("number_of_pages"),
        extraction_errors=kwargs.get("extraction_errors", []),
    )
    db.session.add(sensible_extraction)
    return sensible_extraction


def sensible_extraction_document_fixture(**kwargs) -> SensibleExtractionDocument:
    sensible_extraction_document = SensibleExtractionDocument(
        id=str(kwargs.get("id", uuid.uuid4())),
        extraction_id=str(kwargs["extraction_id"]),
        document_type=kwargs.get("document_type"),
        configuration=kwargs.get("configuration"),
        start_page=kwargs.get("start_page", 0),
        end_page=kwargs.get("end_page", 0),
        validations=kwargs.get("validations", {}),
        errors=kwargs.get("errors", {}),
        classification_summary=kwargs.get("classification_summary", {}),
        validation_summary=kwargs.get("validation_summary", {}),
    )
    db.session.add(sensible_extraction_document)
    return sensible_extraction_document


def paragon_underwriter_fixture(**kwargs) -> ParagonUnderwriter:
    id = kwargs.get("id", uuid.uuid4())
    email = kwargs.get("email", "<EMAIL>")
    username = kwargs.get("username", "paragon_underwriter")
    kalepa_user_id = kwargs.get("kalepa_user_id", 1)
    ims_underwriter_id = kwargs.get("ims_underwriter_id", 123)
    if paragon_underwriter := ParagonUnderwriter.query.get(id):
        return paragon_underwriter
    paragon_underwriter = ParagonUnderwriter(
        id=id,
        username=username,
        email=email,
        kalepa_user_id=kalepa_user_id,
        ims_underwriter_id=ims_underwriter_id,
        ims_underwriter_guid=kwargs.get("ims_underwriter_guid"),
        fax=kwargs.get("fax"),
        mobile=kwargs.get("mobile"),
        first_name=kwargs.get("first_name"),
        last_name=kwargs.get("last_name"),
        title=kwargs.get("title"),
        office=kwargs.get("office"),
        phone=kwargs.get("phone"),
    )
    db.session.add(paragon_underwriter)
    return paragon_underwriter


def paragon_company_line_fixture(**kwargs) -> ParagonCompanyLine:
    id = kwargs.get("id", uuid.uuid4())
    company_line_guid = kwargs.get("company_line_guid", uuid.uuid4())
    company_location_guid = kwargs.get("company_location_guid", uuid.uuid4())
    line_guid = kwargs.get("line_guid", uuid.uuid4())
    office_guid = kwargs.get("office_guid", uuid.uuid4())
    state_id = kwargs.get("state_id", "CA")
    location_name = kwargs.get("location_name", "Test Location")
    line_name = kwargs.get("line_name", "Test Line")
    quoting_and_issuing_office_name = kwargs.get("quoting_and_issuing_office_name", "Test Office")
    office_default_cost_center_id = kwargs.get("office_default_cost_center_id", 1)
    office_default_cost_center_name = kwargs.get("office_default_cost_center_name", "Test Cost Center")
    company_default_cost_center_id = kwargs.get("company_default_cost_center_id", 1)
    company_default_cost_center_name = kwargs.get("company_default_cost_center_name", "Test Cost Center")
    billing_type_id = kwargs.get("billing_type_id", 9)
    billing_type_name = kwargs.get("billing_type_name", "Test Billing Type")

    if paragon_company_line := ParagonUnderwriter.query.get(id):
        return paragon_company_line
    paragon_company_line = ParagonCompanyLine(
        id=id,
        company_line_guid=str(company_line_guid),
        company_location_guid=str(company_location_guid),
        line_guid=str(line_guid),
        office_guid=str(office_guid),
        state_id=state_id,
        location_name=location_name,
        line_name=line_name,
        quoting_and_issuing_office_name=quoting_and_issuing_office_name,
        office_default_cost_center_id=office_default_cost_center_id,
        office_default_cost_center_name=office_default_cost_center_name,
        company_default_cost_center_id=company_default_cost_center_id,
        company_default_cost_center_name=company_default_cost_center_name,
        billing_type_id=billing_type_id,
        billing_type_name=billing_type_name,
        company_license=kwargs.get("company_license"),
    )
    db.session.add(paragon_company_line)
    return paragon_company_line


def workers_comp_experience_fixture(**kwargs) -> WorkersCompExperience:
    id = kwargs.get("id") or uuid.uuid4()
    wce = WorkersCompExperience(
        id=id,
        risk_name=kwargs.get("risk_name", "Test risk name"),
        risk_id=kwargs.get("risk_id"),
        combination_code=kwargs.get("combination_code"),
        risk_address=kwargs.get("risk_address"),
        risk_city=kwargs.get("risk_city"),
        rating_effective_date=kwargs.get("rating_effective_date", datetime.utcnow()),
        rating_effective_date_to=kwargs.get("rating_effective_date_to"),
        experience_period_date_from=kwargs.get("experience_period_date_from"),
        experience_period_date_to=kwargs.get("experience_period_date_to"),
        production_date=kwargs.get("production_date"),
        state=kwargs.get("state", "NY"),
        actual_primary_losses=kwargs.get("actual_primary_losses"),
        actual_stabilizing_value=kwargs.get("actual_stabilizing_value"),
        actual_ratable_excess=kwargs.get("actual_ratable_excess"),
        actual_totals=kwargs.get("actual_totals"),
        actual_losses=kwargs.get("actual_losses"),
        expected_primary_losses=kwargs.get("expected_primary_losses"),
        expected_excess_losses=kwargs.get("expected_excess_losses"),
        expected_losses=kwargs.get("expected_losses"),
        modified_primary_losses=kwargs.get("modified_primary_losses"),
        modified_excess_losses=kwargs.get("modified_excess_losses"),
        modified_losses=kwargs.get("modified_losses"),
        expected_stabilizing_value=kwargs.get("expected_stabilizing_value"),
        expected_ratable_excess=kwargs.get("expected_ratable_excess"),
        expected_totals=kwargs.get("expected_totals"),
        experience_modification=kwargs.get("experience_modification"),
        combinable_group_id=kwargs.get("combinable_group_id"),
        issue_date=kwargs.get("issue_date"),
        status=kwargs.get("status"),
        calculated_experience_modification=kwargs.get("calculated_experience_modification"),
        capped_debit_modification=kwargs.get("capped_debit_modification"),
        transitional_modification_factor=kwargs.get("transitional_modification_factor"),
        number_of_claims=kwargs.get("number_of_claims"),
        type=kwargs.get("type", "Test type"),
        state_list=kwargs.get("state_list"),
        file_id=kwargs.get("file_id", uuid.uuid4()),
        submission_id=kwargs.get("submission_id", uuid.uuid4()),
        source=kwargs.get("source", "FILE"),
    )
    db.session.add(wce)
    return wce


def scheduled_email_fixture(**kwargs) -> ScheduledEmail:
    id = kwargs.get("id") or uuid.uuid4()
    submission = kwargs.get("submission")
    user = kwargs.get("user")
    template = kwargs.get("template")
    if "submission_id" in kwargs:
        submission = Submission.query.get(kwargs["submission_id"])
    if "user_id" in kwargs:
        user = User.query.get(kwargs["user_id"])
    if "template_id" in kwargs:
        template = EmailTemplate.query.get(kwargs["template_id"])
    res = ScheduledEmail(
        id=id,
        submission_id=kwargs.get("submission_id"),
        rule_name=kwargs.get("rule_name", "rule"),
        template_id=kwargs.get("template_id"),
        user_id=kwargs.get("user_id"),
        submission=submission,
        user=user,
        template=template,
        created_at=kwargs.get("created_at", datetime.now()),
    )

    db.session.add(res)
    return res


def sensible_quota_fixture(**kwargs) -> SensibleQuota:
    res = SensibleQuota(
        id=kwargs.get("id", uuid.uuid4()),
        organization_id=kwargs.get("organization_id", 1),
        quota=kwargs.get("quota", 1),
    )
    db.session.add(res)
    return res


def ask_questions_fixture(**kwargs) -> AskQuestions:
    id = kwargs.get("id") or uuid.uuid4()
    ask_questions = AskQuestions(
        id=id,
        submission_id=kwargs.get("submission_id"),
        question=kwargs.get("question"),
        answer=kwargs.get("answer"),
    )
    db.session.add(ask_questions)
    return ask_questions


def audit_trail_fixture(**kwargs) -> AuditTrail:
    id = kwargs.get("id") or 1
    audit_trail = AuditTrail(
        id=id,
        created_at=kwargs.get("created_at"),
        row_id=kwargs.get("row_id", 1),
        new_value=kwargs.get("new_value"),
        table_name=kwargs.get("table_name", "table_name"),
        user_external_id=kwargs.get("user_external_id", "asd"),
        column_name=kwargs.get("column_name", "column_name"),
    )
    db.session.add(audit_trail)
    return audit_trail


def client_application_fixture(**kwargs) -> ClientApplication:
    id = kwargs.get("id") or uuid.uuid4()
    client_application = ClientApplication(
        id=id,
        organization_id=kwargs.get("organization_id"),
        created_at=kwargs.get("created_at"),
        updated_at=kwargs.get("updated_at"),
        name=kwargs.get("name"),
        client_id=kwargs.get("client_id"),
        client_secret=kwargs.get("client_secret"),
    )
    db.session.add(client_application)
    return client_application


def report_email_correspondence_fixture(db_flush=True, **kwargs) -> ReportEmailCorrespondence:
    id = kwargs.get("id") or uuid.uuid4()
    report_email_correspondence = ReportEmailCorrespondence(
        id=id,
        thread_id=kwargs.get("thread_id"),
        active_email_template_id=kwargs.get("active_email_template_id"),
        email_cc=kwargs.get("email_cc"),
        recipient_address=kwargs.get("recipient_address"),
        email_account=kwargs.get("email_account"),
    )
    db.session.add(report_email_correspondence)
    if db_flush:
        db.session.flush()
    return report_email_correspondence


def report_bundle_fixture(**kwargs) -> ReportBundle:
    id = kwargs.get("id") or uuid.uuid4()
    report_bundle = ReportBundle(
        id=id,
    )
    db.session.add(report_bundle)
    return report_bundle


def feedback_fixture(**kwargs) -> Feedback:
    id = kwargs.get("id") or uuid.uuid4()
    feedback = Feedback(
        id=id,
        business_id=kwargs.get("business_id"),
        value=kwargs.get("value"),
        user_id=kwargs.get("user_id"),
        is_trusted=kwargs.get("is_trusted"),
        user_feedback=kwargs.get("user_feedback"),
        attribute_name=kwargs.get("attribute_name"),
        object_id=kwargs.get("object_id"),
        object_type=kwargs.get("object_type"),
        user_origin=kwargs.get("user_origin"),
    )
    db.session.add(feedback)
    return feedback


def lob_fixture(**kwargs) -> Lob:
    id = kwargs.get("id") or uuid.uuid4()
    lob = Lob(
        id=id,
        display_name=kwargs.get("display_name"),
        tabs=kwargs.get("tabs"),
    )
    db.session.add(lob)
    return lob


def copilot_worker_execution_event_fixture(**kwargs) -> CopilotWorkerExecutionEvent:
    id = kwargs.get("id") or uuid.uuid4()
    copilot_worker_execution_event = CopilotWorkerExecutionEvent(
        id=id,
        submission_id=kwargs.get("submission_id"),
        submission_business_id=kwargs.get("submission_business_id"),
        event_type=kwargs.get("event_type"),
        execution_id=kwargs.get("execution_id"),
        old_parent_id=kwargs.get("old_parent_id"),
        execution_type=kwargs.get("execution_type"),
        first_party_field_original_name=kwargs.get("first_party_field_original_name"),
        occurred_at=kwargs.get("occurred_at"),
    )
    db.session.add(copilot_worker_execution_event)
    return copilot_worker_execution_event


def file_label_studio_fixture(**kwargs) -> FileLabelStudio:
    id = kwargs.get("id") or uuid.uuid4()
    file_label_studio = FileLabelStudio(
        id=id,
        processed_data=kwargs.get("processed_data"),
        file_id=kwargs.get("file_id"),
        total_pages=kwargs.get("total_pages"),
        page_num=kwargs.get("page_num"),
        status=kwargs.get("status"),
        task_id=kwargs.get("task_id"),
        image_s3_key=kwargs.get("image_s3_key"),
    )
    db.session.add(file_label_studio)
    return file_label_studio


def hub_template_fixture(**kwargs) -> HubTemplate:
    id = kwargs.get("id") or uuid.uuid4()
    hub_template = HubTemplate(
        id=id,
        user_id=kwargs.get("user_id"),
        template=kwargs.get("template"),
        template_type=kwargs.get("template_type", HubTemplateType.HUB),
        is_default=kwargs.get("is_default"),
        name=kwargs.get("name"),
    )
    db.session.add(hub_template)
    return hub_template


def loss_run_sensible_extraction_document_fixture(**kwargs) -> LossRunSensibleExtractionDocument:
    id = kwargs.get("id") or uuid.uuid4()
    loss_run_sensible_extraction_document = LossRunSensibleExtractionDocument(
        id=id,
        extracted_claims=kwargs.get("extracted_claims"),
        invalid_claims=kwargs.get("invalid_claims"),
        not_loadable_claims=kwargs.get("not_loadable_claims"),
        file_id=kwargs.get("file_id"),
        duplicated_claims=kwargs.get("duplicated_claims"),
        same_file_duplicated_claims=kwargs.get("same_file_duplicated_claims"),
    )
    db.session.add(loss_run_sensible_extraction_document)
    return loss_run_sensible_extraction_document


def metric_group_fixture(**kwargs) -> MetricGroup:
    id = kwargs.get("id") or uuid.uuid4()
    metric_group = MetricGroup(
        id=id,
        display_name=kwargs.get("display_name"),
    )
    db.session.add(metric_group)
    return metric_group


def metric_template_fixture(**kwargs) -> MetricTemplate:
    id = kwargs.get("id") or uuid.uuid4()
    metric_template = MetricTemplate(
        id=id,
        user_id=kwargs.get("user_id"),
        is_shared=kwargs.get("is_shared"),
        name=kwargs.get("name"),
    )
    db.session.add(metric_template)
    return metric_template


def metric_preferences_templates_fixture(**kwargs) -> MetricPreferencesTemplates:
    id = kwargs.get("id") or uuid.uuid4()
    metric_preferences_templates = MetricPreferencesTemplates(
        id=id,
        template_id=kwargs.get("template_id"),
        metric_preferences_id=kwargs.get("metric_preferences_id"),
    )
    db.session.add(metric_preferences_templates)
    return metric_preferences_templates


def naics_code_fixture(**kwargs) -> NAICSCode:
    id = kwargs.get("id") or uuid.uuid4()
    naics_code = NAICSCode(
        id=id,
        title=kwargs.get("title"),
        description=kwargs.get("description"),
    )
    db.session.add(naics_code)
    return naics_code


def notebook_thread_fixture(**kwargs) -> NotebookThread:
    id = kwargs.get("id") or uuid.uuid4()
    notebook_thread = NotebookThread(
        id=id,
        submission_id=kwargs.get("submission_id"),
        relates_to=kwargs.get("relates_to"),
        submission_business_id=kwargs.get("submission_business_id"),
        is_deleted=kwargs.get("is_deleted"),
        dossier_component_paths=kwargs.get("dossier_component_paths"),
        copilot_component_paths=kwargs.get("copilot_component_paths"),
    )
    db.session.add(notebook_thread)
    return notebook_thread


def notebook_message_fixture(**kwargs) -> NotebookMessage:
    id = kwargs.get("id") or uuid.uuid4()
    notebook_message = NotebookMessage(
        id=id,
        is_deleted=kwargs.get("is_deleted"),
        author_id=kwargs.get("author_id"),
        thread_id=kwargs.get("thread_id"),
        body=kwargs.get("body"),
    )
    db.session.add(notebook_message)
    return notebook_message


def org_metric_config_fixture(**kwargs) -> OrgMetricConfig:
    id = kwargs.get("id") or uuid.uuid4()
    org_metric_config = OrgMetricConfig(
        id=id,
        organization_id=kwargs.get("organization_id"),
        weight=kwargs.get("weight"),
        metric_name=kwargs.get("metric_name"),
    )
    db.session.add(org_metric_config)
    return org_metric_config


def quality_audit_questions_fixture(**kwargs) -> QualityAuditQuestion:
    id = kwargs.get("id") or uuid.uuid4()
    quality_audit_questions = QualityAuditQuestion(
        id=id,
        submission_id=kwargs.get("submission_id"),
        question=kwargs.get("question"),
        answer=kwargs.get("answer"),
    )
    db.session.add(quality_audit_questions)
    return quality_audit_questions


def read_submission_fixture(**kwargs) -> ReadSubmission:
    read_submission = ReadSubmission(
        submission_id=kwargs.get("submission_id"),
        user_id=kwargs.get("user_id"),
    )
    db.session.add(read_submission)
    return read_submission


def report_alert_fixture(**kwargs) -> ReportAlert:
    id = kwargs.get("id") or uuid.uuid4()
    report_alerts = ReportAlert(
        id=id,
        report_id=kwargs.get("report_id"),
        alert_type=kwargs.get("alert_type"),
    )
    db.session.add(report_alerts)
    return report_alerts


def report_link_fixture(**kwargs) -> ReportLink:
    report_link = ReportLink(
        report_1_id=kwargs.get("report_1_id"),
        report_2_id=kwargs.get("report_2_id"),
    )
    db.session.add(report_link)
    return report_link


def summary_preference_fixture(**kwargs) -> SummaryPreference:
    id = kwargs.get("id") or uuid.uuid4()
    summary_preference = SummaryPreference(
        id=id,
        is_default=kwargs.get("is_default"),
        metric_type=kwargs.get("metric_type"),
        display_name=kwargs.get("display_name"),
        group_display_name=kwargs.get("group_display_name"),
        icon_name=kwargs.get("icon_name"),
    )
    db.session.add(summary_preference)
    return summary_preference


def report_summary_preference_fixture(**kwargs) -> ReportSummaryPreference:
    id = kwargs.get("id") or uuid.uuid4()
    report_summary_preference = ReportSummaryPreference(
        id=id,
        summary_preference_id=kwargs.get("summary_preference_id"),
        report_v2_id=kwargs.get("report_v2_id"),
    )
    db.session.add(report_summary_preference)
    return report_summary_preference


def report_triage_fixture(**kwargs) -> ReportTriage:
    id = kwargs.get("id") or uuid.uuid4()
    report_triage = ReportTriage(
        id=id,
        report_id=kwargs.get("report_id"),
        email_sent=kwargs.get("email_sent"),
        result=kwargs.get("result"),
        explanations=kwargs.get("explanations"),
    )
    db.session.add(report_triage)
    return report_triage


def routing_rule_fixture(**kwargs) -> RoutingRule:
    id = kwargs.get("id") or uuid.uuid4()
    routing_rule = RoutingRule(
        id=id,
        organization_id=kwargs.get("organization_id"),
        user_id=kwargs.get("user_id"),
        is_rush=kwargs.get("is_rush"),
        order=kwargs.get("order"),
        contained_phrases=kwargs.get("contained_phrases"),
        skip_if_previous_rule_was_hit=kwargs.get("skip_if_previous_rule_was_hit"),
        tier=kwargs.get("tier"),
        tag=kwargs.get("tag"),
        not_contained_words=kwargs.get("not_contained_words"),
        contained_words=kwargs.get("contained_words"),
        is_case_sensitive=kwargs.get("is_case_sensitive"),
        words_threshold=kwargs.get("words_threshold"),
        only_email_body=kwargs.get("only_email_body"),
    )
    db.session.add(routing_rule)
    return routing_rule


def sensible_calls_fixture(**kwargs) -> SensibleCalls:
    id = kwargs.get("id") or uuid.uuid4()
    sensible_calls = SensibleCalls(
        id=id,
        organization_id=kwargs.get("organization_id"),
        year=kwargs.get("year"),
        month=kwargs.get("month"),
        day=kwargs.get("day"),
        calls_made=kwargs.get("calls_made"),
    )
    db.session.add(sensible_calls)
    return sensible_calls


def sensible_document_response_cache_fixture(**kwargs) -> SensibleDocumentResponseCache:
    id = kwargs.get("id") or uuid.uuid4()
    sensible_document_response_cache = SensibleDocumentResponseCache(
        id=id,
        response=kwargs.get("response"),
        file_ids=kwargs.get("file_ids"),
        configurations=kwargs.get("configurations"),
    )
    db.session.add(sensible_document_response_cache)
    return sensible_document_response_cache


def stuck_details_fixture(**kwargs) -> StuckDetails:
    id = kwargs.get("id") or uuid.uuid4()
    stuck_details = StuckDetails(
        id=id,
        submission_id=kwargs.get("submission_id"),
        processing_state=kwargs.get("processing_state"),
        stuck_at=kwargs.get("stuck_at"),
        unstuck_at=kwargs.get("unstuck_at"),
        stuck_by=kwargs.get("stuck_by"),
        was_correct_to_stuck=kwargs.get("was_correct_to_stuck"),
        stuck_reason_buckets=kwargs.get("stuck_reason_buckets"),
        stuck_reason_explanation=kwargs.get("stuck_reason_explanation"),
    )
    db.session.add(stuck_details)
    return stuck_details


def stuck_submission_feedback_fixture(**kwargs) -> StuckSubmissionFeedback:
    id = kwargs.get("id") or uuid.uuid4()
    stuck_submission_feedback = StuckSubmissionFeedback(
        id=id,
        submission_id=kwargs.get("submission_id"),
        file_id=kwargs.get("file_id"),
        user_id=kwargs.get("user_id"),
        resolution=kwargs.get("resolution"),
        issue=kwargs.get("issue"),
        stuck_issue=kwargs.get("stuck_issue"),
    )
    db.session.add(stuck_submission_feedback)
    return stuck_submission_feedback


def submission_bookmark_fixture(**kwargs) -> SubmissionBookmark:
    id = kwargs.get("id") or uuid.uuid4()
    submission_bookmark = SubmissionBookmark(
        id=id,
        submission_id=kwargs.get("submission_id"),
        user_id=kwargs.get("user_id"),
    )
    db.session.add(submission_bookmark)
    return submission_bookmark


def submission_clearing_issue_fixture(**kwargs) -> SubmissionClearingIssue:
    id = kwargs.get("id") or uuid.uuid4()
    submission_clearing_issue = SubmissionClearingIssue(
        id=id,
        submission_id=kwargs.get("submission_id"),
        suspected_report_id=kwargs.get("suspected_report_id"),
        is_resolved=kwargs.get("is_resolved"),
        is_light=kwargs.get("is_light"),
        reason=kwargs.get("reason"),
    )
    db.session.add(submission_clearing_issue)
    return submission_clearing_issue


def submission_deductible_fixture(**kwargs) -> SubmissionDeductible:
    id = kwargs.get("id") or uuid.uuid4()
    submission_deductible = SubmissionDeductible(
        id=id,
        coverage_id=kwargs.get("coverage_id"),
        submission_id=kwargs.get("submission_id"),
        policy_limit=kwargs.get("policy_limit"),
        policy_level=kwargs.get("policy_level"),
        policy_level_type=kwargs.get("policy_level_type"),
        minimum=kwargs.get("minimum"),
        comment=kwargs.get("comment"),
        coverage_type=kwargs.get("coverage_type"),
    )
    db.session.add(submission_deductible)
    return submission_deductible


def submission_priority_fixture(**kwargs) -> SubmissionPriority:
    id = kwargs.get("id") or uuid.uuid4()
    submission_priority = SubmissionPriority(
        id=id,
        submission_id=kwargs.get("submission_id"),
        escalated_at=kwargs.get("escalated_at"),
        removed_from_auto_assign=kwargs.get("removed_from_auto_assign"),
        overwritten_priority=kwargs.get("overwritten_priority"),
    )
    db.session.add(submission_priority)
    return submission_priority


def taxonomy_mapping_fixture(**kwargs) -> TaxonomyMapping:
    id = kwargs.get("id") or uuid.uuid4()
    description = kwargs.get("taxonomy_description") or taxonomy_description_fixture(
        description=kwargs.get("description", "new description"), code=kwargs.get("code")
    )
    taxonomy_mapping = TaxonomyMapping(
        id=id,
        mapping_type=kwargs.get("mapping_type"),
        code=kwargs.get("code"),
        mapped_codes=kwargs.get("mapped_codes"),
        description=description,
    )
    db.session.add(taxonomy_mapping)
    return taxonomy_mapping


def taxonomy_description_fixture(**kwargs) -> TaxonomyDescription:
    id = kwargs.get("id") or uuid.uuid4()
    taxonomy_description = TaxonomyDescription(
        id=id,
        code=kwargs.get("code"),
        code_type=kwargs.get("code_type"),
        description=kwargs.get("description"),
    )
    db.session.add(taxonomy_description)
    return taxonomy_description


def tenant_feedback_fixture(**kwargs) -> TenantFeedback:
    id = kwargs.get("id") or uuid.uuid4()
    tenant_feedback = TenantFeedback(
        id=id,
        is_not_at_premises=kwargs.get("is_not_at_premises"),
        user_id=kwargs.get("user_id"),
        is_trusted=kwargs.get("is_trusted"),
        premise_id=kwargs.get("premise_id"),
        business_id=kwargs.get("business_id"),
        open_date=kwargs.get("open_date"),
        closed_date=kwargs.get("closed_date"),
        business_name=kwargs.get("business_name"),
        formatted_address=kwargs.get("formatted_address"),
        owner_name=kwargs.get("owner_name"),
    )
    db.session.add(tenant_feedback)
    return tenant_feedback


def update_businesses_log_fixture(**kwargs) -> UpdateBusinessLog:
    id = kwargs.get("id") or uuid.uuid4()
    update_businesses_log = UpdateBusinessLog(
        id=id,
        new_id=kwargs.get("new_id"),
        old_ids=kwargs.get("old_ids"),
        submission_ids=kwargs.get("submission_ids"),
        affected_tables=kwargs.get("affected_tables"),
    )
    db.session.add(update_businesses_log)
    return update_businesses_log


def user_action_fixture(**kwargs) -> UserAction:
    id = kwargs.get("id") or uuid.uuid4()
    user_action = UserAction(
        id=id,
        user_id=kwargs.get("user_id"),
        action_type=kwargs.get("action_type"),
        context=kwargs.get("context"),
        action=kwargs.get("action"),
    )
    db.session.add(user_action)
    return user_action


def verification_check_fixture(**kwargs) -> VerificationCheckResult:
    id = kwargs.get("id") or uuid.uuid4()
    verification_check = VerificationCheckResult(
        id=id,
        is_hard_check=kwargs.get("is_hard_check"),
        run_id=kwargs.get("run_id"),
        status=kwargs.get("status"),
        manual=kwargs.get("manual"),
        force_manual=kwargs.get("force_manual"),
        force_verify=kwargs.get("force_verify"),
        submission_id=kwargs.get("submission_id"),
        name=kwargs.get("name"),
        error_message=kwargs.get("error_message"),
        stage=kwargs.get("stage"),
    )
    db.session.add(verification_check)
    return verification_check


def experiment_fixture(**kwargs) -> Experiment:
    id = kwargs.get("id") or uuid.uuid4()
    experiment = Experiment(
        id=id,
        name=kwargs.get("name", "name-" + str(id)),
        is_enabled=kwargs.get("is_enabled", True),
        settings=kwargs.get("settings", {"probability": 0.25}),
    )
    db.session.add(experiment)
    return experiment


def experiment_run_fixture(**kwargs) -> ExperimentRun:
    id = kwargs.get("id") or uuid.uuid4()
    experiment_run = ExperimentRun(
        id=id,
        experiment_id=kwargs.get("experiment_id"),
        submission_id=kwargs.get("submission_id"),
    )
    db.session.add(experiment_run)
    return experiment_run


def experiment_sample_fixture(**kwargs) -> ExperimentSample:
    id = kwargs.get("id") or uuid.uuid4()
    experiment_sample = ExperimentSample(
        id=id,
        experiment_run_id=kwargs.get("experiment_run_id"),
        user_id=kwargs.get("user_id"),
        type=kwargs.get("type", "ONBOARDED_FILE_ENTITY"),
        value_id=kwargs.get("value_id", "12345"),
        user_input=kwargs.get("user_input"),
        computed_value=kwargs.get("computed_value", 123),
        is_match=kwargs.get("is_match"),
    )
    db.session.add(experiment_sample)
    return experiment_sample


def sendgrid_email_data_fixture(**kwargs) -> SendgridEmailData:
    sendgrid_email_data = SendgridEmailData(
        id=kwargs.get("id", uuid.uuid4()),
        email_id=kwargs["email_id"],
        data=kwargs["data"],
    )
    db.session.add(sendgrid_email_data)
    return sendgrid_email_data


def report_permission_fixture(**kwargs) -> ReportPermission:
    report_permission = ReportPermission(
        id=kwargs.get("id", uuid.uuid4()),
        grantee_user_id=kwargs.get("grantee_user_id"),
        grantee_group_id=kwargs.get("grantee_group_id"),
        report_id=kwargs["report_id"],
        permission_type=kwargs.get("permission_type", PermissionType.OWNER),
        message=kwargs.get("message"),
        is_referral=kwargs.get("is_referral"),
    )
    db.session.add(report_permission)
    return report_permission


def brokerage_employee_fixture(**kwargs) -> BrokerageEmployee:
    if "brokerage_id" not in kwargs:
        brokerage = brokerage_fixture()
        kwargs["brokerage_id"] = brokerage.id

    org = None
    organizations = []
    if organization_id := kwargs.get("organization_id", None):
        if not Organization.query.get(organization_id):
            org = organization_fixture(id=organization_id)

    if not org:
        org = Organization.query.get(organization_id)
    if org:
        organizations.append(org)

    brokerage_employee = BrokerageEmployee(
        id=kwargs.get("id", uuid.uuid4()),
        brokerage_id=kwargs["brokerage_id"],
        roles=kwargs.get("roles", [BrokerageEmployeeRoles.AGENT]),
        organizations=organizations,
        name=kwargs.get("name", "Walter Hartwell White"),
        email=kwargs.get("email", "<EMAIL>"),
        aliases=kwargs.get("aliases", []),
    )

    db.session.add(brokerage_employee)
    return brokerage_employee


def submission_brokerage_employee_fixture(**kwargs) -> SubmissionBrokerageEmployee:
    submission_brokerage_employee = SubmissionBrokerageEmployee(
        id=kwargs.get("id", uuid.uuid4()),
        brokerage_employee_id=kwargs["brokerage_employee_id"],
        submission_id=kwargs["submission_id"],
        role=kwargs["role"],
        source=kwargs["source"],
    )
    db.session.add(submission_brokerage_employee)
    return submission_brokerage_employee


def submission_brokerage_fixture(**kwargs) -> SubmissionBrokerage:
    submission_brokerage = SubmissionBrokerage(
        id=kwargs.get("id", uuid.uuid4()),
        brokerage_id=kwargs["brokerage_id"],
        submission_id=kwargs["submission_id"],
        source=kwargs["source"],
    )
    db.session.add(submission_brokerage)
    return submission_brokerage


def submission_relation_fixture(**kwargs) -> SubmissionRelation:
    submission_relation = SubmissionRelation(
        id=kwargs.get("id", uuid.uuid4()),
        from_submission_id=kwargs["from_submission_id"],
        to_submission_id=kwargs["to_submission_id"],
        type=kwargs["type"],
        confidence=kwargs["confidence"],
        reason=kwargs.get("reason"),
        is_resolved=kwargs.get("is_resolved"),
        is_light=kwargs.get("is_light"),
        is_active=kwargs.get("is_active", False),
        source=kwargs.get("source"),
    )
    db.session.add(submission_relation)
    return submission_relation


def submission_identifier_fixture(**kwargs) -> SubmissionIdentifier:
    submission_identifier = SubmissionIdentifier(
        id=kwargs.get("id", uuid.uuid4()),
        submission_id=kwargs["submission_id"],
        identifier=kwargs["identifier"],
        identifier_type=kwargs["identifier_type"],
    )
    db.session.add(submission_identifier)
    return submission_identifier


def submission_level_extracted_data_fixture(**kwargs) -> SubmissionLevelExtractedData:
    submission_level_extracted_data = SubmissionLevelExtractedData(
        id=kwargs.get("id", uuid.uuid4()),
        submission_id=kwargs["submission_id"],
        file_id=kwargs.get("file_id"),
        field=kwargs["field"],
        value=kwargs["value"],
        source_details=kwargs.get("source_details", SourceDetails.FILE_CONTENT),
        generation_method=kwargs.get("generation_method"),
        is_selected=kwargs.get("is_selected"),
        selected_by_user=kwargs.get("selected_by_user"),
        is_valid=kwargs.get("is_valid", True),
        validation_details=kwargs.get("validation_details"),
        parent_id=kwargs.get("parent_id"),
    )
    db.session.add(submission_level_extracted_data)
    return submission_level_extracted_data


def submission_processing_fixture(**kwargs) -> SubmissionProcessing:
    submission_processing = SubmissionProcessing(
        id=kwargs.get("id", uuid.uuid4()),
        submission_id=kwargs["submission_id"],
        report_id=kwargs["report_id"],
        created_at=kwargs.get("created_at"),
        dependency_parent_report_id=kwargs.get("dependency_parent_report_id"),
        processing_state=kwargs["processing_state"],
        is_shadow_processing=kwargs.get("is_shadow_processing"),
        is_triage_processing=kwargs.get("is_triage_processing"),
        is_dependency_processing=kwargs.get("is_dependency_processing"),
        verification_sla=kwargs["verification_sla"],
        verified_at=kwargs.get("verified_at"),
        auto_verified_at=kwargs.get("auto_verified_at"),
        manual_verified_at=kwargs.get("manual_verified_at"),
        is_active=kwargs.get("is_active"),
        is_stuck_for_engineering=kwargs.get("is_stuck_for_engineering"),
        stuck_reason=kwargs.get("stuck_reason"),
        is_for_night_shift_processing=kwargs.get("is_for_night_shift_processing"),
        is_initial_verification=kwargs.get("is_initial_verification"),
        updated_at=kwargs.get("updated_at"),
    )
    db.session.add(submission_processing)
    return submission_processing


def shareholder_fixture(**kwargs) -> Shareholder:
    shareholder = Shareholder(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at"),
        updated_at=kwargs.get("updated_at"),
        submission_id=kwargs["submission_id"],
        file_id=kwargs["file_id"],
        shareholder_id=kwargs.get("shareholder_id"),
        shareholder_name=kwargs["shareholder_name"],
        shareholder_type=kwargs.get("shareholder_type"),
        ownership_percentage=kwargs.get("ownership_percentage"),
        is_director_or_board_member=kwargs.get("is_director_or_board_member"),
        submission_business_id=kwargs.get("submission_business_id"),
    )
    db.session.add(shareholder)
    return shareholder


def broker_group_fixture(**kwargs) -> BrokerGroup:
    broker_group = BrokerGroup(
        id=kwargs.get("id", uuid.uuid4()),
        name=kwargs.get("name", "Test name"),
        organization_id=kwargs["organization_id"],
    )
    db.session.add(broker_group)
    return broker_group


def broker_group_mapping_fixture(**kwargs) -> BrokerGroupMapping:
    broker_group_mapping = BrokerGroupMapping(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        broker_group_id=kwargs["broker_group_id"],
        broker_id=kwargs["broker_id"],
        organization_id=kwargs.get("organization_id"),
        user_id=kwargs.get("user_id"),
    )
    db.session.add(broker_group_mapping)
    return broker_group_mapping


def task_fixture(**kwargs) -> Task:
    task = Task(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        task_definition_id=kwargs["task_definition_id"],
        organization_id=kwargs.get("organization_id", 1),
        submission_id=kwargs.get("submission_id"),
        file_id=kwargs.get("file_id"),
        business_id=kwargs.get("business_id"),
        status=kwargs.get("status", "PENDING"),
        context=kwargs.get("context"),
        input=kwargs.get("input", {"input_field": "input_value"}),
        output=kwargs.get("output"),
        processed_output=kwargs.get("processed_output"),
        confidence=kwargs.get("confidence"),
        metrics=kwargs.get("metrics"),
        processing_cost=kwargs.get("processing_cost"),
        processing_time=kwargs.get("processing_time"),
        is_valid_output=kwargs.get("is_valid_output"),
        is_valid_input=kwargs.get("is_valid_input"),
        output_task_execution_id=kwargs.get("output_task_execution_id"),
        is_test_run=kwargs.get("is_test_run", False),
        task_dataset_execution_id=kwargs.get("task_dataset_execution_id"),
    )
    db.session.add(task)
    db.session.flush()
    return task


def task_model_fixture(**kwargs) -> TaskModel:
    task_model = TaskModel(
        id=kwargs.get("id", uuid.uuid4()),
        name=kwargs.get("name", "name"),
        type=kwargs.get("type", "LLM_PROMPT"),
        execution_config=kwargs.get(
            "execution_config", {"execution_arn": "arn:aws:states:us-west-2:123456789012:stateMachine:HelloWorld"}
        ),
        execution_type=kwargs.get("execution_type", "LAMBDA"),
        processing_type=kwargs.get("processing_type", "EXTRACTION"),
        llm_model=kwargs.get("llm_model"),
        use_task_output_processor=kwargs.get("use_task_output_processor", False),
        timeout=kwargs.get("timeout"),
    )
    db.session.add(task_model)
    db.session.flush()
    return task_model


def task_definition_model_fixture(**kwargs) -> TaskDefinitionModel:
    task_definition_model = TaskDefinitionModel(
        id=kwargs.get("id", uuid.uuid4()),
        task_definition_id=kwargs["task_definition_id"],
        task_model_id=kwargs["task_model_id"],
        validation_task_model_id=kwargs.get("validation_task_model_id"),
        order=kwargs.get("order", 0),
        is_always_run=kwargs.get("is_always_run", False),
        is_aware_of_previous_runs=kwargs.get("is_aware_of_previous_runs", False),
        is_refinement_run=kwargs.get("is_refinement_run", False),
        is_preprocessing_input_run=kwargs.get("is_preprocessing_input_run", False),
        is_consolidation_run=kwargs.get("is_consolidation_run", False),
        is_validation_run=kwargs.get("is_validation_run", False),
        can_self_reflect=kwargs.get("can_self_reflect", False),
        is_benchmark_run=kwargs.get("is_benchmark_run", False),
        is_disabled=kwargs.get("is_disabled", False),
        only_organization_ids=kwargs.get("only_organization_ids"),
        except_organization_ids=kwargs.get("except_organization_ids"),
        is_cost_fallback=kwargs.get("is_cost_fallback", False),
    )
    db.session.add(task_definition_model)
    db.session.flush()
    return task_definition_model


def task_execution_fixture(**kwargs) -> TaskExecution:
    task_execution = TaskExecution(
        id=kwargs.get("id", uuid.uuid4()),
        task_id=kwargs["task_id"],
        task_model_id=kwargs["task_model_id"],
        input=kwargs.get("input"),
        output=kwargs.get("output"),
        used_input_tokens=kwargs.get("used_input_tokens"),
        used_output_tokens=kwargs.get("used_output_tokens"),
        processing_cost=kwargs.get("processing_cost"),
        processing_time=kwargs.get("processing_time"),
        confidence=kwargs.get("confidence"),
        output_evaluation=kwargs.get("output_evaluation"),
        processed_output=kwargs.get("processed_output"),
        is_validation_run=kwargs.get("is_validation_run", False),
        is_self_reflection_run=kwargs.get("is_self_reflection_run", False),
        is_consolidation_run=kwargs.get("is_consolidation_run", False),
        is_benchmark_run=kwargs.get("is_benchmark_run", False),
        validated_task_execution_id=kwargs.get("validated_task_execution_id"),
    )
    db.session.add(task_execution)
    db.session.flush()
    return task_execution


def task_definition_fixture(**kwargs) -> TaskDefinition:
    task_definition = TaskDefinition(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at", datetime.utcnow()),
        name=kwargs.get("name", "name"),
        code=kwargs.get("code", "code"),
        group=kwargs.get("group"),
        llm_task_description=kwargs.get("llm_task_description"),
        has_large_state=kwargs.get("has_large_state", False),
        timeout=kwargs.get("timeout"),
    )
    db.session.add(task_definition)
    db.session.flush()
    return task_definition


def customizable_classifier_fixture(**kwargs) -> CustomizableClassifier:
    classifier = CustomizableClassifier(
        id=kwargs.get("id", uuid.uuid4()),
        fact_subtype_id=kwargs["fact_subtype_id"],
        label=kwargs.get("label", "label"),
        description=kwargs.get("description", "description"),
        is_submission_classifier=kwargs.get("is_submission_classifier", False),
        is_business_classifier=kwargs.get("is_business_classifier", True),
        is_applicable_to_all_industries=kwargs.get("is_applicable_to_all_industries", True),
        is_applicable_to=kwargs.get("is_applicable_to"),
        is_applicable_to_naics_codes=kwargs.get("is_applicable_to_naics_codes"),
        is_applicable_to_coverage_ids=kwargs.get("is_applicable_to_coverage_ids"),
        is_not_applicable_to_naics_codes=kwargs.get("is_not_applicable_to_naics_codes"),
        should_analyze=kwargs.get("should_analyze"),
        should_not_analyze=kwargs.get("should_not_analyze"),
        minimum_required_documents=kwargs.get("minimum_required_documents", 10),
        organization_id=kwargs.get("organization_id"),
        type=kwargs.get("type", CustomizableClassifierType.PHRASE_MATCHING),
        classification_business_rule_type=kwargs.get(
            "classification_business_rule_type", ClassificationBusinessRuleType.PERCENTAGE_THRESHOLD
        ),
        count_cutoff=kwargs.get("count_cutoff"),
        percentage_cutoff=kwargs.get("percentage_cutoff"),
        is_active=kwargs.get("is_active", True),
    )

    db.session.add(classifier)
    return classifier


def email_classification_label_fixture(**kwargs) -> EmailClassificationLabel:
    email_classification_label = EmailClassificationLabel(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at", None),
        label=kwargs.get("label", "label"),
        organization_id=kwargs.get("organization_id", 1),
        should_process=kwargs.get("should_process", False),
    )
    db.session.add(email_classification_label)
    return email_classification_label


def email_classifier_fixture(**kwargs) -> EmailClassifier:
    email_classifier = EmailClassifier(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at", None),
        email_label_id=kwargs["email_label_id"],
        order=kwargs.get("order", 0),
        skip_if_previous_rule_was_hit=kwargs.get("skip_if_previous_rule_was_hit", False),
        type=kwargs.get("type", EmailClassifierType.ATTACHMENTS_BASED),
        organization_id=kwargs.get("organization_id", 1),
    )
    db.session.add(email_classifier)
    return email_classifier


def email_classification_fixture(**kwargs) -> EmailClassification:
    email_classification = EmailClassification(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at", None),
        email_id=kwargs["email_id"],
        classifier_id=kwargs.get("classifier_id", None),
        label_id=kwargs["label_id"],
        explanation=kwargs.get("explanation"),
        snippet=kwargs.get("snippet"),
        additional_data=kwargs.get("additional_data"),
    )
    db.session.add(email_classification)
    return email_classification


def task_scoring_fixture(**kwargs) -> TaskScoring:
    task_scoring = TaskScoring(
        id=kwargs.get("id", uuid.uuid4()),
        task_definition_id=kwargs["task_definition_id"],
        start_date=kwargs.get("start_date", datetime.today()),
        end_date=kwargs.get("end_date", datetime.today()),
        sample_size=kwargs.get("sample_size"),
        scoring_methods=kwargs.get("scoring_methods"),
        task_models=kwargs.get("task_models"),
        batch_id=kwargs.get("batch_id"),
        status=kwargs.get("status"),
        scoring_result=kwargs.get("scoring_result"),
    )
    db.session.add(task_scoring)
    return task_scoring


def task_scoring_execution_fixture(**kwargs) -> TaskScoringExecution:
    task_scoring_execution = TaskScoringExecution(
        id=kwargs.get("id", uuid.uuid4()),
        task_scoring_id=kwargs["task_scoring_id"],
        task_id=kwargs["task_id"],
        task_index=kwargs["task_index"],
        task_execution_id=kwargs["task_execution_id"],
        task_model_index=kwargs["task_model_index"],
        independent_score=kwargs.get("independent_score"),
        ab_score=kwargs.get("ab_score"),
        is_score_consistent=kwargs.get("is_score_consistent"),
        is_valid_output=kwargs.get("is_valid_output", True),
    )
    db.session.add(task_scoring_execution)
    return task_scoring_execution


def support_user_file_fixture(**kwargs) -> SupportUserFile:
    support_user_file = SupportUserFile(
        id=kwargs.get("id", uuid.uuid4()),
        support_user_id=kwargs["support_user_id"],
        file_id=kwargs["file_id"],
        created_at=kwargs.get("created_at", datetime.utcnow()),
        file_assigned_by_id=kwargs.get("file_assigned_by_id"),
    )
    db.session.add(support_user_file)
    return support_user_file


def broker_client_code_fixture(**kwargs) -> BrokerClientCode:
    task_scoring_execution = BrokerClientCode(
        id=kwargs.get("id", uuid.uuid4()),
        agent_email=kwargs.get("agent_email"),
        portal_email=kwargs.get("portal_email"),
        agent_code=kwargs.get("agent_code"),
        agency_code=kwargs.get("agency_code"),
        organization_id=kwargs["organization_id"],
    )
    db.session.add(task_scoring_execution)
    return task_scoring_execution


def brokerage_client_code_fixture(**kwargs) -> BrokerageClientCode:
    brokerage_client_code = BrokerageClientCode(
        id=kwargs.get("id", uuid.uuid4()),
        brokerage_id=kwargs["brokerage_id"],
        agency_name=kwargs.get("agency_name"),
        agent_name=kwargs.get("agent_name"),
        agent_code=kwargs.get("agent_code"),
        agency_code=kwargs.get("agency_code"),
        organization_id=kwargs["organization_id"],
    )
    db.session.add(brokerage_client_code)
    return brokerage_client_code


def submission_field_source_fixture(**kwargs) -> SubmissionFieldSource:
    submission_field_source = SubmissionFieldSource(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at"),
        field_name=kwargs.get("field_name", "happy_field"),
        submission_id=kwargs["submission_id"],
        source=kwargs.get("source", "AUTO"),
    )
    db.session.add(submission_field_source)
    return submission_field_source


def task_definition_metrics_fixture(**kwargs) -> TaskDefinitionMetrics:
    """
    Create a TaskDefinitionMetrics fixture.

    Args:
        **kwargs: Overrides for the default values.

    Returns:
        TaskDefinitionMetrics: The created fixture.
    """
    now = datetime.now(timezone.utc)
    task_definition_metrics = TaskDefinitionMetrics(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", now),
        updated_at=kwargs.get("updated_at", now),
        task_definition_id=kwargs["task_definition_id"],
        task_dataset_execution_id=kwargs.get("task_dataset_execution_id"),
        start_date=kwargs.get("start_date", now - timedelta(days=7)),
        end_date=kwargs.get("end_date", now),
        sample_size=kwargs.get("sample_size", 100),
        valid_output_ratio=kwargs.get("valid_output_ratio", 0.85),
        consolidation_cost_per_model=kwargs.get("consolidation_cost_per_model", 0.05),
        task_avg_cost=kwargs.get("task_avg_cost", 0.15),
        task_overall_quality=kwargs.get("task_overall_quality", 0.75),
        precision=kwargs.get("precision", 0.8),
        recall=kwargs.get("recall", 0.75),
        f1_score=kwargs.get("f1_score", 0.77),
        accuracy=kwargs.get("accuracy", 0.8),
        performance_metrics_details=kwargs.get("performance_metrics_details", {}),
    )
    db.session.add(task_definition_metrics)
    return task_definition_metrics


def task_model_metrics_fixture(**kwargs) -> TaskModelMetrics:
    task_model_metrics = TaskModelMetrics(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.now(timezone.utc)),
        updated_at=kwargs.get("updated_at", datetime.now(timezone.utc)),
        task_definition_model_id=kwargs["task_definition_model_id"],
        task_definition_metrics_id=kwargs["task_definition_metrics_id"],
        sample_size=kwargs.get("sample_size", 100),
        is_fallback_model=kwargs.get("is_fallback_model", False),
        valid_output_ratio=kwargs.get("valid_output_ratio", 0.85),
        avg_independent_score=kwargs.get("avg_independent_score", 0.75),
        avg_ab_score=kwargs.get("avg_ab_score", 0.65),
        selection_ratio=kwargs.get("selection_ratio", 0.70),
        selection_quality=kwargs.get("selection_quality", 0.80),
        avg_cost=kwargs.get("avg_cost", 0.05),
        avg_processing_time=kwargs.get("avg_processing_time", 300),
        cost_per_valid_output=kwargs.get("cost_per_valid_output", 0.06),
        cost_per_selected_output=kwargs.get("cost_per_selected_output", 0.07),
        quality_score=kwargs.get("quality_score", 0.70),
        quality_to_cost_ratio=kwargs.get("quality_to_cost_ratio", 100.0),
    )
    db.session.add(task_model_metrics)
    return task_model_metrics


def sync_matching_run_fixture(**kwargs) -> SyncMatchingRun:
    sync_matching_run = SyncMatchingRun(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at", datetime.utcnow()),
        producer_config=kwargs.get("producer_config"),
        organization_id=kwargs["organization_id"],
        status=kwargs["status"],
        started_at=kwargs.get("started_at"),
        completed_at=kwargs.get("completed_at"),
        automatically_triggered=kwargs.get("automatically_triggered", False),
    )
    db.session.add(sync_matching_run)
    return sync_matching_run


def sync_matching_result_fixture(**kwargs) -> SyncMatchingResult:
    sync_matching_result = SyncMatchingResult(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at", datetime.utcnow()),
        sync_matching_run_id=kwargs["sync_matching_run_id"],
        submission_sync_id=kwargs["submission_sync_id"],
        matching_results=kwargs["matching_results"],
        is_processed=kwargs.get("is_processed", False),
    )
    db.session.add(sync_matching_result)
    return sync_matching_result


def submission_identifiers_suggestion_fixture(**kwargs) -> SubmissionIdentifiersSuggestion:
    suggestion = SubmissionIdentifiersSuggestion(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        submission_id=kwargs["submission_id"],
        submission_sync_id=kwargs["submission_sync_id"],
        confidence=kwargs["confidence"],
    )
    db.session.add(suggestion)
    return suggestion


def enhanced_file_fixture(**kwargs) -> EnhancedFile:
    enhanced_file = EnhancedFile(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at"),
        file_id=kwargs["file_id"],
        s3_key=kwargs["s3_key"],
        enhancement_type=FileEnhancementType.try_parse_str(kwargs["enhancement_type"]),
    )
    db.session.add(enhanced_file)
    return enhanced_file


def task_dataset_fixture(**kwargs):
    """Create a TaskDataset fixture."""
    task_dataset = TaskDataset(
        id=kwargs.get("id", uuid.uuid4()),
        description=kwargs.get("description", "Test dataset"),
        organization_id=kwargs.get("organization_id", 1),
        processing_type=kwargs.get("processing_type", "CLASSIFICATION"),
        output_type=kwargs.get("output_type", "BOOLEAN"),
        dataset_inputs_id=kwargs.get("dataset_inputs_id", uuid.uuid4()),
        updated_at=kwargs.get("updated_at", datetime.utcnow()),
    )
    db.session.add(task_dataset)
    return task_dataset


def task_dataset_input_fixture(**kwargs):
    """Create a TaskDatasetInput fixture."""
    task_dataset_input = TaskDatasetInput(
        id=kwargs.get("id", uuid.uuid4()),
        dataset_inputs_id=kwargs.get("dataset_inputs_id"),
        submission_id=kwargs.get("submission_id"),
        file_id=kwargs.get("file_id"),
        business_id=kwargs.get("business_id"),
        context=kwargs.get("context"),
        input=kwargs.get("input", {"question": "Test question", "context": "Test context"}),
    )
    db.session.add(task_dataset_input)
    db.session.flush()
    return task_dataset_input


def task_dataset_ground_truth_fixture(**kwargs):
    """Create a TaskDatasetGroundTruth fixture."""
    task_dataset_ground_truth = TaskDatasetGroundTruth(
        id=kwargs.get("id", uuid.uuid4()),
        task_dataset_id=kwargs.get("task_dataset_id"),
        task_dataset_input_id=kwargs.get("task_dataset_input_id"),
        value=kwargs.get("value", {"value": True}),
        has_value=kwargs.get("has_value", True),
    )
    db.session.add(task_dataset_ground_truth)
    return task_dataset_ground_truth


def task_dataset_model_outcome_fixture(**kwargs):
    """Create a TaskDatasetModelOutcome fixture."""
    task_dataset_model_outcome = TaskDatasetModelOutcome(
        id=kwargs.get("id", uuid.uuid4()),
        task_dataset_input_id=kwargs.get("task_dataset_input_id"),
        task_model_id=kwargs.get("task_model_id"),
        validation_task_model_id=kwargs.get("validation_task_model_id"),
        task_definition_id=kwargs.get("task_definition_id"),
        output=kwargs.get("output"),
        is_valid_output=kwargs.get("is_valid_output", False),
        prediction_result=kwargs.get("prediction_result"),
        score=kwargs.get("score"),
        input_tokens=kwargs.get("input_tokens"),
        output_tokens=kwargs.get("output_tokens"),
        validation_input_tokens=kwargs.get("validation_input_tokens"),
        validation_output_tokens=kwargs.get("validation_output_tokens"),
        processing_time=kwargs.get("processing_time"),
    )
    db.session.add(task_dataset_model_outcome)
    return task_dataset_model_outcome


def task_dataset_execution_fixture(**kwargs) -> TaskDatasetExecution:
    task_dataset_execution = TaskDatasetExecution(
        id=kwargs.get("id", uuid.uuid4()),
        created_at=kwargs.get("created_at", datetime.utcnow()),
        updated_at=kwargs.get("updated_at", datetime.utcnow()),
        task_dataset_id=kwargs["task_dataset_id"],
        task_definition_id=kwargs["task_definition_id"],
        status=kwargs["status"],
        execution_arn=kwargs.get("execution_arn", "example_arn"),
        test_run_id=kwargs.get("test_run_id"),
    )
    db.session.add(task_dataset_execution)
    db.session.flush()
    return task_dataset_execution


def customizable_classifier_v2_fixture(**kwargs) -> CustomizableClassifierV2:
    fact_subtype_id = kwargs.get("fact_subtype_id")
    extracted_value_name = kwargs.get("extracted_value_name")

    if not fact_subtype_id and not extracted_value_name:
        fact_subtype_id = "Test"

    default_kwargs = {
        "name": f"Test Classifier {uuid.uuid4()}",
        "fact_subtype_id": fact_subtype_id,
        "extracted_value_name": extracted_value_name,
        "output_type": ClassifierOutputType.BOOLEAN,
        "output_unit": None,
        "input_types": ["document"],
        "organization_ids": kwargs.get("organization_ids", []),
        "run_in_pds": False,
        "is_internal": False,
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }

    default_kwargs.update(kwargs)

    classifier = CustomizableClassifierV2(**default_kwargs)
    db.session.add(classifier)
    db.session.flush()

    return classifier


def classifier_version_fixture(**kwargs) -> ClassifierVersion:
    default_kwargs = {
        "name": f"Version {uuid.uuid4()}",
        "classifier_description": "Test classifier version",
        "is_active": False,
        "classifier_id": kwargs.get("classifier_id") or customizable_classifier_v2_fixture().id,
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }

    default_kwargs.update(kwargs)

    version = ClassifierVersion(**default_kwargs)
    db.session.add(version)
    db.session.flush()

    return version


def classifier_filter_rule_fixture(**kwargs) -> FilterRule:
    default_kwargs = {
        "id": kwargs.get("id", uuid.uuid4()),
        "classifier_id": kwargs.get("classifier_id") or customizable_classifier_v2_fixture().id,
        "filter_type": kwargs.get("filter_type", FilterRuleType.NAICS),
        "negated": kwargs.get("negated", False),
        "values": kwargs.get("values", ["2137"]),
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }
    default_kwargs.update(kwargs)

    filter_rule = FilterRule(**default_kwargs)
    db.session.add(filter_rule)
    db.session.flush()

    return filter_rule


def classifier_config_fixture(**kwargs) -> ClassifierConfig:
    default_kwargs = {
        "input_types": ["document"],
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }

    default_kwargs.update(kwargs)

    config = ClassifierConfig(**default_kwargs)
    db.session.add(config)
    db.session.flush()

    return config


def classifier_config_version_fixture(**kwargs) -> ClassifierConfigVersion:
    default_kwargs = {
        "classifier_config_id": kwargs.get("classifier_config_id") or classifier_config_fixture().id,
        "input_processing_type": InputProcessingType.OCR_TEXT.value,
        "extraction_type": ExtractionType.PHRASES,
        "is_autogenerated": False,
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }

    default_kwargs.update(kwargs)

    config_version = ClassifierConfigVersion(**default_kwargs)
    db.session.add(config_version)
    db.session.flush()

    return config_version


def phrases_config_version_fixture(**kwargs) -> PhrasesConfigVersion:
    default_kwargs = {
        "classifier_config_id": kwargs.get("classifier_config_id") or classifier_config_fixture().id,
        "input_processing_type": kwargs.get("input_processing_type") or InputProcessingType.OCR_TEXT,
        "extraction_type": ExtractionType.PHRASES,
        "is_autogenerated": False,
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }

    default_kwargs.update(kwargs)

    config_version = PhrasesConfigVersion(**default_kwargs)
    db.session.add(config_version)
    db.session.flush()

    return config_version


def phrases_with_llm_config_version_fixture(**kwargs) -> PhrasesWithLLMConfigVersion:
    default_kwargs = {
        "classifier_config_id": kwargs.get("classifier_config_id") or classifier_config_fixture().id,
        "input_processing_type": kwargs.get("input_processing_type") or InputProcessingType.OCR_TEXT.value,
        "extraction_type": ExtractionType.PHRASES_WITH_LLM,
        "is_autogenerated": False,
        "llm_model": "gpt-4",
        "prompt": "Test prompt",
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }

    default_kwargs.update(kwargs)

    config_version = PhrasesWithLLMConfigVersion(**default_kwargs)
    db.session.add(config_version)
    db.session.flush()

    return config_version


def llm_config_version_fixture(**kwargs) -> LLMConfigVersion:
    default_kwargs = {
        "classifier_config_id": kwargs.get("classifier_config_id") or classifier_config_fixture().id,
        "input_processing_type": kwargs.get("input_processing_type") or InputProcessingType.OCR_TEXT.value,
        "extraction_type": ExtractionType.LLM,
        "is_autogenerated": False,
        "llm_model": "gpt-4",
        "prompt": "Test prompt",
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }

    default_kwargs.update(kwargs)

    config_version = LLMConfigVersion(**default_kwargs)
    db.session.add(config_version)
    db.session.flush()

    return config_version


def classifier_phrase_fixture(**kwargs) -> ClassifierPhrase:
    default_kwargs = {
        "classifier_config_version_id": (
            kwargs.get("classifier_config_version_id") or phrases_config_version_fixture().id
        ),
        "phrase": "Test phrase",
        "weight": 0.8,
        "excludes": [],
        "created_at": kwargs.get("created_at", datetime.utcnow()),
        "updated_at": kwargs.get("updated_at", datetime.utcnow()),
    }

    default_kwargs.update(kwargs)

    phrase = ClassifierPhrase(**default_kwargs)
    db.session.add(phrase)
    db.session.flush()

    return phrase


def classifier_to_config_version_fixture(**kwargs) -> ClassifierToConfigVersion:
    # Get required IDs first to avoid using default_kwargs before it's defined
    classifier_version_id = kwargs.get("classifier_version_id")
    classifier_config_id = kwargs.get("classifier_config_id")
    classifier_config_version_id = kwargs.get("classifier_config_version_id")

    current_time = datetime.utcnow()
    default_kwargs = {
        "id": kwargs.get("id", uuid.uuid4()),
        "classifier_version_id": classifier_version_id or uuid.uuid4(),
        "classifier_config_id": classifier_config_id or uuid.uuid4(),
        "classifier_config_version_id": classifier_config_version_id or uuid.uuid4(),
        "created_at": current_time,
        "updated_at": current_time,
    }

    for k, v in default_kwargs.items():
        if k not in kwargs:
            kwargs[k] = v

    c2cv = ClassifierToConfigVersion(**kwargs)
    db.session.add(c2cv)
    db.session.flush()
    return c2cv


def task_cost_limit_fixture(**kwargs) -> TaskCostLimit:
    """
    Create a TaskCostLimit instance for testing.

    Args:
        **kwargs: Override default values for the TaskCostLimit

    Returns:
        A TaskCostLimit instance
    """
    default_kwargs = {
        "task_definition_group": "test_group",
        "for_test_run": False,
        "daily_cost_limit_in_usd": 100.0,
        "daily_cost_limit_in_reference_to_last_week": None,
        "limit_exceeded_action": "block",
    }
    default_kwargs.update(kwargs)
    task_cost_limit = TaskCostLimit(**default_kwargs)
    db.session.add(task_cost_limit)
    db.session.flush()
    return task_cost_limit


def submission_premises_fixture(**kwargs) -> SubmissionPremises:
    premises = SubmissionPremises(
        id=kwargs.get("id", uuid.uuid4()),
        submission_id=kwargs["submission_id"],
        named_insured=kwargs.get("named_insured"),
        address=kwargs["address"],
        premises_id=kwargs.get("premises_id"),
        submission_premises_type=kwargs.get("submission_premises_type", SubmissionPremisesType.FNI),
        additional_data=kwargs.get("additional_data", {}),
        organization_id=kwargs["organization_id"],
    )
    db.session.add(premises)
    return premises


def aig_uw_mapping_fixture(**kwargs) -> AIGUwMapping:
    mapping = AIGUwMapping(
        id=kwargs.get("id"),
        broker_contact_email=kwargs.get("broker_contact_email", "<EMAIL>"),
        ww_uw_name=kwargs.get("ww_uw_name", "Over Writer"),
        user_id=kwargs.get("user_id"),
    )
    db.session.add(mapping)
    return mapping


def classifier_version_task_definition_fixture(**kwargs) -> ClassifierVersionTaskDefinition:
    """Create a ClassifierVersionTaskDefinition fixture."""
    default_kwargs = {
        "classifier_version_id": kwargs.get("classifier_version_id") or classifier_version_fixture().id,
        "classifier_config_id": kwargs.get("classifier_config_id") or classifier_config_fixture().id,
        "task_definition_id": kwargs.get("task_definition_id") or task_definition_fixture().id,
    }
    default_kwargs.update(kwargs)

    link = ClassifierVersionTaskDefinition(**default_kwargs)
    db.session.add(link)
    db.session.flush()
    return link


def auto_qa_check_fixture(**kwargs) -> "AutoQACheck":
    from copilot.models.auto_qa_checks import AutoQACheck

    default_kwargs = {
        "id": kwargs.get("id", uuid.uuid4()),
        "submission_id": kwargs["submission_id"],
        "organization_id": kwargs.get("organization_id", 1),
        "check_type": kwargs.get("check_type", "data_validation"),
        "original_value": kwargs.get("original_value", "original_test_value"),
        "original_value_source": kwargs.get("original_value_source", "test_source"),
        "check_status": kwargs.get("check_status", "passed"),
        "check_score": kwargs.get("check_score", 95),
        "explanation": kwargs.get("explanation", "Test explanation for QA check"),
        "details": kwargs.get("details", {"test_key": "test_value"}),
        "suggested_value": kwargs.get("suggested_value", "suggested_test_value"),
        "suggested_value_reasoning": kwargs.get("suggested_value_reasoning", "Test reasoning"),
        "suggested_value_confidence": kwargs.get("suggested_value_confidence", 90),
    }
    default_kwargs.update(kwargs)

    auto_qa_check = AutoQACheck(**default_kwargs)
    db.session.add(auto_qa_check)
    return auto_qa_check
