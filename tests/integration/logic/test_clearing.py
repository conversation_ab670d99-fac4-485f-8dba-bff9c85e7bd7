import datetime
import json
import uuid

from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_processing_state import SubmissionProcessingState
import pytest

from copilot.logic.clearing.clearing_service import (
    SAME_ENTITY_REASON,
    SAME_FILE_REASON,
    SUBJECT_ILIKE_REASON,
    SUBJECT_SIMILAR_REASON,
    ClearingService,
)
from copilot.models import (
    File,
    Organization,
    ReportPermission,
    Settings,
    SubmissionAudit,
    db,
)
from copilot.models.files import ProcessedFile
from copilot.models.reports import (
    ClearingSubStatus,
    ReportLink,
    SubmissionClearingIssue,
    SubmissionClientId,
    SubmissionIdentifier,
)
from copilot.models.submission_premises import SubmissionPremises
from copilot.models.types import (
    ClearingStatus,
    CoverageType,
    PermissionType,
    SubmissionPremisesType,
)
from copilot.services.orgs.bowhead_service import (
    BowheadRelationResult,
    BowheadRelationType,
)
from copilot.v3.controllers.clearing import (
    clear_submission,
    invoke_clearing_for_submission,
)
from tests.integration.factories import (
    broker_fixture,
    brokerage_fixture,
    coverage_fixture,
    organization_fixture,
    report_and_submission_fixture,
    report_fixture,
    report_processing_dependency_fixture,
    submission_business_fixture,
    submission_coverage_fixture,
    submission_fixture,
    submission_premises_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj

UUID_1 = "ea2ae8db-4ea3-4483-9425-7c821c1adff6"
UUID_2 = "aa2ae8db-4ea3-4483-9425-7c821c1adff6"


def _test_clearing_base(client_id_required_for_clearing=None):
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=1)
    agent1 = broker_fixture(organization_id=1, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    agent2 = broker_fixture(organization_id=1, brokerage_id=brokerage.id, name="Broker 2", email="<EMAIL>")
    org.settings = Settings(is_clearing_enabled=True, clear_oldest_by_default=True)
    user1 = user_fixture(id=1, external_id="external_id_1")
    user2 = user_fixture(id=2, external_id="external_id_2")
    user1.settings = Settings(
        can_resolve_clearing_issues=False, client_id_required_for_clearing=client_id_required_for_clearing
    )
    user2.settings = Settings(
        can_resolve_clearing_issues=True, client_id_required_for_clearing=client_id_required_for_clearing
    )
    report1 = report_fixture(owner_id=user1.id)
    report2 = report_fixture(owner_id=user2.id)
    sub1 = submission_fixture()
    sub2 = submission_fixture()
    report1.submission = sub1
    report2.submission = sub2
    sub1.clearing_status = ClearingStatus.PRE_CLEARING
    sub2.clearing_status = ClearingStatus.PRE_CLEARING
    sb1 = submission_business_fixture(business_id=UUID_1)
    sb2 = submission_business_fixture(business_id=UUID_1)
    sub1.businesses = [sb1]
    sub2.businesses = [sb2]
    sub1.brokerage_id = brokerage.id
    sub2.brokerage_id = brokerage.id
    sub1.broker_id = agent1.id
    sub2.broker_id = agent2.id

    for r in [report1, report2]:
        r.is_deleted = False
        r.is_archived = False
        r.links = []

    for s in [sub1, sub2]:
        s.is_verified = True
        s.is_verification_required = True
        s.proposed_effective_date = datetime.datetime.now()

    db.session.add(org)
    db.session.add(user1)
    db.session.add(user2)
    db.session.add(report1)
    db.session.add(report2)
    db.session.add(sub1)
    db.session.add(sub2)
    db.session.commit()

    return report1, report2


def _create_report(
    submission_kwargs=None,
    submission_business_kwargs=None,
    submission_premises_kwargs=None,
    broker_id=None,
    organization_id=1,
    report_name="a",
):
    org = organization_fixture(id=organization_id)
    org.settings = Settings(is_clearing_enabled=True, clear_oldest_by_default=True)
    user1 = user_fixture(id=1, organization_id=organization_id)
    user1.settings = Settings(can_resolve_clearing_issues=False)
    report1 = report_fixture(owner_id=user1.id, organization_id=organization_id)
    submission_kwargs = {"proposed_effective_date": datetime.datetime.now()} | (
        submission_kwargs if submission_kwargs else {}
    )
    sub1 = submission_fixture(**submission_kwargs)
    report1.submission = sub1
    report1.name = report_name
    sb1 = (
        submission_business_fixture(**submission_business_kwargs)
        if submission_business_kwargs
        else submission_business_fixture(business_id=UUID_1)
    )
    sub1.businesses = [sb1]
    report1.is_deleted = False
    report1.is_archived = False
    report1.links = []
    sub1.is_verified = True
    sub1.is_verification_required = True
    sub1.broker_id = broker_id or uuid.uuid4()
    sub1.is_auto_processed = True

    db.session.add(org)
    db.session.add(user1)
    db.session.add(report1)
    db.session.add(sub1)
    if submission_premises_kwargs:
        default_params = {"submission_id": sub1.id, "organization_id": org.id}
        premises = submission_premises_fixture(**(default_params | submission_premises_kwargs))
        db.session.add(premises)

    db.session.commit()
    return report1


def test_get_similar_reports_conflict(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    report1, report2 = _test_clearing_base()
    service = ClearingService()

    reports = service._get_similar_reports(report1.submission)
    unique_reports = {r for r, _ in reports}
    assert len(unique_reports) == 1
    assert report2 in unique_reports

    reports = service._get_similar_reports(report2.submission)
    unique_reports = {r for r, _ in reports}
    assert len(unique_reports) == 1
    assert report1 in unique_reports


def test_get_similar_reports_no_conflict_agent(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    report1, report2 = _test_clearing_base()
    service = ClearingService()

    report1.submission.broker_id = report2.submission.broker_id
    db.session.commit()

    reports = service._get_similar_reports(report1.submission)
    unique_reports = {r for r, _ in reports}
    assert len(unique_reports) == 1
    reports = service._get_similar_reports(report2.submission)
    unique_reports = {r for r, _ in reports}
    assert len(unique_reports) == 1

    report1.submission.broker_id = None
    db.session.commit()

    reports = service._get_similar_reports(report2.submission)
    unique_reports = {r for r, _ in reports}
    assert len(unique_reports) == 1


def test_get_similar_reports_no_conflict_different_orgs(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    report1, report2 = _test_clearing_base()
    service = ClearingService()

    organization_fixture(id=2)
    user3 = user_fixture(id=3, organization_id=2)
    report1.owner_id = user3.id
    report1.organization_id = 2
    db.session.commit()

    # Not conflicting because different_orgs
    reports = service._get_similar_reports(report1.submission)
    assert len(reports) == 0
    reports = service._get_similar_reports(report2.submission)
    assert len(reports) == 0


def test_get_similar_reports_no_conflict_verified(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    report1, report2 = _test_clearing_base()
    service = ClearingService()

    report1.submission.is_verified = False
    db.session.commit()

    # Not conflicting because not verified
    reports = service._get_similar_reports(report2.submission)
    assert len(reports) == 0


def test_get_similar_reports_no_conflict_linked(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    report1, report2 = _test_clearing_base()
    service = ClearingService()

    report1.links = [ReportLink(report_2=report2)]
    report2.links = [ReportLink(report_2=report1)]
    db.session.commit()

    # Not conflicting because linked
    reports = service._get_similar_reports(report1.submission)
    assert len(reports) == 0
    reports = service._get_similar_reports(report2.submission)
    assert len(reports) == 0


def test_get_similar_reports_no_conflict_is_deleted(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    report1, report2 = _test_clearing_base()
    service = ClearingService()

    report1.is_deleted = True
    db.session.commit()

    # Not conflicting because deleted
    reports = service._get_similar_reports(report2.submission)
    assert len(reports) == 0


def test_get_similar_reports_no_conflict_different_businesses(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    report1, report2 = _test_clearing_base()
    service = ClearingService()

    report1.submission.businesses[0].business_id = UUID_2
    report2.submission.businesses[0].business_id = UUID_1
    report1.name = "abcde"
    report1.email_subject = "abcde"
    report2.name = "xyzxyz"
    report2.email_subject = "xyzxyz"
    db.session.commit()

    # Not conflicting because different businesses
    reports = service._get_similar_reports(report1.submission)
    assert len(reports) == 0

    reports = service._get_similar_reports(report2.submission)
    assert len(reports) == 0


def test_get_similar_reports_same_sync_address(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    org = organization_fixture()
    report1 = report_fixture(owner_id=user_fixture().id, name="abcde")
    report2 = report_fixture(owner_id=user_fixture().id, name="xyzxyz")
    sub1 = submission_fixture(report=report1)
    sub2 = submission_fixture(report=report2)
    submission_premises_fixture(submission_id=sub1.id, organization_id=org.id, address="  15 broadway")
    submission_premises_fixture(submission_id=sub2.id, organization_id=org.id, address="15 BroadWAY   ")
    submission_business_fixture(submission_id=sub1.id)
    submission_business_fixture(submission_id=sub2.id)
    db.session.commit()

    service = ClearingService()

    assert len(service._get_similar_reports(sub1)) == 0
    reports = [report for report, reason in service._get_similar_reports(sub1, True)]
    assert len(reports) == 1
    assert reports == [report2]

    assert len(service._get_similar_reports(sub2)) == 0
    reports = [report for report, reason in service._get_similar_reports(sub2, True)]
    assert len(reports) == 1
    assert reports == [report1]


def test_get_similar_reports_same_sync_premise_id(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    org = organization_fixture()
    report1 = report_fixture(owner_id=user_fixture().id, name="abcde")
    report2 = report_fixture(owner_id=user_fixture().id, name="xyzxyz")
    sub1 = submission_fixture(report=report1)
    sub2 = submission_fixture(report=report2)
    submission_premises_fixture(
        submission_id=sub1.id, organization_id=org.id, address="15 Broadway, NY", premises_id=UUID_1
    )
    submission_premises_fixture(
        submission_id=sub2.id, organization_id=org.id, address="15 Broadway, NYC,", premises_id=UUID_1
    )
    submission_business_fixture(submission_id=sub1.id)
    submission_business_fixture(submission_id=sub2.id)
    db.session.commit()

    service = ClearingService()

    assert len(service._get_similar_reports(sub1)) == 0
    reports = [report for report, reason in service._get_similar_reports(sub1, True)]
    assert len(reports) == 1
    assert reports == [report2]

    assert len(service._get_similar_reports(sub2)) == 0
    reports = [report for report, reason in service._get_similar_reports(sub2, True)]
    assert len(reports) == 1
    assert reports == [report1]


def test_get_similar_reports_different_sync_address(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    org = organization_fixture()
    report1 = report_fixture(owner_id=user_fixture().id, name="abcde")
    report2 = report_fixture(owner_id=user_fixture().id, name="xyzxyz")
    sub1 = submission_fixture(report=report1)
    sub2 = submission_fixture(report=report2)
    submission_premises_fixture(
        submission_id=sub1.id, organization_id=org.id, address="15 Broadway, NY", premises_id=UUID_1
    )
    submission_premises_fixture(
        submission_id=sub2.id, organization_id=org.id, address="Some other address", premises_id=UUID_2
    )
    submission_business_fixture(submission_id=sub1.id)
    submission_business_fixture(submission_id=sub2.id)
    db.session.commit()

    service = ClearingService()

    assert len(service._get_similar_reports(sub1)) == 0
    assert len(service._get_similar_reports(sub1, True)) == 0
    assert len(service._get_similar_reports(sub2)) == 0
    assert len(service._get_similar_reports(sub2, True)) == 0


def test_process_clearing_different_sync_address(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled", return_value=True)
    org = organization_fixture()
    report1 = report_fixture(owner_id=user_fixture().id, name="abcde")
    report2 = report_fixture(owner_id=user_fixture().id, name="xyzxyz")
    sub1 = submission_fixture(report=report1, clearing_status=ClearingStatus.PRE_CLEARING)
    sub2 = submission_fixture(report=report2, clearing_status=ClearingStatus.PRE_CLEARING)
    submission_premises_fixture(
        submission_id=sub1.id, organization_id=org.id, address="15 Broadway, NY", premises_id=UUID_1
    )
    submission_premises_fixture(
        submission_id=sub2.id, organization_id=org.id, address="Some other address", premises_id=UUID_2
    )
    submission_business_fixture(submission_id=sub1.id)
    submission_business_fixture(submission_id=sub2.id)
    db.session.commit()

    service = ClearingService()

    assert service._process_clearing(report1.submission, report1.owner) == []
    assert service._process_clearing(report2.submission, report2.owner) == []


def test_process_clearing_same_sync_address(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled", return_value=True)
    org = organization_fixture()
    report1 = report_fixture(owner_id=user_fixture().id, name="abcdef")
    report2 = report_fixture(owner_id=user_fixture().id, name="xyzxyz")
    report3 = report_fixture(owner_id=user_fixture().id, name="xyzxyz")
    report4 = report_fixture(owner_id=user_fixture().id, name="report")
    sub1 = submission_fixture(report=report1, clearing_status=ClearingStatus.PRE_CLEARING)
    sub2 = submission_fixture(report=report2, clearing_status=ClearingStatus.PRE_CLEARING)
    sub3 = submission_fixture(report=report3, clearing_status=ClearingStatus.PRE_CLEARING)
    sub4 = submission_fixture(report=report4, clearing_status=ClearingStatus.PRE_CLEARING)
    submission_premises_fixture(
        submission_id=sub1.id, organization_id=org.id, address="15 Broadway, NY", premises_id=UUID_1
    )
    submission_premises_fixture(
        submission_id=sub2.id, organization_id=org.id, address=" 15 broadway, ny ", premises_id=UUID_2
    )
    submission_premises_fixture(
        submission_id=sub3.id, organization_id=org.id, address=" some OTHER address ", premises_id=UUID_1
    )
    submission_premises_fixture(
        submission_id=sub4.id, organization_id=org.id, address="some other address", premises_id=UUID_2
    )
    submission_business_fixture(submission_id=sub1.id)
    submission_business_fixture(submission_id=sub2.id)
    submission_business_fixture(submission_id=sub3.id)
    submission_business_fixture(submission_id=sub4.id)
    db.session.commit()

    service = ClearingService()

    affected_reports1 = service._process_clearing(report1.submission, report1.owner)
    assert len(affected_reports1) == 3
    assert {report.id for report in affected_reports1} == {report1.id, report2.id, report3.id}

    affected_reports4 = service._process_clearing(report4.submission, report4.owner)
    assert {report.id for report in affected_reports4} == {report2.id, report3.id, report4.id}


def test_should_clear_report(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    report1, report2 = _test_clearing_base()
    service = ClearingService()

    assert service._should_clear_report(report1, report1.owner)
    assert service._should_clear_report(report2, report2.owner)

    org = organization_fixture()
    org.settings.is_clearing_enabled = False
    assert not service._should_clear_report(report1, report1.owner)
    assert not service._should_clear_report(report2, report2.owner)
    org.settings.is_clearing_enabled = True
    assert service._should_clear_report(report1, report1.owner)
    assert service._should_clear_report(report2, report2.owner)

    report1.submission.is_verified = False
    assert not service._should_clear_report(report1, report1.owner)
    report1.submission.is_verified = True
    assert service._should_clear_report(report1, report1.owner)

    sb = report1.submission.businesses[0]
    report1.submission.businesses = []
    assert not service._should_clear_report(report1, report1.owner)
    report1.submission.businesses = [sb]
    assert service._should_clear_report(report1, report1.owner)


def test_handle_clearing_multiple_reports(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False, id=1)
    )
    r1 = _create_report()
    r2 = _create_report()
    r3 = _create_report()
    r4 = _create_report()
    r5 = _create_report()
    r6 = _create_report()

    r1.submission.is_verified = False
    r6.submission.clearing_status = ClearingStatus.BLOCKED
    r5.submission.clearing_issues = [
        SubmissionClearingIssue(submission_id=r5.submission.id, is_resolved=False, suspected_report_id=r2.id)
    ]
    r1.submission.clearing_status = ClearingStatus.PRE_CLEARING
    r2.submission.clearing_status = ClearingStatus.PRE_CLEARING
    r3.submission.clearing_status = ClearingStatus.PRE_CLEARING
    r4.submission.clearing_status = ClearingStatus.PRE_CLEARING
    db.session.commit()

    assert not r1.submission.clearing_issues
    assert not r2.submission.clearing_issues
    assert not r3.submission.clearing_issues
    assert not r4.submission.clearing_issues
    assert len(r5.submission.clearing_issues) == 1
    assert not r6.submission.clearing_issues

    service = ClearingService()
    service.handle_clearing(r2)

    assert not r1.submission.clearing_issues
    assert len(r2.submission.clearing_issues) == 4
    assert len(r3.submission.clearing_issues) == 1
    assert len(r4.submission.clearing_issues) == 1
    assert len(r5.submission.clearing_issues) == 1
    assert not r6.submission.clearing_issues

    r1.submission.is_verified = True
    service.handle_clearing(r1)
    assert len(r1.submission.clearing_issues) == 5
    assert not r1.submission.clearing_issues[0].is_resolved  # r2 was already cleared from the group so expect False
    assert len(r2.submission.clearing_issues) == 5  # was already cleared, it should have resolved and unresolved
    assert len(r3.submission.clearing_issues) == 2
    assert not r3.submission.clearing_issues[0].is_resolved
    assert len(r4.submission.clearing_issues) == 2
    assert not r4.submission.clearing_issues[0].is_resolved
    assert len(r5.submission.clearing_issues) == 1
    assert not r6.submission.clearing_issues


def test_clearing_by_setting_client_submission_id(app_context, mocker):
    user_settings = Settings(can_resolve_clearing_issues=True, client_id_required_for_clearing=True)
    user_mock = AnonObj(
        applicable_settings=user_settings,
        can_light_clear=True,
        is_internal_machine_user=False,
        email="<EMAIL>",
        is_being_impersonated=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=user_mock)
    report1, report2 = _test_clearing_base(client_id_required_for_clearing=True)
    report1.submission.clearing_status = ClearingStatus.CLEARED
    report2.submission.clearing_status = ClearingStatus.CLEARED

    assert report1.submission.clearing_status == ClearingStatus.CLEARED
    assert report2.submission.clearing_status == ClearingStatus.CLEARED

    report1.submission.clearing_status = ClearingStatus.PRE_CLEARING
    report2.submission.clearing_status = ClearingStatus.PRE_CLEARING
    db.session.commit()

    assert report1.submission.cleared_date is None
    assert report2.submission.cleared_date is None

    report1.submission.client_submission_ids = [SubmissionClientId(client_submission_id="bar")]
    report2.submission.client_submission_ids = [SubmissionClientId(client_submission_id="foo")]
    db.session.commit()

    current_date = datetime.datetime.now(tz=datetime.UTC)
    assert (
        current_date - datetime.timedelta(seconds=20)
        <= report1.submission.cleared_date.replace(tzinfo=datetime.UTC)
        <= current_date + datetime.timedelta(seconds=20)
    )
    assert (
        current_date - datetime.timedelta(seconds=20)
        <= report2.submission.cleared_date.replace(tzinfo=datetime.UTC)
        <= current_date + datetime.timedelta(seconds=20)
    )
    assert report1.submission.cleared_at is not None
    assert report2.submission.cleared_at is not None

    assert report1.submission.clearing_status == ClearingStatus.CLEARED
    assert report2.submission.clearing_status == ClearingStatus.CLEARED

    # Test fallback to submission audit if cleared_date is None
    report1.submission.cleared_date = None
    report2.submission.cleared_date = None
    db.session.commit()

    assert report1.submission.cleared_date is None
    assert report2.submission.cleared_date is None

    assert (
        current_date - datetime.timedelta(seconds=20)
        <= report1.submission.cleared_at.replace(tzinfo=datetime.UTC)
        <= current_date + datetime.timedelta(seconds=20)
    )
    assert (
        current_date - datetime.timedelta(seconds=20)
        <= report2.submission.cleared_at.replace(tzinfo=datetime.UTC)
        <= current_date + datetime.timedelta(seconds=20)
    )
    assert report1.submission.cleared_at is not None
    assert report2.submission.cleared_at is not None

    # Test fallback to client submission ids if submission audit is missing
    db.session.query(SubmissionAudit).filter(SubmissionAudit.submission_id == report1.submission.id).delete()
    db.session.query(SubmissionAudit).filter(SubmissionAudit.submission_id == report2.submission.id).delete()
    report1.submission.cleared_date = None
    report2.submission.cleared_date = None

    db.session.commit()

    assert report1.submission.cleared_date is None
    assert report2.submission.cleared_date is None

    assert report1.submission.cleared_at is not None
    assert report2.submission.cleared_at is not None

    report1.submission.client_submission_ids = []
    report2.submission.clearing_status = ClearingStatus.BLOCKED
    db.session.commit()

    assert report1.submission.clearing_status == ClearingStatus.PRE_CLEARING
    assert report2.submission.client_submission_ids == []


def test_clearing_by_setting_submission_identifier(app_context, mocker):
    user_settings = Settings(
        is_clearing_enabled=True, can_resolve_clearing_issues=True, client_id_required_for_clearing=True
    )
    user_mock = AnonObj(
        applicable_settings=user_settings,
        can_light_clear=True,
        is_internal_machine_user=False,
        email="<EMAIL>",
        is_being_impersonated=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=user_mock)
    organization_fixture(
        id=49,
    )
    user_fixture(organization_id=49, settings=user_settings)
    report, submission = report_and_submission_fixture(organization_id=49, clearing_status=ClearingStatus.PRE_CLEARING)
    assert report.submission.clearing_status == ClearingStatus.PRE_CLEARING

    submission.identifiers = [SubmissionIdentifier(identifier="ez", identifier_type="quote_number")]
    db.session.commit()
    assert report.submission.clearing_status == ClearingStatus.PRE_CLEARING

    submission.identifiers = [SubmissionIdentifier(identifier="ez", identifier_type="policy_number")]
    db.session.commit()
    assert report.submission.clearing_status == ClearingStatus.CLEARED

    submission.identifiers = []
    db.session.commit()
    assert report.submission.clearing_status == ClearingStatus.PRE_CLEARING


def test_clearing_with_prevent_clearing_updates(app_context, mocker):
    user_settings = Settings(
        is_clearing_enabled=True, can_resolve_clearing_issues=True, client_id_required_for_clearing=True
    )
    user_mock = AnonObj(
        applicable_settings=user_settings,
        can_light_clear=True,
        is_internal_machine_user=False,
        email="<EMAIL>",
        is_being_impersonated=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=user_mock)
    organization_fixture()
    user_fixture(settings=user_settings)
    report, submission = report_and_submission_fixture(clearing_status=ClearingStatus.PRE_CLEARING)
    assert report.submission.clearing_status == ClearingStatus.PRE_CLEARING

    submission.client_submission_ids = [SubmissionClientId(client_submission_id="ez")]
    db.session.commit()
    assert report.submission.clearing_status == ClearingStatus.CLEARED

    submission.prevent_clearing_updates = True
    submission.clearing_status = ClearingStatus.BLOCKED
    db.session.commit()

    assert report.submission.clearing_status == ClearingStatus.BLOCKED

    submission.prevent_clearing_updates = False
    db.session.commit()

    assert report.submission.clearing_status == ClearingStatus.CLEARED


def test_clearing_using_endpoint(app_context, mocker):
    user_settings = Settings(can_resolve_clearing_issues=True)
    user_mock = AnonObj(
        applicable_settings=user_settings,
        can_light_clear=True,
        is_internal_machine_user=False,
        email="<EMAIL>",
        is_being_impersonated=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=user_mock)
    report1, report2 = _test_clearing_base()
    report1.submission.clearing_status = ClearingStatus.PRE_CLEARING
    report2.submission.clearing_status = ClearingStatus.PRE_CLEARING

    org = organization_fixture()
    org.settings = Settings(is_clearing_enabled=True, clear_oldest_by_default=False)
    db.session.commit()

    assert not report1.submission.clearing_issues
    assert not report2.submission.clearing_issues

    invoke_clearing_for_submission(report1.submission.id, False)

    assert report1.submission.has_unresolved_clearing_issues
    assert report2.submission.has_unresolved_clearing_issues
    assert not report1.submission.clearing_issues[0].is_light
    assert not report2.submission.clearing_issues[0].is_light

    report1.submission.is_processing = False
    report1.submission.processing_state = SubmissionProcessingState.NEEDS_CLEARING
    db.session.commit()
    clear_submission(report1.submission.id, False)

    assert not report1.submission.has_unresolved_clearing_issues
    assert report2.submission.has_unresolved_clearing_issues


def test_light_clearing_subject_ilike(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    r1 = _create_report()
    r2 = _create_report()
    r3 = _create_report()
    r4 = _create_report()
    r5 = _create_report()
    r1.email_subject = "FOOOobar"
    r2.email_subject = "oooBa"
    r3.email_subject = None
    r4.email_subject = "SomethingElse"
    r5.email_subject = "ooBa"
    db.session.commit()

    assert not r1.submission.clearing_issues
    assert not r2.submission.clearing_issues
    assert not r3.submission.clearing_issues
    assert not r4.submission.clearing_issues
    assert not r5.submission.clearing_issues

    ClearingService().handle_clearing(r2, light_clearing_only=True)

    assert not r1.submission.has_unresolved_clearing_issues
    assert r2.submission.has_unresolved_clearing_issues

    assert r2.submission.clearing_issues[0].is_light
    assert (
        r2.submission.clearing_issues[0].reason == SUBJECT_ILIKE_REASON
        or r2.submission.clearing_issues[0].reason == SUBJECT_SIMILAR_REASON
    )
    assert len(r2.submission.clearing_issues) == 2
    sus_reports = [r.suspected_report_id for r in r2.submission.clearing_issues]
    assert r1.id in sus_reports
    assert r5.id in sus_reports
    assert not r3.submission.has_unresolved_clearing_issues
    assert not r4.submission.has_unresolved_clearing_issues
    assert not r5.submission.has_unresolved_clearing_issues


def test_get_similar_reports_light_subject_sim(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    r1 = _create_report()
    r2 = _create_report()
    r1.email_subject = "somethingsomething"
    r2.email_subject = "somethingsometh1ng"
    r2.full_pds = True
    db.session.commit()

    result = ClearingService()._get_similar_reports_light(r2.submission)

    assert len(result) == 1
    assert result[0][0] == r1
    assert result[0][1] == SUBJECT_SIMILAR_REASON


def test_get_similar_reports_light_same_files(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    r1 = _create_report()
    r2 = _create_report()
    r3 = _create_report()
    r1.submission.files = [File(file_type=FileType.EMAIL, checksum="123", name="email", origin=Origin.EMAIL)]
    r2.submission.files = [
        File(file_type=FileType.EMAIL, checksum="123", name="email", origin=Origin.EMAIL),
        File(file_type=FileType.ACORD_FORM, checksum="234", name="acord", origin=Origin.EMAIL),
    ]
    r3.submission.files = [
        File(file_type=FileType.EMAIL, checksum="123", name="email", origin=Origin.EMAIL),
        File(file_type=FileType.ACORD_FORM, checksum="234", name="acord", origin=Origin.EMAIL),
    ]
    r2.full_pds = True
    db.session.commit()

    result = ClearingService()._get_similar_reports_light(r2.submission)

    assert len(result) == 1
    assert result[0][0] == r3
    assert result[0][1] == SAME_FILE_REASON


def test_get_similar_reports_light_same_entity_data(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    r1 = _create_report()
    r2 = _create_report()
    r3 = _create_report()
    r4 = _create_report()
    r5 = _create_report()
    r6 = _create_report()
    r1.submission.files = [File(file_type=FileType.EMAIL, size=123, name="email", origin=Origin.EMAIL)]
    r2.submission.files = [File(file_type=FileType.EMAIL, size=123, name="email", origin=Origin.EMAIL)]
    r3.submission.files = [File(file_type=FileType.EMAIL, size=123, name="email", origin=Origin.EMAIL)]
    r4.submission.files = [File(file_type=FileType.EMAIL, size=123, name="email", origin=Origin.EMAIL)]
    r5.submission.files = [File(file_type=FileType.EMAIL, size=123, name="email", origin=Origin.EMAIL)]
    r1.submission.files[0].processed_file = ProcessedFile(
        business_resolution_data={"resolution_data": [{"requested_name": "foo"}]}, processed_data={}
    )
    r2.submission.files[0].processed_file = ProcessedFile(
        business_resolution_data={"resolution_data": [{"requested_name": "foo"}, {"requested_name": "bar"}]},
        processed_data={},
    )
    r3.submission.files[0].processed_file = ProcessedFile(
        business_resolution_data={"resolution_data": [{"requested_name": "bar"}]}, processed_data={}
    )
    r4.submission.files[0].processed_file = ProcessedFile(
        business_resolution_data={"resolution_data": [{"requested_name": "xyz"}]}, processed_data={}
    )
    r5.submission.files[0].processed_file = ProcessedFile(business_resolution_data={}, processed_data={})
    r2.full_pds = True
    db.session.commit()

    assert r2.submission.files[0].processed_file.resolution_requested_names == ["foo", "bar"]

    result = ClearingService()._get_similar_reports_light(r2.submission)

    assert len(result) == 2
    reports = [r[0] for r in result]
    assert r1 in reports
    assert r3 in reports
    assert result[0][1] == SAME_ENTITY_REASON


def test_get_similar_reports_light_no_conflict_with_processing_dependency(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    r1 = _create_report()
    r2 = _create_report()
    r1.email_subject = "somethingsomething"
    r2.email_subject = "somethingsometh1ng"
    r2.full_pds = True
    db.session.commit()

    result = ClearingService()._get_similar_reports_light(r2.submissions[0])
    assert len(result) == 1

    report_processing_dependency_fixture(report_id=r2.id, dependent_report_id=r1.id)
    db.session.commit()

    # Not conflicting because processing dependency
    result = ClearingService()._get_similar_reports_light(r2.submissions[0])
    assert len(result) == 0


def test_get_similar_reports_light_subject_sim_normalized(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    r1 = _create_report()
    r2 = _create_report()
    r1.email_subject = "Daws Trucking Inc. - Eff Date:01/01/2024 -- CRC# 11733037 #secure#"
    r2.email_subject = "Eff Trucking, Inc. - Eff Date:01/01/2024 -- CRC# 11745123 #secure#"
    r2.full_pds = True
    db.session.commit()

    ClearingService()._light_clear_submission(r2.submission, r2.owner)

    assert r2.submission.clearing_issues == []


def test_get_similar_reports_name(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    r1 = _create_report()
    r2 = _create_report()
    r1.name = "Mercy Montessori Center, Sisters of Mercy of the Americas South Central Community"
    r2.name = "MERCY MONTESSORI CENTER"
    db.session.commit()

    ClearingService()._light_clear_submission(r2.submission, r2.owner)

    assert len(r2.submission.clearing_issues) == 1


def test_get_similar_reports_name_for_boss(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    organization_fixture(id=6)
    r1 = _create_report()
    r2 = _create_report()
    r1.organization_id = 6
    r2.organization_id = 6
    r2.submission.origin = "API"

    r1.name = "Submission Confirmation WJ Surface Treatments LLC"
    r2.name = "WJ Surface Treatments LLC - eff. 6/14 - GL Submission"
    db.session.commit()

    ClearingService()._light_clear_submission(r2.submission, r2.owner)

    # Updated logic: BOSS subs now do not go through light clearing
    assert len(r2.submission.clearing_issues) == 0


def test_get_similar_reports_name_for_sync_shells(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    organization_fixture()
    r1 = _create_report()
    r2 = _create_report()
    r1.submission.origin = "SYNC"
    r1.submission.created_at = datetime.datetime(year=2024, month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
    r1.name = "subject"
    r2.name = "subject"
    db.session.commit()

    ClearingService()._light_clear_submission(r2.submission, r2.owner)

    assert len(r2.submission.clearing_issues) == 0


def test_no_conflict_when_different_brokerages(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    r1 = _create_report()
    r2 = _create_report()
    r3 = _create_report()
    r4 = _create_report()
    r5 = _create_report()
    r1.email_subject = "somethingsomething"
    r2.email_subject = "somethingsomething"
    r3.email_subject = "somethingsomething"
    r4.email_subject = "somethingsomething"
    r5.email_subject = "somethingsomething"
    a1 = brokerage_fixture(name="A1", domains=[], organization_id=r1.organization_id)
    a2 = brokerage_fixture(name="A2", domains=[], organization_id=r1.organization_id)
    r1.submission.brokerage_id = a1.id
    r2.submission.brokerage_id = a2.id
    r4.submission.brokerage_id = a2.id
    db.session.commit()

    # R3 has no agency set, so it conflicts with all
    result = ClearingService()._get_similar_reports_light(r3.submission)
    assert len(result) == 4

    # R2 conflicts with R3, R5 (no agency) and R4 (same agency)
    result = ClearingService()._get_similar_reports_light(r2.submission)
    assert len(result) == 3
    reports = [r[0] for r in result]
    assert r4 in reports
    assert r3 in reports
    assert r5 in reports

    # R1 only conflicts with R3, R5 (no agency)
    result = ClearingService()._get_similar_reports_light(r1.submission)
    assert len(result) == 2
    assert r3 in [r[0] for r in result]
    assert r5 in [r[0] for r in result]


def test_check_in_force_policies_as_conflict_same_agency(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "policy_expiration_date": datetime.datetime.now() + datetime.timedelta(days=400),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_kwargs={"brokerage_id": brokerage.id},
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission)

    assert len(report.submission.clearing_issues) == 1
    [clearing_issue] = report.submission.clearing_issues
    assert clearing_issue.suspected_report_id == active_policy_report.id

    assert len(report.submission.clearing_sub_statuses) == 1
    [clearing_sub_status] = report.submission.clearing_sub_statuses
    assert clearing_sub_status.status == ClearingSubStatus.CONFLICT
    assert clearing_sub_status.sub_status == "Active policy - Same agency"
    assert clearing_sub_status.submission_clearing_issue_id == clearing_issue.id

    assert report.submission.clearing_status == ClearingStatus.BLOCKED


def test_check_in_force_policies_as_conflict_same_agency_no_conflict_too_old(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": "01/01/2020",
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_kwargs={"brokerage_id": brokerage.id},
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission)

    assert len(report.submission.clearing_issues) == 0
    assert len(report.submission.clearing_sub_statuses) == 0
    assert report.submission.clearing_status == ClearingStatus.CLEARED


def test_check_in_force_policies_as_conflict_same_agency_no_proposed_effective_date_bug(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_kwargs={"brokerage_id": brokerage.id, "proposed_effective_date": None},
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission)

    assert len(report.submission.clearing_issues) == 1
    [clearing_issue] = report.submission.clearing_issues
    assert clearing_issue.suspected_report_id == active_policy_report.id

    assert len(report.submission.clearing_sub_statuses) == 1
    [clearing_sub_status] = report.submission.clearing_sub_statuses
    assert clearing_sub_status.status == ClearingSubStatus.CONFLICT
    assert clearing_sub_status.sub_status == "Active policy - Same agency"
    assert clearing_sub_status.submission_clearing_issue_id == clearing_issue.id

    assert report.submission.clearing_status == ClearingStatus.BLOCKED


def test_check_in_force_policies_as_conflict_same_agency_policy_expires_before_submission(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "policy_expiration_date": datetime.datetime.now() - datetime.timedelta(days=5),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_kwargs={
            "brokerage_id": brokerage.id,
            "proposed_effective_date": "01/01/2025",
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission)

    assert len(report.submission.clearing_issues) == 0
    assert len(report.submission.clearing_sub_statuses) == 0
    assert report.submission.clearing_status == ClearingStatus.CLEARED


def test_check_in_force_policies_as_conflict_same_agency_different_coverage(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_kwargs={"brokerage_id": brokerage.id},
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    coverage1 = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage1.id)
    coverage2 = coverage_fixture(
        name="Property coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id
    )
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage2.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission)

    assert len(report.submission.clearing_sub_statuses) == 0
    assert len(report.submission.clearing_issues) == 0
    assert report.submission.clearing_status == ClearingStatus.CLEARED


def test_check_in_force_policies_as_conflict_same_agency_same_sync_address(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "policy_expiration_date": datetime.datetime.now() + datetime.timedelta(days=400),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        submission_premises_kwargs={
            "address": "15 Broadway  ",
            "premises_id": UUID_1,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_kwargs={"brokerage_id": brokerage.id},
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        submission_premises_kwargs={
            "address": "   15 broadway",
            "premises_id": UUID_2,
        },
        broker_id=broker.id,
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission, True)

    assert len(report.submission.clearing_issues) == 1
    [clearing_issue] = report.submission.clearing_issues
    assert clearing_issue.suspected_report_id == active_policy_report.id

    assert len(report.submission.clearing_sub_statuses) == 1
    [clearing_sub_status] = report.submission.clearing_sub_statuses
    assert clearing_sub_status.status == ClearingSubStatus.CONFLICT
    assert clearing_sub_status.sub_status == "Active policy - Same agency"
    assert clearing_sub_status.submission_clearing_issue_id == clearing_issue.id

    assert report.submission.clearing_status == ClearingStatus.BLOCKED


def test_check_in_force_policies_as_conflict_same_agency_same_sync_premises_id(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "policy_expiration_date": datetime.datetime.now() + datetime.timedelta(days=400),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        submission_premises_kwargs={
            "address": "15 Broadway, New York",
            "premises_id": UUID_1,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_kwargs={"brokerage_id": brokerage.id},
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        submission_premises_kwargs={
            "address": "15 Broadway, NYC",
            "premises_id": UUID_1,
        },
        broker_id=broker.id,
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission, True)

    assert len(report.submission.clearing_issues) == 1
    [clearing_issue] = report.submission.clearing_issues
    assert clearing_issue.suspected_report_id == active_policy_report.id

    assert len(report.submission.clearing_sub_statuses) == 1
    [clearing_sub_status] = report.submission.clearing_sub_statuses
    assert clearing_sub_status.status == ClearingSubStatus.CONFLICT
    assert clearing_sub_status.sub_status == "Active policy - Same agency"
    assert clearing_sub_status.submission_clearing_issue_id == clearing_issue.id

    assert report.submission.clearing_status == ClearingStatus.BLOCKED


def test_check_in_force_policies_as_conflict_same_agency_different_sync_address(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "policy_expiration_date": datetime.datetime.now() + datetime.timedelta(days=400),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        submission_premises_kwargs={
            "address": "15 Broadway, New York",
            "premises_id": UUID_1,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_kwargs={"brokerage_id": brokerage.id},
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        submission_premises_kwargs={
            "address": "Some other address",
            "premises_id": UUID_2,
        },
        broker_id=broker.id,
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission, True)

    assert len(report.submission.clearing_issues) == 0
    assert len(report.submission.clearing_sub_statuses) == 0
    assert report.submission.clearing_status == ClearingStatus.CLEARED


def test_check_in_force_policies_as_conflict_different_agency(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_business_kwargs={
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission)

    assert len(report.submission.clearing_sub_statuses) == 1
    [clearing_sub_status] = report.submission.clearing_sub_statuses
    assert clearing_sub_status.status == ClearingSubStatus.CONFLICT
    assert clearing_sub_status.sub_status == "Active policy - Different agency"

    assert len(report.submission.clearing_issues) == 1
    [clearing_issue] = report.submission.clearing_issues
    assert clearing_issue.suspected_report_id == active_policy_report.id
    assert clearing_issue.clearing_sub_statuses == [clearing_sub_status]

    assert report.submission.clearing_status == ClearingStatus.BLOCKED


def test_check_in_force_policies_as_conflict_different_agency_business_id(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    org = organization_fixture()
    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(organization_id=org.id, brokerage_id=brokerage.id, name="Broker 1", email="<EMAIL>")
    business_id = uuid.uuid4()
    business_name = "Business Name"
    business_address = "Broadway 15"
    active_policy_report = _create_report(
        submission_kwargs={
            "stage": SubmissionStage.QUOTED_BOUND,
            "policy_status": "Active",
            "proposed_effective_date": datetime.datetime.now() - datetime.timedelta(days=10),
            "brokerage_id": brokerage.id,
        },
        submission_business_kwargs={
            "business_id": business_id,
            "requested_name": business_name,
            "requested_address": business_address,
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
        broker_id=broker.id,
    )
    report = _create_report(
        submission_business_kwargs={
            "business_id": business_id,
            "requested_name": business_name + "not same",
            "requested_address": business_address + "not same",
            "named_insured": SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        },
    )
    coverage = coverage_fixture(name="Crime coverage", coverage_types=[CoverageType.PRIMARY], organization_id=org.id)
    submission_coverage_fixture(submission_id=active_policy_report.submission.id, coverage_id=coverage.id)
    submission_coverage_fixture(submission_id=report.submission.id, coverage_id=coverage.id)

    ClearingService()._check_in_force_policies_as_conflict(report.submission)

    assert len(report.submission.clearing_sub_statuses) == 1
    [clearing_sub_status] = report.submission.clearing_sub_statuses
    assert clearing_sub_status.status == ClearingSubStatus.CONFLICT

    assert len(report.submission.clearing_issues) == 1
    [clearing_issue] = report.submission.clearing_issues
    assert clearing_issue.suspected_report_id == active_policy_report.id
    assert clearing_issue.clearing_sub_statuses == [clearing_sub_status]

    assert report.submission.clearing_status == ClearingStatus.BLOCKED


def test_light_clearing_coverages(app_context, mocker):
    mocker.patch("flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False))
    r1 = _create_report()
    r2 = _create_report()
    r3 = _create_report()
    r4 = _create_report()
    r5 = _create_report()
    r6 = _create_report()
    r7 = _create_report()
    r1.email_subject = "somethingsomething"
    r2.email_subject = "somethingsomething"
    r3.email_subject = "somethingsomething"
    r4.email_subject = "somethingsomething"
    r5.email_subject = "somethingsomething"
    r6.email_subject = "somethingsomething"
    r7.email_subject = "somethingsomething"
    cov1 = coverage_fixture(
        name="1", coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS], organization_id=r1.organization_id
    )
    cov2 = coverage_fixture(name="2", coverage_types=[], organization_id=r1.organization_id)
    cov3 = coverage_fixture(name="3", coverage_types=[], organization_id=r1.organization_id)
    cov4 = coverage_fixture(name="4", coverage_types=[], organization_id=r1.organization_id)
    submission_coverage_fixture(submission_id=r1.submission.id, coverage_id=cov1.id)
    submission_coverage_fixture(submission_id=r1.submission.id, coverage_id=cov2.id)
    submission_coverage_fixture(submission_id=r2.submission.id, coverage_id=cov2.id)
    submission_coverage_fixture(submission_id=r2.submission.id, coverage_id=cov3.id)
    submission_coverage_fixture(submission_id=r3.submission.id, coverage_id=cov4.id)
    submission_coverage_fixture(submission_id=r6.submission.id, coverage_id=cov1.id, coverage_type=CoverageType.PRIMARY)
    submission_coverage_fixture(submission_id=r7.submission.id, coverage_id=cov1.id, coverage_type=CoverageType.EXCESS)
    db.session.commit()

    # R1 has overlap of coverages with R2, R4 & R5 do not have coverages.
    result = ClearingService()._get_similar_reports_light(r1.submission)
    assert len(result) == 4
    assert r2 in [r[0] for r in result]
    assert r4 in [r[0] for r in result]
    assert r5 in [r[0] for r in result]
    assert r6 in [r[0] for r in result]

    result = ClearingService()._get_similar_reports_light(r2.submission)
    assert len(result) == 3
    assert r1 in [r[0] for r in result]
    assert r4 in [r[0] for r in result]
    assert r5 in [r[0] for r in result]

    result = ClearingService()._get_similar_reports_light(r3.submission)
    assert len(result) == 2
    assert r4 in [r[0] for r in result]
    assert r5 in [r[0] for r in result]

    result = ClearingService()._get_similar_reports_light(r4.submission)
    assert len(result) == 6


def _run_bowhead_clearing_test(
    app_context,
    mocker,
    relation_type,
    create_matched_submission=True,
    expected_clearing_status=ClearingStatus.PRE_CLEARING,
    expected_issue_count=0,
    expected_issue_reason=None,
    expected_premises_created=False,
    check_is_renewal=False,
):
    org_id = ExistingOrganizations.BowheadSpecialty.value
    r1 = _create_report(organization_id=org_id, report_name="current report")
    r2 = None
    s2_premises = None

    if create_matched_submission:
        r2 = _create_report(organization_id=org_id, report_name="conflict report")

        s2_premises = submission_premises_fixture(
            submission_id=r2.submission.id,
            address="456 Oak St, Chicago, IL",
            named_insured="ACME Construction Company",
            premises_id=uuid.uuid4(),
            organization_id=org_id,
            submission_premises_type=SubmissionPremisesType.SYNC,
        )
    db.session.commit()

    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled", return_value=True)
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False, id=1)
    )
    mocker.patch(
        "copilot.logic.clearing.clearing_service.BowheadService.get_matching_submission_premises", return_value=[]
    )

    bowhead_relations = [
        BowheadRelationResult(
            current_submission=r1.submission,
            matched_submission=r2.submission if create_matched_submission else None,
            matched_submission_premises=s2_premises if create_matched_submission else None,
            relation_type=relation_type,
        )
    ]
    mocker.patch("copilot.logic.clearing.bowhead.BowheadService.analyze_relations", return_value=bowhead_relations)
    # Make sure we don't have any other clearing issues coming from common test setup
    mocker.patch("copilot.logic.clearing.clearing_service.ClearingService._get_similar_reports", return_value=[])

    r1.submission.clearing_status = ClearingStatus.PRE_CLEARING
    db.session.commit()

    service = ClearingService()
    service.handle_clearing(r1)

    # Verify the number of clearing issues
    assert len(r1.submission.clearing_issues) == expected_issue_count
    assert r1.submission.clearing_status == expected_clearing_status

    # Check clearing issue details if expected
    if expected_issue_count > 0:
        clearing_issue = r1.submission.clearing_issues[0]
        if expected_issue_reason:
            assert clearing_issue.reason == expected_issue_reason
        if create_matched_submission:
            assert clearing_issue.suspected_report_name == "conflict report"

    # Check if submission premises were created
    created_submission_premises = (
        db.session.query(SubmissionPremises).filter(SubmissionPremises.submission_id == r1.submission.id).first()
    )

    if expected_premises_created:
        assert created_submission_premises is not None
        assert created_submission_premises.submission_premises_type == SubmissionPremisesType.COPY
        assert created_submission_premises.address == "456 Oak St, Chicago, IL"
        assert created_submission_premises.named_insured == "ACME Construction Company"
    else:
        assert created_submission_premises is None

    # Check if submission is marked as renewal if needed
    if check_is_renewal:
        assert r1.submission.is_renewal is True


def test_handle_clearing_bowhead_match_by_premises_for_different_broker_conflict(app_context, mocker):
    _run_bowhead_clearing_test(
        app_context=app_context,
        mocker=mocker,
        relation_type=BowheadRelationType.CONFLICT_FOR_DIFFERENT_BROKER,
        create_matched_submission=True,
        expected_clearing_status=ClearingStatus.IN_CLEARING_CONFLICT,
        expected_issue_count=1,
        expected_issue_reason="Different broker already holds in-force policy",
        expected_premises_created=True,
    )


def test_handle_clearing_bowhead_match_by_premises_for_completely_new_account(app_context, mocker):
    _run_bowhead_clearing_test(
        app_context=app_context,
        mocker=mocker,
        relation_type=BowheadRelationType.NO_CONFLICT_AND_NEW_ACCOUNT,
        create_matched_submission=False,
        expected_clearing_status=ClearingStatus.PRE_CLEARING,
        expected_issue_count=0,
        expected_premises_created=False,
    )


def test_handle_clearing_bowhead_match_by_premises_for_renewal(app_context, mocker):
    _run_bowhead_clearing_test(
        app_context=app_context,
        mocker=mocker,
        relation_type=BowheadRelationType.RENEWAL,
        create_matched_submission=True,
        expected_clearing_status=ClearingStatus.PRE_CLEARING,
        expected_issue_count=0,
        expected_premises_created=True,
        check_is_renewal=True,
    )


@pytest.mark.parametrize(
    "case",
    [
        ["aaaaaaaa", "aaaaaaaa", True],
        [
            "FW: Gilliam Construction, Inc - 7/13/2024 Paragon Insurance Holdin",
            "FW: EST Construction LLC - 7/1/2024 Paragon Insurance Holdin",
            False,
        ],
        ["Work Comp Submission - Freight Hawk USA, LLC", "Work Comp Submission", False],
        ["FW: Jerry & Belenda, Inc. Work Comp Submission - TKG", "Work Comp Submission", False],
        [
            "Skywalker Holdings, LLC, File # BR69986-01 - EXCESS SUBMISSION FOR $5M x $10 & $10M x $15M",
            (
                "Skywalker Holdings, LLC, File # BR69986-01 - Lead Everest $10M Quote Attached. Looking for $5M x $10M"
                " x Primary and option for $10M x $10M x Primary if possible."
            ),
            True,
        ],
        ["a llc B", "bbbb", False],
        [
            "FW: Wilshire Boulevard Temple - Workers Compensation Policy Submission (Eff. 6/1/24)",
            "FW: (EXTERNAL) FW: Workers Comp",
            False,
        ],
        [
            (
                "Duit Construction Co., Inc - Excess Liability Submission **Please clear on behalf of Scott Jensen for"
                " Casualty**"
            ),
            "Sonar Inc. - Excess Liability Submission **Please clear on behalf of Scott Jensen for Casualty**",
            False,
        ],
        [
            "DECLINE – BELOW MINIMUM PREMIUM - Aurora world, inc.",
            "New Business Submission Aurora World WC Eff 9/4/24",
            True,
        ],
        ["SPIGEN, INC - 024469617", "Spigen Inc - 024231263", True],
        [
            "JM Transportation Solutions, LLC - Effective 8/15/2024",
            "JM Transportation Group, LLC - WC Eff. 8/15/24",
            True,
        ],
        ["Declined : Incomplete- ATKINS SHEEP RANCH INC.", "Atkins Sheep Ranch, Inc. - 024482523", True],
        ["Atkins Sheep Ranch, Inc. - 024482523", "Declined : Incomplete- ATKINS SHEEP RANCH INC.", True],
        ["DeBoer Homes Inc / Eff: 07-01 / Workers Compensation Submission", "DeBoer Homes Inc 07/01/2024 WC", True],
        [
            "Gaspar, Jesus dba Jesus Gaspar Landscaping EFF. 7/28/2024",
            "Jesus Gaspar - Decline - Missing Information",
            True,
        ],
        [
            "Jesus Gaspar - Decline - Missing Information",
            "Gaspar, Jesus dba Jesus Gaspar Landscaping EFF. 7/28/2024",
            True,
        ],
        ["Safeware, Inc (Primary)", "SAFEWARE INC new Business please clear", True],
        ["Decline - INTERSTATE DRYWALL INC", "Interstate Drywall 8/1", True],
        [
            "Morley Builders, Inc. - Project Name: Skechers - NEW SUBMISSION",
            "NEW BUSINESS SUBMISSION - Prince And Sons Inc, SJP Enterprises LLC - EFF 9/1",
            False,
        ],
        [
            "BOR - American Orthodontics Corp - Policy # CXS4006426",
            "New XS Submission - American Orthodontics Corporation - 8/1/24",
            True,
        ],
        ["", "aaaa", False],
        [None, "aaaa", False],
    ],
)
def test_get_similar_reports_light_subjects(app_context, mocker, case):
    mocker.patch(
        "flask_login.utils._get_user", return_value=AnonObj(email="<EMAIL>", is_being_impersonated=False)
    )
    r1 = _create_report()
    r2 = _create_report()
    s1, s2, should_match = case
    r1.email_subject = s1
    r2.email_subject = s2
    r2.full_pds = True
    db.session.commit()

    ClearingService()._light_clear_submission(r2.submission, r2.owner)

    if should_match:
        assert r2.submission.clearing_issues != []
    else:
        assert r2.submission.clearing_issues == []
