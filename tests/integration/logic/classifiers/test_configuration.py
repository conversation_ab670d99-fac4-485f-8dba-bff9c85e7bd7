from unittest.mock import MagicMock

from facts_client_v2.model.fact_subtype import FactSubtype
from llm_common.clients.llm_client import AbstractLLMClient
from static_common.enums.classification import ClassifierOutputType, InputProcessingType
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.file_type import FileType
import pytest

from copilot.logic.classifiers.configuration import (
    CustomizableClassifierAutoconfigurator,
)
from copilot.logic.classifiers.models import (
    AutoconfigurationResources,
    LLMGeneratedPhrase,
    LLMPhrases,
)
from copilot.models import db
from tests.integration.factories import (
    classifier_config_fixture,
    classifier_version_fixture,
    customizable_classifier_v2_fixture,
)


@pytest.fixture
def resources(app, app_context):
    mock_llm_client = MagicMock(spec=AbstractLLMClient)
    mock_llm_client.get_llm_response.side_effect = [
        LLMPhrases(phrases=[LLMGeneratedPhrase(phrase="text", exclude=["test_exclude"], weight=1.0)]),
        "Test question",
    ]

    fact_subtypes = {
        "test_subtype": FactSubtype(
            description="Test Description",
        )
    }

    return AutoconfigurationResources(llm_client=mock_llm_client, fact_subtypes=fact_subtypes)


@pytest.fixture
def classifier():
    return customizable_classifier_v2_fixture(
        fact_subtype_id="test_subtype",
        input_types=[FileType.SUPPLEMENTAL_FORM.value],
        output_type=ClassifierOutputType.NUMERIC,
    )


@pytest.fixture
def version(classifier):
    return classifier_version_fixture(
        classifier_id=classifier.id,
        classifier_description="Test Description",
    )


def test_full_autoconfiguration_flow(app_context, resources, classifier, version):
    autoconfigurator = CustomizableClassifierAutoconfigurator(resources)

    result = autoconfigurator.autoconfigure(classifier, version)
    db.session.commit()

    assert result is not None
    assert result == classifier
    assert len(version.configs) > 0

    config = version.configs[0]
    assert FileType.SUPPLEMENTAL_FORM.value in config.input_types
    assert len(config.versions) > 0

    processing_types = set()
    for version in config.versions:
        processing_types.add(version.input_processing_type)

    expected_types = {
        InputProcessingType.KV_PAIRS,
    }
    assert processing_types == expected_types


def test_autoconfiguration_with_existing_config(app_context, resources, classifier, version):
    existing_config = classifier_config_fixture(
        input_types=[FileType.SUPPLEMENTAL_FORM.value],
    )
    version.configs.append(existing_config)

    autoconfigurator = CustomizableClassifierAutoconfigurator(resources)
    result = autoconfigurator.autoconfigure(classifier, version)
    db.session.commit()

    assert result is not None
    assert result == classifier
    assert len(version.configs) == 1  # Should not add new configs


def test_autoconfiguration_with_multiple_input_types(app_context, resources, classifier, version):
    classifier.input_types = [
        FileType.SUPPLEMENTAL_FORM.value,
        FileType.EMAIL.value,
        DocumentTypeID.NEWS.value,
        DocumentTypeID.HOMEPAGE.value,
        "Custom - Test File",
    ]
    db.session.commit()

    autoconfigurator = CustomizableClassifierAutoconfigurator(resources)
    result = autoconfigurator.autoconfigure(classifier, version)
    db.session.commit()

    assert result is not None
    assert result == classifier
    assert (
        len(version.configs) == 4
    )  # Should create 3 configs - 1 for email and supplemental, custom file type each and 1 for documents

    # Verify that each config has appropriate versions
    for config in version.configs:
        if DocumentTypeID.NEWS.value in config.input_types:
            assert DocumentTypeID.HOMEPAGE.value in config.input_types

        processing_types = {v.input_processing_type for v in config.versions}
        if FileType.SUPPLEMENTAL_FORM.value in config.input_types:
            assert processing_types == {
                InputProcessingType.KV_PAIRS,
            }
        else:
            assert processing_types == {InputProcessingType.OCR_TEXT}
