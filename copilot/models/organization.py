from datetime import <PERSON><PERSON><PERSON>
from enum import Enum

from infrastructure_common.logging import get_logger
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, Interval, Sequence, String, func
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.company import Company
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations, OrganizationGroups

from copilot.models._private import db
from copilot.utils import extract_domain

logger = get_logger()
PRIORITY_MULTIPLIER = 10


class OrganizationPriorityConfig:
    def __init__(
        self, default_priority: int, older_subs_priority: int | None = None, cutoff_minutes: int | None = None
    ):
        self.default_priority = default_priority * PRIORITY_MULTIPLIER
        self.older_subs_priority = older_subs_priority * PRIORITY_MULTIPLIER if older_subs_priority else None
        self.cutoff_minutes = cutoff_minutes


class Organization(db.Model):
    NSM_MASTER_GROUP_NAME = "NSM"
    PARAGON_MASTER_GROUP_NAME = "Paragon"
    KALEPA_NEW_DEMO_MASTER_GROUP_NAME = "Kalepa Users"
    KALEPA_DEMO_MASTER_GROUP_NAME = "Kalepa Users"
    K2_MASTER_GROUP_NAME = "K2"

    K2_FORWARD_EMAIL_TO_GROUP = {
        "<EMAIL>": "Vikco",
        "<EMAIL>": "Aegis Specialty Dealers",
    }
    PARAGON_FORWARD_EMAIL_TO_GROUP = {
        "<EMAIL>": "Ally Auto",
        "<EMAIL>": "E&S Casualty",
        "<EMAIL>": "E&S Casualty",
        "<EMAIL>": "Workers Comp",
        "<EMAIL>": "PSP E3",
        "<EMAIL>": "Trident Public Risk",
    }
    MERCHANTS_FORWARD_EMAIL_TO_GROUP = {
        "<EMAIL>": "CEP",
        "<EMAIL>": "Admitted",
    }
    NSM_EMAIL_TO_GROUP = {
        "<EMAIL>": "CPS",
        "<EMAIL>": "HabPro",
        "<EMAIL>": "KBK",
        "<EMAIL>": "KBK",
        "<EMAIL>": "KBK",
    }
    PARAGON_ORG_GROUP_TO_USER_GROUP = {
        OrganizationGroups.PARAGON_PSP_E3.value: "PSP E3",
        OrganizationGroups.PARAGON_ALLY_AUTO.value: "Ally Auto",
        OrganizationGroups.PARAGON_WC.value: "Workers Comp",
        OrganizationGroups.PARAGON_XS.value: "E&S Casualty",
        OrganizationGroups.PARAGON_TRIDENT.value: "Trident Public Risk",
    }

    class Owners(Enum):
        """
        Enum which holds mappings between organization name and owner user emails.
        DB First approach is used here
        """

        KalepaTest = "<EMAIL>"
        Nationwide = "<EMAIL>"
        KalepaMGA = "<EMAIL>"
        KalepaDemo = "<EMAIL>"
        Arch = "<EMAIL>"
        GuideOne = "<EMAIL>"
        MunichRe = "<EMAIL>"
        Paragon = "<EMAIL>"
        NorthStarMutual = "<EMAIL>"
        NecSpecialty = "<EMAIL>"
        GoldenBear = "<EMAIL>"
        NSM = "<EMAIL>"
        KalepaNewDemo = "<EMAIL>"
        OmahaNational = "<EMAIL>"
        WCF = "<EMAIL>"
        BoltonStreet = "<EMAIL>"
        ZurichNA = "<EMAIL>"
        CrcGroup = "<EMAIL>"
        AdmiralInsuranceGroup = "<EMAIL>"
        MSIGNA = "<EMAIL>"
        FCCIGroup = "<EMAIL>"
        QualityAudit = "<EMAIL>"
        BowheadSpecialty = "<EMAIL>"
        Travelers = "<EMAIL>"
        MerchantsGroup = "<EMAIL>"
        NationwideML = "<EMAIL>"
        BishopConifer = "<EMAIL>"
        ARU = "<EMAIL>"
        ISC = "<EMAIL>"
        K2 = "<EMAIL>"
        MarkelDemo = "<EMAIL>"
        CNA = "<EMAIL>"
        SECURA = "<EMAIL>"
        Vivere = "<EMAIL>"
        AIG = "<EMAIL>"
        AdmiralInsuranceGroupTest = "<EMAIL>"
        BowheadSpecialtyTest = "<EMAIL>"

    __tablename__ = "organization"

    id = Column(Integer, Sequence("organization_id_seq"), primary_key=True)
    name = Column(String(200), unique=True)
    description = Column(String)
    appetite = Column(JSONB)
    renewal_creation_interval = Column(Interval, nullable=False, default=timedelta(days=95))
    identity_provider = Column(String)
    settings = relationship("Settings", lazy="joined", uselist=False)
    email_domain = Column(String, unique=True)
    shared_notification_address = Column(String)
    for_analysis = Column(Boolean, default=True)

    @property
    def is_kalepa_test(self) -> bool:
        return Organization.is_kalepa_test_for_id(self.id)

    @staticmethod
    def is_kalepa_test_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.KalepaTest.value

    @property
    def is_kalepa_mga(self) -> bool:
        return Organization.is_kalepa_mga_for_id(self.id)

    @staticmethod
    def is_kalepa_mga_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.KalepaMGA.value

    @property
    def is_kalepa_demo(self) -> bool:
        return Organization.is_kalepa_demo_for_id(self.id)

    @staticmethod
    def is_kalepa_demo_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.KalepaDemo.value

    @staticmethod
    def is_kalapa_new_demo_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.KalepaNewDemo.value

    @property
    def is_nationwide(self) -> bool:
        return Organization.is_nationwide_for_id(self.id)

    @staticmethod
    def is_nationwide_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.Nationwide.value

    @staticmethod
    def is_nationwide_ml_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.NationwideML.value

    @staticmethod
    def is_k2_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.K2.value

    @property
    def is_arch(self) -> bool:
        return Organization.is_arch_for_id(self.id)

    @staticmethod
    def is_arch_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.Arch.value

    @property
    def is_arch_test(self) -> bool:
        return Organization.is_arch_test_for_id(self.id)

    @staticmethod
    def is_arch_test_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.ArchTest.value

    @staticmethod
    def is_arch_or_arch_test_for_id(organization_id: int) -> bool:
        return Organization.is_arch_test_for_id(organization_id) or Organization.is_arch_for_id(organization_id)

    @staticmethod
    def is_nsm_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.NSM.value

    @property
    def is_paragon(self) -> bool:
        return Organization.is_paragon_for_id(self.id)

    @staticmethod
    def is_paragon_for_id(organization_id: int | None) -> bool:
        return organization_id == ExistingOrganizations.Paragon.value

    @staticmethod
    def is_merchants_for_id(organization_id: int | None) -> bool:
        return organization_id == ExistingOrganizations.MerchantsGroup.value

    @staticmethod
    def is_quality_audit_for_id(organization_id: int | None) -> bool:
        return organization_id == ExistingOrganizations.QualityAudit.value

    @property
    def is_guideone(self) -> bool:
        return Organization.is_guideone_for_id(self.id)

    def is_auto_processing_enabled(self, tier: int | None) -> bool:
        if tier is None or self.settings.max_tier_for_auto_processing is None:
            return False
        return tier <= self.settings.max_tier_for_auto_processing

    @property
    def is_document_ingestion_enabled(self) -> bool:
        return self.settings and self.settings.is_document_ingestion_enabled

    @property
    def is_email_classification_enabled(self) -> bool:
        return self.settings and self.settings.is_email_classification_enabled

    @property
    def is_strict_clearance_controls_enabled(self) -> bool:
        return self.settings and self.settings.strict_clearance_controls

    @staticmethod
    def is_triage_processing_enabled_for_id(organization_id: int) -> bool:
        # Temp:
        from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType

        if organization_id == ExistingOrganizations.SECURA.value and FeatureFlagsClient.is_feature_enabled(
            FeatureType.SECURA_MAPPINGS_ENABLED
        ):
            return True
        # return organization_id in {ExistingOrganizations.Paragon.value, ExistingOrganizations.SECURA.value}
        return organization_id in {ExistingOrganizations.Paragon.value}

    def is_triage_processing_enabled(self) -> bool:
        return self.is_triage_processing_enabled_for_id(self.id)

    @staticmethod
    def is_primary_naics_code_required_for_triage_processing_for_org_id(organization_id: int) -> bool:
        return organization_id in {ExistingOrganizations.Paragon.value}

    def is_primary_naics_code_required_for_triage_processing(self) -> bool:
        return self.is_primary_naics_code_required_for_triage_processing_for_org_id(self.id)

    @staticmethod
    def is_guideone_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.GuideOne.value

    @staticmethod
    def is_munich_re_for_id(organization_id: int | None) -> bool:
        return organization_id == ExistingOrganizations.MunichRe.value

    @property
    def is_munich_re(self) -> bool:
        return Organization.is_munich_re_for_id(self.id)

    @staticmethod
    def is_nec_specialty_for_id(organization_id: int | None) -> bool:
        return organization_id == ExistingOrganizations.NecSpecialty.value

    @property
    def is_nec_specialty(self) -> bool:
        return Organization.is_nec_specialty_for_id(self.id)

    @staticmethod
    def is_omaha_national_for_id(organization_id: int | None) -> bool:
        return organization_id == ExistingOrganizations.OmahaNational.value

    @property
    def is_omaha_national(self) -> bool:
        return Organization.is_omaha_national_for_id(self.id)

    @staticmethod
    def is_admiral_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.AdmiralInsuranceGroup.value

    @property
    def is_admiral(self) -> bool:
        return Organization.is_admiral_for_id(self.id)

    @staticmethod
    def is_admiral_test_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.AdmiralInsuranceGroupTest.value

    @property
    def is_admiral_test(self) -> bool:
        return Organization.is_admiral_test_for_id(self.id)

    @staticmethod
    def is_admiral_or_admiral_test_for_id(organization_id: int) -> bool:
        return Organization.is_admiral_for_id(organization_id) or Organization.is_admiral_test_for_id(organization_id)

    @staticmethod
    def is_crc_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.CrcGroup.value

    @property
    def is_crc(self) -> bool:
        return Organization.is_crc_for_id(self.id)

    @staticmethod
    def is_bowhead_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.BowheadSpecialty.value

    @staticmethod
    def is_bowhead_test_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.BowheadSpecialtyTest.value

    @staticmethod
    def is_bowhead_or_bowhead_test_for_id(organization_id: int) -> bool:
        return Organization.is_bowhead_for_id(organization_id) or Organization.is_bowhead_test_for_id(organization_id)

    @staticmethod
    def is_aru_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.ARU.value

    @property
    def is_bowhead(self) -> bool:
        return Organization.is_bowhead_for_id(self.id)

    @property
    def is_bowhead_test(self) -> bool:
        return Organization.is_bowhead_test_for_id(self.id)

    @staticmethod
    def is_bishop_conifer_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.BishopConifer.value

    @property
    def is_bishop_conifer(self) -> bool:
        return Organization.is_bishop_conifer_for_id(self.id)

    @staticmethod
    def is_isc_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.ISC.value

    @property
    def is_isc(self) -> bool:
        return Organization.is_isc_for_id(self.id)

    @staticmethod
    def is_cna_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.CNA.value

    @property
    def is_cna(self) -> bool:
        return Organization.is_cna_for_id(self.id)

    @staticmethod
    def is_secura_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.SECURA.value

    @property
    def is_secura(self) -> bool:
        return Organization.is_secura_for_id(self.id)

    @staticmethod
    def is_aig_for_id(organization_id: int) -> bool:
        return organization_id == ExistingOrganizations.AIG.value

    @property
    def is_document_ingestion(self) -> bool:
        return ExistingOrganizations.MarkelDemo.value == self.id

    @staticmethod
    def get_id_by_email(email: str) -> int | None:
        log = logger.bind(email=email)
        log.info("Getting Organization by email domain")
        email_domain = extract_domain(email)
        organization = (
            db.session.query(Organization).filter(func.lower(Organization.email_domain) == email_domain.lower()).first()
        )

        if not organization:
            log.warning("Unable to find matching Organization for domain extracted from email address")
            raise Exception("Unable to find matching Organization for domain extracted from email address")

        log.info("Found Organization for email address", organization_id=organization.id)
        return organization.id

    @staticmethod
    def get_company_for_id(organization_id: int) -> Company | None:
        org_to_company = {
            ExistingOrganizations.Nationwide.value: Company.NATIONWIDE,
            ExistingOrganizations.Arch.value: Company.ARCH,
            ExistingOrganizations.GuideOne.value: Company.GUIDEONE,
            ExistingOrganizations.KalepaTest.value: Company.TEST,
            ExistingOrganizations.MunichRe.value: Company.MUNICHRE,
            ExistingOrganizations.Paragon.value: Company.PARAGON,
            ExistingOrganizations.KalepaMGA.value: Company.KIS,
            ExistingOrganizations.KalepaDemo.value: Company.KALEPA,
            ExistingOrganizations.NorthStarMutual.value: Company.NORTH_STAR_MUTUAL,
            ExistingOrganizations.NecSpecialty.value: Company.NEC_SPECIALTY,
            ExistingOrganizations.GoldenBear.value: Company.GOLDEN_BEAR,
            ExistingOrganizations.NSM.value: Company.NSM,
            ExistingOrganizations.KalepaNewDemo.value: Company.NEW_DEMO,
            ExistingOrganizations.OmahaNational.value: Company.OMAHA_NATIONAL,
            ExistingOrganizations.WCF.value: Company.WCF,
            ExistingOrganizations.BoltonStreet.value: Company.BOLTON_STREET,
            ExistingOrganizations.ZurichNA.value: Company.ZURICH_NA,
            ExistingOrganizations.AdmiralInsuranceGroup.value: Company.ADMIRAL_INSURANCE_GROUP,
            ExistingOrganizations.CrcGroup.value: Company.CRC_GROUP,
            ExistingOrganizations.MSIGNA.value: Company.MSIG_NA,
            ExistingOrganizations.FCCIGroup.value: Company.FCCI_GROUP,
            ExistingOrganizations.QualityAudit.value: Company.QUALITY_AUDIT,
            ExistingOrganizations.BowheadSpecialty.value: Company.BOWHEAD_SPECIALTY,
            ExistingOrganizations.Travelers.value: Company.TRAVELERS,
            ExistingOrganizations.MerchantsGroup.value: Company.MERCHANTS_GROUP,
            ExistingOrganizations.NationwideML.value: Company.NATIONWIDE_ML,
            ExistingOrganizations.BishopConifer.value: Company.BISHOP_CONIFER,
            ExistingOrganizations.ARU.value: Company.ARU,
            ExistingOrganizations.ISC.value: Company.ISC,
            ExistingOrganizations.K2.value: Company.K2,
            ExistingOrganizations.MarkelDemo.value: Company.MARKEL_DEMO,
            ExistingOrganizations.CNA.value: Company.CNA,
            ExistingOrganizations.SECURA.value: Company.SECURA,
            ExistingOrganizations.Vivere.value: Company.VIVERE,
            ExistingOrganizations.AIG.value: Company.AIG,
            ExistingOrganizations.AdmiralInsuranceGroupTest.value: Company.ADMIRAL_INSURANCE_GROUP_TEST,
            ExistingOrganizations.BowheadSpecialtyTest.value: Company.BOWHEAD_SPECIALTY_TEST,
        }
        return org_to_company.get(organization_id)

    @staticmethod
    def find_organization_owner(organization_id: int):
        from copilot.models import User

        organization_name = [org.name for org in ExistingOrganizations if org.value == organization_id]
        if not organization_name:
            return None
        organization_name = organization_name[0]
        organization_owner_email = [owner.value for owner in Organization.Owners if owner.name == organization_name]
        if not organization_owner_email:
            return None
        organization_owner_email = organization_owner_email[0]
        organization_owner = User.query.filter(User.email == organization_owner_email).first()
        return organization_owner

    @staticmethod
    def find_organization_owner_id(organization_id: int) -> int | None:
        owner = Organization.find_organization_owner(organization_id)
        return owner.id if owner else None

    @staticmethod
    def kalepa_organizations() -> set[int]:
        return {
            ExistingOrganizations.KalepaTest.value,
            ExistingOrganizations.KalepaMGA.value,
            ExistingOrganizations.KalepaDemo.value,
            ExistingOrganizations.KalepaNewDemo.value,
            ExistingOrganizations.QualityAudit.value,
        }

    @staticmethod
    def active_client_organizations() -> set[int]:
        return {
            ExistingOrganizations.Nationwide.value,
            ExistingOrganizations.Arch.value,
            ExistingOrganizations.MunichRe.value,
            ExistingOrganizations.Paragon.value,
            ExistingOrganizations.NecSpecialty.value,
            ExistingOrganizations.NorthStarMutual.value,
            ExistingOrganizations.GoldenBear.value,
            ExistingOrganizations.NSM.value,
            ExistingOrganizations.OmahaNational.value,
            ExistingOrganizations.WCF.value,
            ExistingOrganizations.ZurichNA.value,
            ExistingOrganizations.BoltonStreet.value,
            ExistingOrganizations.CrcGroup.value,
            ExistingOrganizations.AdmiralInsuranceGroup.value,
            ExistingOrganizations.MSIGNA.value,
            ExistingOrganizations.FCCIGroup.value,
            ExistingOrganizations.BowheadSpecialty.value,
            ExistingOrganizations.Travelers.value,
            ExistingOrganizations.MerchantsGroup.value,
            ExistingOrganizations.NationwideML.value,
            ExistingOrganizations.BishopConifer.value,
            ExistingOrganizations.ARU.value,
            ExistingOrganizations.ISC.value,
            ExistingOrganizations.K2.value,
            ExistingOrganizations.CNA.value,
            ExistingOrganizations.SECURA.value,
            ExistingOrganizations.Vivere.value,
            ExistingOrganizations.AIG.value,
            ExistingOrganizations.AdmiralInsuranceGroupTest.value,
            ExistingOrganizations.BowheadSpecialtyTest.value,
        }

    @staticmethod
    def priorities() -> dict[int, OrganizationPriorityConfig]:
        # If new organization is added, remember to update DB query in support_users.get_submission_queue_query
        return {
            ExistingOrganizations.Arch.value: OrganizationPriorityConfig(70, 10, 60),
            ExistingOrganizations.AdmiralInsuranceGroup.value: OrganizationPriorityConfig(15),
            ExistingOrganizations.SECURA.value: OrganizationPriorityConfig(16),
            ExistingOrganizations.ISC.value: OrganizationPriorityConfig(17),
            ExistingOrganizations.NationwideML.value: OrganizationPriorityConfig(18),
            ExistingOrganizations.BowheadSpecialty.value: OrganizationPriorityConfig(20),
            ExistingOrganizations.MerchantsGroup.value: OrganizationPriorityConfig(25),
            ExistingOrganizations.ARU.value: OrganizationPriorityConfig(28),
            ExistingOrganizations.CrcGroup.value: OrganizationPriorityConfig(30),
            ExistingOrganizations.MunichRe.value: OrganizationPriorityConfig(50, 40, 120),
            ExistingOrganizations.BoltonStreet.value: OrganizationPriorityConfig(60),
            ExistingOrganizations.OmahaNational.value: OrganizationPriorityConfig(80),
            ExistingOrganizations.NorthStarMutual.value: OrganizationPriorityConfig(90),
            ExistingOrganizations.Paragon.value: OrganizationPriorityConfig(120, 60, 60),
            ExistingOrganizations.BishopConifer.value: OrganizationPriorityConfig(127),
            ExistingOrganizations.Nationwide.value: OrganizationPriorityConfig(130, 110, 120),
            ExistingOrganizations.K2.value: OrganizationPriorityConfig(999),
            ExistingOrganizations.Vivere.value: OrganizationPriorityConfig(999),
            ExistingOrganizations.NecSpecialty.value: OrganizationPriorityConfig(1000),
            ExistingOrganizations.GoldenBear.value: OrganizationPriorityConfig(1000),
            ExistingOrganizations.ZurichNA.value: OrganizationPriorityConfig(1000),
            ExistingOrganizations.MSIGNA.value: OrganizationPriorityConfig(1000),
            ExistingOrganizations.WCF.value: OrganizationPriorityConfig(1000),
            ExistingOrganizations.NSM.value: OrganizationPriorityConfig(1000),
            ExistingOrganizations.Travelers.value: OrganizationPriorityConfig(1000),
            ExistingOrganizations.FCCIGroup.value: OrganizationPriorityConfig(10000),
            ExistingOrganizations.CNA.value: OrganizationPriorityConfig(10000),
            ExistingOrganizations.AIG.value: OrganizationPriorityConfig(10000),
            ExistingOrganizations.AdmiralInsuranceGroupTest.value: OrganizationPriorityConfig(11000),
            ExistingOrganizations.BowheadSpecialtyTest.value: OrganizationPriorityConfig(11000),
        }

    @staticmethod
    def get_priority(organization_id: int) -> OrganizationPriorityConfig:
        return Organization.priorities().get(organization_id, OrganizationPriorityConfig(1000))

    @staticmethod
    def file_types_processed_independently_from_auto_processing() -> dict[int, set[FileType]]:
        return {}

    @staticmethod
    def file_classifications_processed_independently_from_auto_processing() -> (
        dict[int, set[ClassificationDocumentType]]
    ):
        return {}

    @staticmethod
    def is_file_processed_independently_from_auto_processing(
        organization_id: int, file_type: FileType, classification: str
    ) -> bool:
        file_types = FileType.always_processed_types().union(
            Organization.file_types_processed_independently_from_auto_processing().get(organization_id, set())
        )
        classifications = Organization.file_classifications_processed_independently_from_auto_processing().get(
            organization_id, set()
        )
        return file_type in file_types or classification in classifications

    @staticmethod
    def get_processing_sla(organization_id: int, routing_tags: list[str]) -> int | None:
        if organization_id not in Organization.active_client_organizations():
            return None
        if (
            Organization.is_arch_for_id(organization_id)
            or Organization.is_bowhead_or_bowhead_test_for_id(organization_id)
            or Organization.is_admiral_or_admiral_test_for_id(organization_id)
            or (
                Organization.is_paragon_for_id(organization_id)
                and not any(rt in ["paragon_pspe3", "paragon_auto", "paragon_workerscomp"] for rt in routing_tags)
            )
        ):
            return 120
        else:
            return 240
