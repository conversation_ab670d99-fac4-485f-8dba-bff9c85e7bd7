"""
The import order is important here as
`meta` needs to be imported at the end so it can *know* about all models.
As `meta.create_all()` only takes into account models that are imported.
"""

from ._private import BaseModel, db, meta  # noqa: F401
from .settings import Settings
from .organization import Organization
from .brokerages_v2 import Brokerage, BrokerageEmployee
from .feedback import Feedback, TenantFeedback, StuckSubmissionFeedback
from .files import File
from .forms import AuditTrail
from .permissions import ReportPermission
from .auto_qa_checks import AutoQACheck

# Import models from tasks.py before task_dataset.py
from .tasks import (
    TaskDefinition,
    Task,
    TaskModel,
    TaskDefinitionModel,
    TaskExecution,
    TaskCostLimit,
    TaskDefinitionAudit,
)
from .task_dataset import (
    TaskDataset,
    TaskDatasetInput,
    TaskDatasetGroundTruth,
    TaskDatasetModelOutcome,
    TaskDatasetExecution,
)
from .workers_comp_experience import WorkersCompExperience, WorkersCompStateRatingInfo
from .support_user import SupportUser, SupportUserReport
from .reports import (
    Report<PERSON>lert,
    ReportV2,
    Submission,
    SubmissionBusiness,
    SubmissionStage,
    Subscription,
    NAICSCode,
    SubmissionClearingIssue,
    ReportProcessingDependency,
    SubmissionPriority,
)
from .metrics import (
    MetricV2,
    MetricSource,
)
from .user import User
from .user_action import UserAction
from .metric_groups import MetricGroup
from .metric_preferences import MetricPreference
from .metric_template import MetricTemplate, MetricPreferencesTemplates
from .notebook import NotebookThread, NotebookMessage
from .execution_events import ExecutionEvent
from .client_applications import ClientApplication
from .customization import CustomizableClassifier
from .hub_template import HubTemplate
from .policy import Policy, Loss, LossPolicy
from .submission_audit import SubmissionAudit
from .submission_history import SubmissionHistory, SubmissionActionType, SubmissionParentType
from .ask_questions import AskQuestions
from .verification_check import VerificationCheckResult
from .customizable_classifiers import (
    CustomizableClassifierV2,
    ClassifierConfig,
    ClassifierVersion,
    ClassifierPhrase,
    ClassifierToConfigVersion,
    ClassifierConfigVersion,
    LLMConfigVersion,
    PhrasesConfigVersion,
    PhrasesWithLLMConfigVersion,
    ClassifierVersionTaskDataset,
)
from .aig_uw_mapping import AIGUwMapping

# Explicit exports (for mypy)
__all__ = [
    "db",
    "BrokerageEmployee",
    "Organization",
    "Submission",
    "User",
    "File",
    "ReportPermission",
    "ReportProcessingDependency",
    "ReportV2",
    "SubmissionBusiness",
    "SubmissionClearingIssue",
    "Subscription",
    "TaskDataset",
    "ClassifierVersionTaskDataset",
    "TaskModel",
    "TaskDefinition",
    "Task",
    "TaskDefinitionModel",
    "TaskExecution",
    "TaskCostLimit",
    "TaskDefinitionAudit",
]
