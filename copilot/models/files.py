from __future__ import annotations

from dataclasses import dataclass, field
from datetime import datetime
from difflib import Se<PERSON><PERSON><PERSON><PERSON>
from typing import TYPE_CHECKING, Any
from uuid import uuid4
import re
import uuid

from common.clients.boss_api_client import BOSS_DOC_ID_KEY
from common.utils.address import get_state_from_ers_address
from infrastructure_common.logging import get_logger
from sqlalchemy import (
    ARRAY,
    Boolean,
    Column,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Integer,
    String,
    event,
    sql,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import backref, exc, relationship  # type: ignore
from sqlalchemy.orm.exc import StaleDataError
from sqlalchemy.sql.schema import Index, UniqueConstraint
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityCategory, EntityFieldID
from static_common.enums.external import ExternalIdentifierType
from static_common.enums.file_enhancement_type import FileEnhancementType
from static_common.enums.file_metric import FileMetricName
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.sensible import SensibleStatus, SensibleUploadStatus
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.business_resolution_data import (
    BusinessResolutionData,
    ExternalIdentifier,
)
from static_common.models.file_additional_info import FileAdditionalInfo
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
    SubmissionEntity,
)
from static_common.schemas.business_resolution_data import BusinessResolutionDataSchema
from static_common.schemas.file_additional_info import FileAdditionalInfoSchema
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
import flask
import psycopg2

from copilot.constants import NAME_MATCH_THRESHOLD
from copilot.database_events.after_commit_events import (
    AfterCommitEventType,
    add_after_commit_event,
)
from copilot.models import Organization
from copilot.models._private import BaseModel, db
from copilot.models.pds_metrics import PDSStats
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.types import UserShadowFileState
from copilot.utils import first_party_s3_key

BOSS_UPLOAD_BUCKET = "post/uploads/files"
file_additional_info_schema = FileAdditionalInfoSchema()
logger = get_logger()

if TYPE_CHECKING:
    from copilot.clients.ers_v3 import ERSClientV3


class File(BaseModel):
    __tablename__ = "files"

    name = Column(String)
    s3_key = Column(String)
    file_type = Column(Enum(FileType), nullable=False)
    classification = Column(String)
    submission_business_id = Column(
        UUID(as_uuid=True), ForeignKey("submission_businesses.id", ondelete="CASCADE"), nullable=True, index=True
    )
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=True, index=True
    )
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True)
    organization_id = Column(Integer, ForeignKey("organization.id", ondelete="CASCADE"), nullable=True)
    processing_state = Column(Enum(FileProcessingState), nullable=True, index=True)
    sensible_status = Column(String, nullable=True)
    origin = Column(Enum(Origin), nullable=False)
    comment = Column(String)
    additional_info = Column(JSONB, nullable=True)
    user_file_type = Column(Enum(FileType))
    parent_file_id = Column(UUID(as_uuid=True), ForeignKey("files.id", ondelete="CASCADE"), index=False, nullable=True)
    child_file_ids = relationship(
        "File",
        uselist=True,
        cascade="all, delete-orphan",
        backref=backref("parent_file", remote_side="File.id"),
        foreign_keys="[File.parent_file_id]",
    )
    processed_file = relationship("ProcessedFile", uselist=False, cascade="all, delete-orphan", back_populates="file")
    size = Column(Integer)
    initial_classification = Column(String)
    initial_classification_confidence = Column(Float)
    issues = Column(ARRAY(String), nullable=True)
    checksum = Column(String, nullable=True, index=True)
    metrics = relationship("FileMetric", uselist=True, cascade="all, delete-orphan", back_populates="file")
    is_internal = Column(Boolean, nullable=False, default=False)
    initial_processing_state = Column(Enum(FileProcessingState), nullable=True)
    is_required_shadow_processing = Column(Boolean, nullable=True, default=False)
    replaced_by_file_ids = Column(ARRAY(UUID(as_uuid=True)), nullable=True)
    internal_notes = Column(ARRAY(String), nullable=True)
    submission_level_extracted_data = relationship(
        SubmissionLevelExtractedData, uselist=True, cascade="all, delete-orphan", overlaps="file"
    )
    execution_arn = Column(String, nullable=True)
    retry_count = Column(Integer, nullable=True)
    processing_state_updated_at = Column(DateTime(timezone=True), nullable=True)
    is_handsigned = Column(Boolean, nullable=True, server_default=sql.false())
    client_file_type = Column(String, nullable=True)
    client_file_tags = Column(JSONB, nullable=True)
    external_identifier = Column(UUID(as_uuid=True), nullable=True, index=True)
    hidden = Column(Boolean, nullable=True, default=False)
    additional_file_names = Column(ARRAY(String), nullable=True)
    is_locked = Column(Boolean, nullable=False, default=False, server_default=sql.false())
    is_deleted = Column(Boolean, nullable=False, default=False, server_default=sql.false())
    cache_origin_file_id = Column(UUID(as_uuid=True), ForeignKey("files.id"), nullable=True, index=True)
    user_shadow_state = Column(Enum(UserShadowFileState), nullable=True)

    pds_stats = relationship(
        PDSStats, back_populates="file", foreign_keys="PDSStats.file_id", uselist=False, cascade="all, delete-orphan"
    )
    custom_file_type_id = Column(UUID(as_uuid=True), ForeignKey("custom_file_type.id"), nullable=True)
    custom_classification = Column(String, nullable=True)
    content_type = Column(String, nullable=True)

    custom_file_type = relationship(
        "CustomFileType",
        uselist=False,
    )

    support_user_file = relationship(
        "SupportUserFile",
        uselist=False,
        back_populates="file",
        foreign_keys="SupportUserFile.file_id",
        lazy="noload",
    )

    enhanced_files = relationship(
        "EnhancedFile",
        uselist=True,
        back_populates="file",
        foreign_keys="EnhancedFile.file_id",
        cascade="all, delete-orphan",
    )

    auto_qa_checks = relationship(
        "AutoQACheck",
        uselist=True,
        back_populates="file",
        foreign_keys="AutoQACheck.file_id",
        cascade="all, delete-orphan",
    )

    def generate_replaced_by_file_ids_copy(self, old_to_new_ids: dict, new_submission_id: str) -> list[str]:
        updated_by_file_ids = []
        for file_id in self.replaced_by_file_ids or []:
            # As the replacing file can't be child file i.e. we can't upload directly a child file,
            # the replace_file id should be present in the mapping.
            if new_file_id := old_to_new_ids.get(file_id):
                updated_by_file_ids.append(new_file_id)
            else:
                logger.error(
                    "Replaced file id not in the map so it will be skipped",
                    replaced_file_id=str(file_id),
                    submission_id=str(self.submission_id),
                    new_submission_id=new_submission_id,
                )
        return updated_by_file_ids

    @property
    def presigned_url(self) -> str:
        if self.classification in ClassificationDocumentType.no_preview_document_types() and self.parent_file_id:
            parent_file = db.session.query(File).get(self.parent_file_id)
            return flask.current_app.submission_s3_client.generate_presigned_url(file=parent_file)
        return flask.current_app.submission_s3_client.generate_presigned_url(self)

    @property
    def additional_info_obj(self) -> FileAdditionalInfo | None:
        try:
            return (
                file_additional_info_schema.load(self.additional_info)
                if self.additional_info and self.file_type in [FileType.ACORD_FORM, FileType.POLICY]
                else None
            )
        except (exc.ObjectDeletedError, psycopg2.errors.ForeignKeyViolation, StaleDataError):
            db.session.rollback()
        except Exception as e:
            db.session.rollback()
            logger.warning("Exception loading additional info", e=str(e))
            return None

    @property
    def is_acord_consolidated(self) -> bool:
        return (
            self.classification in ClassificationDocumentType.skip_onboarding_classifications()
            and self.processing_state in FileProcessingState.already_consolidated_states()
        )

    @property
    def is_rose_file(self) -> bool:
        return (
            Organization.is_nationwide_for_id(self.organization_id)
            and self.client_file_tags
            and self.client_file_tags.get("source_type", "").lower() == "rose"
        )

    @property
    def boss_doc_id(self) -> str | None:
        return self.client_file_tags.get(BOSS_DOC_ID_KEY) if self.client_file_tags else None

    @property
    def display_name(self) -> str | None:
        if not self.client_file_tags:
            return self.name

        from copilot.logic.organization_settings import get_display_name_tag_key

        tag_name = get_display_name_tag_key(self.organization_id)

        if not tag_name:
            return self.name
        return self.client_file_tags.get(tag_name, self.name)

    @property
    def is_document_ingestion(self) -> bool:
        return self.organization_id == ExistingOrganizations.MarkelDemo.value

    def copy(
        self,
        old_to_new_ids: dict,
        submission_s3_client,
        should_copy_processed_data: bool = True,
        for_shadow: bool = False,
        running_on_main_thread: bool = True,
    ) -> File:
        new = File()  # type: ignore
        if not (new_id := old_to_new_ids.get(self.id)):
            logger.info(
                "File id not in the map, generating random", file_id=str(self.id), submission_id=str(self.submission_id)
            )
            new_id = uuid4()
            old_to_new_ids[self.id] = new_id
        new.id = new_id
        new.name = self.name
        new.s3_key = self.s3_key
        new.file_type = self.file_type
        new.classification = self.classification
        new.submission_business_id = old_to_new_ids.get(self.submission_business_id, None)
        new.submission_id = old_to_new_ids.get(self.submission_id, self.submission_id)
        new.user_id = old_to_new_ids.get(self.user_id, self.user_id)
        new.organization_id = old_to_new_ids.get(self.organization_id, self.organization_id)
        new.processing_state = self.processing_state
        if new.processing_state not in FileProcessingState.final_states():
            should_copy_processed_data = (
                should_copy_processed_data
                and new.processing_state in FileProcessingState.final_states_with_processed_data()
            )
            new.processing_state = (
                FileProcessingState.CLASSIFIED if new.classification else FileProcessingState.NOT_CLASSIFIED
            )
        new.sensible_status = self.sensible_status
        new.origin = self.origin
        new.comment = self.comment
        new.user_file_type = self.user_file_type
        new.parent_file_id = old_to_new_ids.get(self.parent_file_id)
        new.additional_info = self.additional_info
        new.size = self.size
        new.initial_classification = self.initial_classification
        new.initial_classification_confidence = self.initial_classification_confidence
        new.issues = self.issues
        new.is_internal = self.is_internal
        new.is_required_shadow_processing = self.is_required_shadow_processing
        new.initial_processing_state = self.initial_processing_state
        new.is_handsigned = self.is_handsigned
        new.client_file_type = self.client_file_type
        new.client_file_tags = self.client_file_tags
        new.hidden = self.hidden
        new.additional_file_names = self.additional_file_names
        new.is_locked = self.is_locked
        new.is_deleted = self.is_deleted
        new.content_type = self.content_type
        if for_shadow:
            new.external_identifier = self.external_identifier
            new.cache_origin_file_id = self.cache_origin_file_id
        new.replaced_by_file_ids = self.generate_replaced_by_file_ids_copy(old_to_new_ids, str(new.submission_id))
        new.internal_notes = [f"Copied from file id: {self.id}"] + (self.internal_notes or [])

        for key in old_to_new_ids:
            if str(key) in new.s3_key:  # type: ignore
                new.s3_key = new.s3_key.replace(str(key), str(old_to_new_ids[key]))
                if new.s3_key.startswith(BOSS_UPLOAD_BUCKET):
                    # We do not want to upload these files to the BOSS bucket as they will trigger lambdas.
                    new.s3_key = new.s3_key.replace(BOSS_UPLOAD_BUCKET, "uploads/files", 1)
        if new.s3_key != self.s3_key:
            copy_status = submission_s3_client.copy_file(self.s3_key, new.s3_key)
            # if the copying failed use the old key
            new.s3_key = self.s3_key if copy_status is False else new.s3_key
        new.checksum = self.checksum

        if self.file_type == FileType.CUSTOM:
            if new.organization_id == self.organization_id:
                new.custom_file_type_id = self.custom_file_type_id
                new.custom_classification = self.custom_classification
            else:
                new.custom_file_type_id = None
                new.custom_classification = None
                new.processing_state = FileProcessingState.NOT_CLASSIFIED
                new.file_type = FileType.UNKNOWN
                should_copy_processed_data = False

        if should_copy_processed_data:
            self.copy_processed_data(
                new, old_to_new_ids, submission_s3_client, running_on_main_thread=running_on_main_thread
            )

        for enhanced_file in self.enhanced_files:
            new.enhanced_files.append(enhanced_file.copy(old_to_new_ids, submission_s3_client))

        return new

    def copy_processed_data(
        self, new: File, old_to_new_ids: dict, submission_s3_client, running_on_main_thread: bool = True
    ) -> None:
        # check if there is first party data for this file
        original_first_party_key = first_party_s3_key(self.submission_id, self.name)
        if submission_s3_client.check_if_file_exists(original_first_party_key):
            submission_s3_client.copy_file(original_first_party_key, first_party_s3_key(new.submission_id, new.name))

        new.metrics = []
        for m in self.metrics:
            new.metrics.append(m.copy(old_to_new_ids))

        if self.processed_file:
            if new.processed_file:
                self.processed_file.copy_into(
                    old_to_new_ids, new.processed_file, running_on_main_thread=running_on_main_thread
                )
            else:
                new.processed_file = self.processed_file.copy(
                    old_to_new_ids, running_on_main_thread=running_on_main_thread
                )

        if self.submission_level_extracted_data:
            db.session.query(SubmissionLevelExtractedData).filter_by(file_id=new.id).delete()
            for sled in self.submission_level_extracted_data:
                new.submission_level_extracted_data.append(sled.copy(old_to_new_ids))

    def to_external_file(self) -> ExternalFile:
        file_id = self.external_identifier or self.id
        tags = []
        if self.client_file_tags:
            for key, value in self.client_file_tags.items():
                if isinstance(key, str) and isinstance(value, str):
                    tags.append({"key": key, "value": value})
        return ExternalFile(
            id=file_id,
            name=self.name,
            client_file_type=self.client_file_type,
            tags=tags,
        )


@event.listens_for(File, "after_insert")
def handle_insert(mapper, connection, target) -> None:
    add_after_commit_event(AfterCommitEventType.FILE_FILE_TYPE_CHANGED, (target.organization_id, target.id))


@event.listens_for(File, "before_update")
def handle_update(mapper, connection, target) -> None:
    file_type = db.inspect(target).attrs["file_type"].history
    is_deleted = db.inspect(target).attrs["is_deleted"].history
    if file_type.has_changes():
        add_after_commit_event(AfterCommitEventType.FILE_FILE_TYPE_CHANGED, (target.organization_id, target.id))

    if (
        target.classification == ClassificationDocumentType.RAW_EMAIL.value
        and is_deleted.has_changes()
        and (target.additional_info or {}).get("email_id")
    ):
        is_hard_delete = False
        email_id = target.additional_info["email_id"]
        add_after_commit_event(
            AfterCommitEventType.RAW_EMAIL_FILE_IS_DELETED_CHANGED, (email_id, target.is_deleted, is_hard_delete)
        )


@event.listens_for(File, "after_insert")
@event.listens_for(File, "after_update")
def handle_locked_file_upsert(mapper, connection, target: File) -> None:
    if not target.is_locked:
        return

    add_after_commit_event(AfterCommitEventType.LOCKED_FILE_UPSERT, (target.id,))


@event.listens_for(File, "before_delete")
def handle_file_is_being_deleted(mapper, connection, target: File) -> None:
    if target.classification == ClassificationDocumentType.RAW_EMAIL.value and (target.additional_info or {}).get(
        "email_id"
    ):
        email_id = target.additional_info["email_id"]
        add_after_commit_event(AfterCommitEventType.RAW_EMAIL_FILE_IS_DELETED_CHANGED, (email_id, True, True))


class ProcessedFile(BaseModel):
    __tablename__ = "processed_files"

    file_id = Column(
        UUID(as_uuid=True), ForeignKey("files.id", ondelete="CASCADE"), nullable=False, index=True, unique=True
    )
    raw_processed_data = Column(JSONB, nullable=True)
    processed_data = Column(JSONB, nullable=True)
    entity_mapped_data = Column(JSONB, nullable=True)
    onboarded_data = Column(JSONB, nullable=True)
    business_resolution_data = Column(JSONB, nullable=True)

    file = relationship("File", uselist=False, back_populates="processed_file")

    def copy(self, old_to_new_ids: dict, running_on_main_thread: bool = True) -> ProcessedFile:
        new = ProcessedFile()
        new.id = uuid4()
        new.file_id = old_to_new_ids[self.file_id]
        self._optimised_jsons_set(new, str(new.file_id), running_on_main_thread=running_on_main_thread)
        return new

    def copy_into(self, old_to_new_ids: dict, target_pf: ProcessedFile, running_on_main_thread: bool = True) -> None:
        old_to_new_ids[self.id] = target_pf.id
        self._optimised_jsons_set(target_pf, str(target_pf.file_id), running_on_main_thread=running_on_main_thread)

    def _optimised_jsons_set(
        self, new_target_processed_file: ProcessedFile, target_file_id: str, running_on_main_thread: bool
    ) -> None:
        columns_to_set_replace = [
            "raw_processed_data",
            "processed_data",
            "entity_mapped_data",
            "onboarded_data",
            "business_resolution_data",
        ]

        this_file_id = str(self.file_id)
        this_processed_file_id = str(self.id)

        try:
            for column in columns_to_set_replace:
                in_db_select_and_replace_query = f"""
                                          select replace({column}::text, '{this_file_id}', '{target_file_id}')::jsonb
                                          from processed_files where id = '{this_processed_file_id}'
                                """
                replaced_json = db.session.execute(in_db_select_and_replace_query)
                setattr(new_target_processed_file, column, replaced_json.scalar())
        finally:
            if not running_on_main_thread:
                # If we are running this file.copy in a separate thread, db.session above will open new connection
                # which we need to close to avoid connection leak. For runs on main thread, this is handled by
                # flask-sqlalchemy
                db.session.rollback()
                db.session.remove()

    @property
    def resolution_requested_names(self) -> list[str]:
        if not self.business_resolution_data or not self.business_resolution_data.get("resolution_data"):
            return []
        resolution_data = self.business_resolution_data["resolution_data"]
        names = [brd.get("requested_name") for brd in resolution_data if brd.get("requested_name")]
        return names

    @property
    def num_confirmed_businesses(self) -> int:
        if not self.business_resolution_data or not self.business_resolution_data.get(  # type: ignore[union-attr]
            "resolution_data"
        ):
            return -1
        resolution_data = self.business_resolution_data["resolution_data"]  # type: ignore[call-overload]
        num_confirmed = sum([1 for brd in resolution_data if brd.get("entity_id") is not None])
        return num_confirmed

    @property
    def all_entities_resolved(self) -> bool:
        if not self.business_resolution_data or not self.business_resolution_data.get(  # type: ignore[union-attr]
            "resolution_data"
        ):
            return True
        resolution_data = self.business_resolution_data["resolution_data"]  # type: ignore[call-overload]
        return all(brd.get("entity_id") is not None for brd in resolution_data)

    @property
    def entity_ids(self) -> list[str]:
        if not self.business_resolution_data or not self.business_resolution_data.get("resolution_data"):
            return []
        resolution_data = self.business_resolution_data["resolution_data"]
        return [brd.get("entity_id") for brd in resolution_data if brd.get("entity_id")]

    @property
    def visible_entity_ids(self) -> list[str]:
        if self.file.classification == ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF:
            if not self.business_resolution_data:
                return []
            return [
                brd.get("entity_id")
                for brd in self.business_resolution_data["resolution_data"]
                if brd["category"] not in {EntityCategory.SHELL, EntityCategory.LEGAL}
            ]
        if (
            not self.file.classification
            or self.file.classification not in ClassificationDocumentType.files_with_named_insureds_classifications()
        ):
            return self.entity_ids

        if (
            not self.processed_data
            or not self.business_resolution_data
            or not self.business_resolution_data.get("resolution_data")
        ):
            return []

        resolution_data = self.business_resolution_data["resolution_data"]

        entity_idxs = set()

        for i, x in enumerate(self.processed_data.get("entities", [])):
            if (
                x.get("acord_location_information") is None
                or x["acord_location_information"].get("location_number") is None
            ):
                continue

            if x.get("type") != "Business":
                continue

            entity_idxs.add(i)

        return [
            brd.get("entity_id")
            for brd in resolution_data
            if brd.get("entity_id") and brd.get("entity_idx") in entity_idxs
        ]


class FileMetric(BaseModel):
    __tablename__ = "file_metrics"

    file_id = Column(UUID(as_uuid=True), ForeignKey("files.id", ondelete="CASCADE"), nullable=False, index=True)
    metric_name = Column(Enum(FileMetricName), nullable=False)
    metric_value = Column(Float, nullable=True)
    file = relationship("File", uselist=False, back_populates="metrics")
    score_calculation = Column(JSONB, nullable=True)

    def copy(self, old_to_new_ids: dict) -> FileMetric:
        new = FileMetric()
        new.id = uuid4()
        old_to_new_ids[self.id] = new.id
        new.file_id = old_to_new_ids[self.file_id]
        new.metric_name = self.metric_name
        new.metric_value = self.metric_value
        new.score_calculation = self.score_calculation

        return new


@event.listens_for(File, "before_delete")
def cleanup_submission_files_data_on_file_delete(mapper, connection, target) -> None:
    add_after_commit_event(
        AfterCommitEventType.CLEAN_SUBMISSION_DATA_ON_FILE_DELETE, (target.submission_id, target.classification)
    )


@event.listens_for(File, "before_update")
def handle_file_processing_state_update(mapper, connection, target) -> None:
    processing_state_history = db.inspect(target).attrs["processing_state"].history
    if processing_state_history.has_changes():
        target.processing_state_updated_at = datetime.utcnow()
        target.retry_count = 0


@event.listens_for(ProcessedFile, "before_delete")
def cleanup_submission_files_data_on_delete(mapper, connection, target) -> None:
    clean_em_data = False
    clean_onboarded_data = False
    if target.entity_mapped_data:
        clean_em_data = True
    if target.onboarded_data:
        clean_onboarded_data = True
    add_after_commit_event(
        AfterCommitEventType.CLEAN_SUBMISSION_DATA_ON_PROCESSED_FILE_DELETE,
        (target.file_id, clean_em_data, clean_onboarded_data),
    )


@event.listens_for(ProcessedFile, "after_insert")
def cleanup_submission_files_data_on_insert(mapper, connection, target) -> None:
    add_after_commit_event(AfterCommitEventType.CLEAN_SUBMISSION_DATA_ON_PROCESSED_FILE_INSERT, (target.file_id,))


class SubmissionFilesData(BaseModel):
    __tablename__ = "submission_files_data"

    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    entity_mapped_data = Column(JSONB, nullable=True)
    onboarded_data = Column(JSONB, nullable=True)
    loaded_data = Column(JSONB, nullable=True)
    raw_em_data = Column(JSONB, nullable=True)
    raw_do_data = Column(JSONB, nullable=True)


@dataclass
class FilePatch:
    processing_state: FileProcessingState | None = None
    file_type: FileType | None = None
    classification: ClassificationDocumentType | None = None
    user_file_type: FileType | None = None
    comment: String | None = None
    additional_info: FileAdditionalInfo | None = None
    initial_classification: ClassificationDocumentType | None = None
    initial_classification_confidence: Float | None = None
    issue: str | None = None
    issues: list[str] | None = None
    is_internal: bool | None = None
    internal_note: str | None = None
    internal_notes: list[str] | None = None
    client_file_tags: dict[str, Any] | None = None
    client_file_type: str | None = None
    external_identifier: UUID | None = None
    hidden: bool | None = None
    is_locked: bool = False
    custom_file_type_id: UUID | None = None
    custom_classification: str | None = None
    content_type: str | None = None


@dataclass
class ExternalFile:
    id: UUID | None = None
    name: str | None = None
    client_file_type: str | None = None
    tags: list[dict[str, str]] = field(default_factory=list)


@dataclass
class OnboardedFileEntityInformation:
    entity_id: UUID | str
    entity_type: SubmissionEntityType
    entity: SubmissionEntity
    parent_entity_id: UUID | str | None = None
    parent_file_id: UUID | None = None
    name: str | None = None
    address: str | None = None
    location_number: int | None = None
    building_number: int | None = None
    name_field: ResolvedDataField | None = None
    address_field: ResolvedDataField | None = None
    resolved_id: UUID | None = None
    resolved_address: str | None = None
    file_id: UUID | None = None
    identifiers: list[ExternalIdentifier] = field(default_factory=list)
    classification: ClassificationDocumentType | None = None

    @property
    def normalized_address(self) -> str | None:
        return re.sub(r"[\W_]", "", self.address).lower() if self.address else None

    @property
    def normalized_name(self) -> str | None:
        return self.name.strip(" ").strip("'").strip('"') if self.name else None

    @property
    def entity_information(self) -> list[ResolvedDataField]:
        result = []
        if self.name_field:
            result.append(self.name_field)

        if self.address_field:
            result.append(self.address_field)
        return result

    @property
    def resolved_state(self) -> str | None:
        return get_state_from_ers_address(self.resolved_address)

    @property
    def is_fni(self) -> bool:
        return self.entity.entity_named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED


class AcordEntityManager:
    def __init__(self, files: list[File] | None = None, use_br_data: bool = False, ers_client: ERSClientV3 = None):
        self._id_to_entity_info: dict[UUID | str, OnboardedFileEntityInformation] = {}
        self._loc_to_business: dict[int, dict[UUID | None, UUID | str]] = {}
        self._loc_building_to_ei: dict[int | None, dict[int | None, list[OnboardedFileEntityInformation]]] = {}
        self._use_br_data = use_br_data
        self._onboarded_file_schema = LeanOnboardedFileSchema()
        self._br_data_schema = BusinessResolutionDataSchema(many=True)
        self.state_to_business_ids: dict[str, set[uuid.UUID]] = {}
        self._fnis: list[OnboardedFileEntityInformation] = []
        self.fni: OnboardedFileEntityInformation | None = None
        self._submission_id = files[0].submission_id if files else None
        self._log = logger.bind(submission_id=self._submission_id, source="AcordEntityManager")
        self._ers_client = ers_client
        if files:
            files.sort(key=lambda x: ClassificationDocumentType.get_consolidation_priority(x.classification))
        for file in files or []:
            entities = self.extract_entities_from_file(file)
            self.add_all_entities(entities)
        self.consolidate_fni()

    def get_named_insureds(self) -> list[OnboardedFileEntityInformation]:
        return [ei for ei in self._id_to_entity_info.values() if ei.is_fni or ei.entity.is_oni]

    def get_first_named_insured(
        self, parent_file_id: UUID | None = None, requested_name: str | None = None, return_first: bool = False
    ) -> OnboardedFileEntityInformation | None:
        candidates = [ei for ei in self._id_to_entity_info.values() if ei.is_fni]

        # find the most similar by name
        if requested_name:
            candidates = [
                ei
                for ei in candidates
                if ei.name and SequenceMatcher(None, ei.name.lower(), requested_name.lower()).ratio() > 0.8
            ]

        if len(candidates) == 0:
            return None

        if len(candidates) == 1 or (candidates and return_first):
            return candidates[0]

        return next((c for c in candidates if c.parent_file_id and c.parent_file_id == parent_file_id), None)

    def find_best_fni_match(self, parent_file_id: UUID | None, requested_name: str | None):
        found = (
            self.get_first_named_insured(parent_file_id, requested_name)
            or self.get_first_named_insured(requested_name=requested_name)
            or self.get_first_named_insured(parent_file_id=parent_file_id)
            or self.get_first_named_insured(requested_name=requested_name, return_first=True)
            or self.get_first_named_insured(return_first=True)
        )
        return found

    @staticmethod
    def _copy_resolved_data_field(value: Any, field: ResolvedDataField) -> ResolvedDataField:
        return ResolvedDataField(
            name=field.name,
            values=[ResolvedDataValue(value=value)],
            value_type=field.value_type,
            display_as_fact=field.display_as_fact,
            fact_subtype_id=field.fact_subtype_id,
            naics_code=field.naics_code,
            unit=field.unit,
        )

    def extract_fni_fein(self) -> str | None:
        if self.fni and (
            fein_identifier := next(
                (identifier for identifier in self.fni.identifiers if identifier.type == ExternalIdentifierType.FEIN),
                None,
            )
        ):
            return fein_identifier.value
        return None

    def extract_entities_from_file(self, file: File) -> list[OnboardedFileEntityInformation]:
        result: list[OnboardedFileEntityInformation] = []
        if file.processing_state in FileProcessingState.final_states_without_processed_data():
            # There is no reason to try to extract entities from failed files
            return result
        of: OnboardedFile = self._onboarded_file_schema.load(file.processed_file.processed_data)
        name_entity_info = next((ei for ei in of.entity_information if ei.name == EntityFieldID.NAME.value), None)
        address_entity_info = next((ei for ei in of.entity_information if ei.name == EntityFieldID.ADDRESS.value), None)
        idx_to_name = {val.entity_idx: val.value for val in name_entity_info.values} if name_entity_info else {}
        idx_to_address = (
            {val.entity_idx: val.value for val in address_entity_info.values} if address_entity_info else {}
        )
        brd_list = []
        if self._use_br_data and file.processed_file.business_resolution_data is not None:
            brd_list = self._br_data_schema.load(file.processed_file.business_resolution_data)
        idx_to_brd_entity: dict[int, BusinessResolutionData] = {brd.entity_idx: brd for brd in brd_list}
        external_identifiers = of.get_external_identifiers()
        for idx, entity in enumerate(of.entities):
            parent_entity_id = of.entities[entity.parent_idx].id if entity.parent_idx is not None else None
            name = None
            name_field = None
            location_number = None
            building_number = None
            if entity.acord_location_information:
                location_number = entity.acord_location_information.location_number
                building_number = entity.acord_location_information.building_number
            if idx in idx_to_name:
                name = idx_to_name[idx]
                name_field = self._copy_resolved_data_field(name, name_entity_info)

            address = None
            address_field = None
            if idx in idx_to_address:
                address = idx_to_address[idx]
                address_field = self._copy_resolved_data_field(address, address_entity_info)

            brd_entity = idx_to_brd_entity.get(idx, BusinessResolutionData(idx))

            result.append(
                OnboardedFileEntityInformation(
                    entity.id,
                    entity.type,
                    entity,
                    parent_entity_id=parent_entity_id,
                    parent_file_id=file.parent_file_id,
                    name=name,
                    address=address,
                    location_number=location_number,
                    building_number=building_number,
                    name_field=name_field,
                    address_field=address_field,
                    resolved_id=brd_entity.entity_id,
                    resolved_address=brd_entity.resolved_address,
                    file_id=file.id,
                    identifiers=external_identifiers.get(idx, []),
                    classification=ClassificationDocumentType.try_parse_str(file.classification),
                )
            )
        return result

    def add_entity(self, entity_information: OnboardedFileEntityInformation) -> None:
        if entity_information.is_fni:
            self._fnis.append(entity_information)

        if (
            entity_information.entity_id in self._id_to_entity_info
            and not self._id_to_entity_info[entity_information.entity_id].resolved_id
            and entity_information.resolved_id
        ):
            self._id_to_entity_info[entity_information.entity_id] = entity_information

        if (
            entity_information.location_number is not None
            and entity_information.entity_type == SubmissionEntityType.BUSINESS
        ):
            # entity_type means business when working with acords
            file_id_to_business = self._loc_to_business.get(entity_information.location_number, {})
            file_id_to_business[entity_information.file_id] = entity_information.entity_id
            self._loc_to_business[entity_information.location_number] = file_id_to_business

        if entity_information.entity_id in self._id_to_entity_info:
            return
        self._id_to_entity_info[entity_information.entity_id] = entity_information

        if (
            entity_information.entity_type == SubmissionEntityType.BUSINESS
            and entity_information.resolved_id
            and (state := entity_information.resolved_state)
        ):
            business_ids = self.state_to_business_ids.get(state, set())
            business_ids.add(entity_information.resolved_id)
            self.state_to_business_ids[state] = business_ids

        if entity_information.entity_type == SubmissionEntityType.STRUCTURE:
            buildings_on_location = self._loc_building_to_ei.get(entity_information.location_number, {})
            building = buildings_on_location.get(entity_information.building_number, [])
            building.append(entity_information)
            buildings_on_location[entity_information.building_number] = building
            self._loc_building_to_ei[entity_information.location_number] = buildings_on_location

    def add_all_entities(self, entity_information_list: list[OnboardedFileEntityInformation]) -> None:
        for ei in entity_information_list:
            self.add_entity(ei)

    def consolidate_fni(self):
        if not self._fnis:
            self._log.warning("No FNI found in the file")
            return
        self.fni = self._fnis[0]
        other_fnis = self._fnis[1:]
        if not other_fnis:
            return
        names = {fni.normalized_name: fni for fni in other_fnis if fni.normalized_name}
        if not self._ers_client:
            self._log.error("ERS client not available, skipping name matching")
            return
        if not (
            name_score_result := self._ers_client.get_entity_names_score(
                name=self.fni.name, potential_names=list(names.keys()), enforce_flexible_in_name_value=True
            )
        ):
            self._log.warning("Failed to get ERS name scores")
            return
        if bellow_threshold := [names[r.name.value] for r in name_score_result if r.score <= NAME_MATCH_THRESHOLD]:
            self._log.warning("Below threshold names", names=[n.name for n in bellow_threshold], name=self.fni.name)
        above_threshold = [names[r.name.value] for r in name_score_result if r.score > NAME_MATCH_THRESHOLD]
        identifiers = {identifier.type: identifier.value for identifier in self.fni.identifiers}
        for entity in above_threshold:
            for identifier in entity.identifiers:
                if identifier.type not in identifiers:
                    identifiers[identifier.type] = identifier.value
                    continue
                if identifier.value != identifiers[identifier.type]:
                    self._log.warning(
                        "Conflicting identifiers, skipping",
                        file_id=entity.file_id,
                        entity_id=entity.entity_id,
                        identifier_type=identifier.type,
                        identifier_value=identifier.value,
                        existing_value=identifiers[identifier.type],
                    )
        self.fni.identifiers = [
            ExternalIdentifier(type=ExternalIdentifierType.try_parse_str(k), value=v) for k, v in identifiers.items()
        ]

    def find_structure(
        self, location_number: int | None, building_number: int | None, address: str | None
    ) -> OnboardedFileEntityInformation | None:
        # if only one structure return it
        structures = [ei for ei in self._id_to_entity_info.values() if ei.entity_type == SubmissionEntityType.STRUCTURE]
        if len(structures) == 1:
            return structures[0]

        if location_number not in self._loc_building_to_ei:
            return None

        buildings_map = self._loc_building_to_ei[location_number]

        # if there is a single building under location and no building number return it
        if building_number is None and len(buildings_map) == 1 and len(list(buildings_map.values())[0]) == 1:
            return list(buildings_map.values())[0][0]

        if building_number not in buildings_map:
            return None

        entities = buildings_map[building_number]
        if len(entities) == 1:
            return entities[0]

        entities.sort(key=lambda x: ClassificationDocumentType.get_consolidation_priority(x.classification))
        return entities[0]

    def find_entity_by_id(self, entity_id: UUID | str) -> OnboardedFileEntityInformation | None:
        return self._id_to_entity_info.get(entity_id)

    def find_entity_by_location(
        self, location_number: int | None, resolved_entities_only: bool = True, file_id: UUID | None = None
    ) -> OnboardedFileEntityInformation | None:
        if location_number is None:
            return None
        file_to_business = self._loc_to_business.get(location_number, {})
        entity_id = file_to_business.get(file_id)
        if not entity_id and len(file_to_business.keys()) > 0:
            entity_id = list(file_to_business.values())[0]
        found = self._id_to_entity_info.get(entity_id)
        if resolved_entities_only:
            return found if found and found.resolved_id else None
        return found


@dataclass
class FileAndSensibleProcessingPatch:
    was_processing_successful: bool
    send_event: bool
    file_id: UUID | None = None
    submission_id: UUID | None = None
    organization_id: int | None = None
    parsed_result_cache_key: str | None = None
    file_processing_state: FileProcessingState | None = None
    sensible_status: SensibleStatus | None = None
    update_sensible_extraction: bool | None = None
    sensible_upload_status: SensibleUploadStatus | None = None
    sensible_extraction_id: UUID | None = None
    sensible_call_made: bool | None = None
    error_message: str | None = None


@dataclass
class FilesClearing:
    file_ids_to_process: list[UUID] = field(default_factory=list)
    file_ids_to_ignore: list[UUID] = field(default_factory=list)
    file_ids_to_replace: list[UUID] = field(default_factory=list)


@dataclass
class LoadedProcessedFile:
    processed_data: OnboardedFile | None = None
    entity_mapped_data: OnboardedFile | None = None
    onboarded_data: OnboardedFile | None = None
    business_resolution_data: list[BusinessResolutionData] | None = None


class CustomFileType(BaseModel):
    __tablename__ = "custom_file_type"
    __table_args__ = (
        UniqueConstraint("organization_id", "file_type_name", name="uq_custom_file_type_name_org"),
        Index("ix_custom_file_type_name_organization", "file_type_name", "organization_id", unique=True),
    )

    file_type_name = Column(String, nullable=False)
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=False)
    is_enabled = Column(Boolean, nullable=False, default=True)


@dataclass
class SplitFilePageRange:
    start: int
    end: int
    file_type: FileType = FileType.UNKNOWN
    custom_file_type_id: UUID | None = None


@dataclass
class SplitFileRequest:
    page_ranges: list[SplitFilePageRange]


@dataclass
class FilesQueueEnvelope:
    files: list[File]
    submission_id_to_report_id: dict
    page: int | None = None
    total_pages: int | None = None
    total_files: int | None = None
    has_next: bool | None = None


class EnhancedFile(BaseModel):
    __tablename__ = "enhanced_files"

    file_id = Column(UUID(as_uuid=True), ForeignKey("files.id", ondelete="CASCADE"), nullable=False, index=True)
    file = relationship("File", uselist=False, back_populates="enhanced_files")
    s3_key = Column(String)
    enhancement_type = Column(Enum(FileEnhancementType), nullable=False)

    __table_args__ = (UniqueConstraint("file_id", "enhancement_type", name="uq_file_id_enhancement_type"),)

    @property
    def presigned_url(self) -> str:
        return flask.current_app.submission_s3_client.generate_presigned_url(self)

    def copy(
        self,
        old_to_new_ids: dict,
        submission_s3_client,
    ) -> EnhancedFile:
        new = EnhancedFile()
        new.id = uuid4()
        new.file_id = old_to_new_ids[self.file_id]
        new.s3_key = self.s3_key
        new.enhancement_type = self.enhancement_type
        for key in old_to_new_ids:
            if str(key) in new.s3_key:  # type: ignore
                new.s3_key = new.s3_key.replace(str(key), str(old_to_new_ids[key]))
        if new.s3_key != self.s3_key:
            copy_status = submission_s3_client.copy_file(self.s3_key, new.s3_key)
            # if the copying failed use the old key
            new.s3_key = self.s3_key if copy_status is False else new.s3_key
        return new
