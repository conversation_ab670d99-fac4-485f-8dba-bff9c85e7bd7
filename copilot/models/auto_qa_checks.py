from __future__ import annotations

from uuid import uuid4

from sqlalchemy import Column, ForeignKey, Index, Integer, String
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON>, UUID
from sqlalchemy.orm import relationship

from copilot.models._private import BaseModel


class AutoQACheck(BaseModel):
    __tablename__ = "auto_qa_checks"

    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    file_id = Column(UUID(as_uuid=True), ForeignKey("files.id", ondelete="CASCADE"), nullable=True, index=True)
    organization_id = Column(Integer, ForeignKey("organization.id", ondelete="CASCADE"), nullable=False)
    check_type = Column(String, nullable=False)
    original_value = Column(String, nullable=False)
    original_value_source = Column(String, nullable=True)
    check_status = Column(String, nullable=False)
    check_score = Column(Integer, nullable=False)
    explanation = Column(String, nullable=False)
    details = Column(JSONB, nullable=True)
    suggested_value = Column(String, nullable=True)
    suggested_value_reasoning = Column(String, nullable=True)
    suggested_value_confidence = Column(Integer, nullable=True)

    # Relationships
    submission = relationship("Submission", back_populates="auto_qa_checks")
    file = relationship("File", back_populates="auto_qa_checks")
    organization = relationship("Organization")

    __table_args__ = (
        Index(
            "ix_auto_qa_checks_organization_check_type_check_status", "organization_id", "check_type", "check_status"
        ),
        Index("ix_auto_qa_checks_check_type_check_status", "check_type", "check_status"),
    )

    def copy(self, old_to_new_ids: dict) -> AutoQACheck:
        new = AutoQACheck()
        new.id = uuid4()
        old_to_new_ids[self.id] = new.id
        new.submission_id = old_to_new_ids[self.submission_id]
        new.file_id = old_to_new_ids.get(self.file_id) if self.file_id else None
        new.organization_id = old_to_new_ids.get(self.organization_id, self.organization_id)
        new.check_type = self.check_type
        new.original_value = self.original_value
        new.original_value_source = self.original_value_source
        new.check_status = self.check_status
        new.check_score = self.check_score
        new.explanation = self.explanation
        new.details = self.details
        new.suggested_value = self.suggested_value
        new.suggested_value_reasoning = self.suggested_value_reasoning
        new.suggested_value_confidence = self.suggested_value_confidence
        return new
