from collections.abc import Sequence
from uuid import UUID

from flask import abort, current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from marshmallow import ValidationError
from sqlalchemy.orm import joinedload
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.submission_processing_state import SubmissionProcessingState

from copilot.logic.dao.submission_client_id_dao import SubmissionClientIdDAO
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.pds.file_handler import FileHandler
from copilot.logic.reports import handle_delete_report, start_report_processing
from copilot.models import ReportV2, Submission, SubmissionClearingIssue, db
from copilot.models.emails import Email
from copilot.models.types import ClearingStatus
from copilot.schemas.clearing.external import FinishExternalClearingRequestSchema
from copilot.utils import lock_func_params
from copilot.v3.controllers.submissions import get_light_submission
from copilot.v3.utils.support_users import assign_next_report_for_support_user

logger = get_logger()

FINISH_EXTERNAL_CLEARING_REQUEST_SCHEMA = FinishExternalClearingRequestSchema()


# POST /submissions/{submission_id}/clear
def clear_submission(submission_id: str | UUID, light_clearing: bool = False, publish_events: bool = True):
    log = logger.bind(submission_id=submission_id, light_clearing=light_clearing)
    log.info("Clearing")
    if current_user.applicable_settings is None and not current_user.is_internal_machine_user:
        abort(403)

    submission = get_light_submission(submission_id)
    if light_clearing and submission.light_cleared:
        abort(409, "Submission was already light cleared")
    if submission.is_processing:
        abort(409, "Submission is processing or does not require clearing")
    has_light_clearing_issues = any(ci.is_light for ci in submission.clearing_issues)
    if has_light_clearing_issues and not light_clearing and not current_user.is_internal_machine_user:
        abort(409, "Submission requires light clearing!")

    can_light_clear = current_user.can_light_clear
    can_clear = current_user.applicable_settings.can_resolve_clearing_issues or current_user.is_internal_machine_user
    if (light_clearing and not can_light_clear) or (not light_clearing and not can_clear):
        abort(403)

    ci: SubmissionClearingIssue
    cleared_counter = 0
    for ci in submission.clearing_issues:
        if ci.is_resolved:
            continue
        if ci.is_light == light_clearing:
            ci.is_resolved = True
            cleared_counter += 1
    if light_clearing:
        submission.light_cleared = True

    log.info("Cleared", cleared_counter=cleared_counter)
    if light_clearing and publish_events:
        current_app.clearing_service.publish_submission_light_cleared_event(submission)
        submission.is_processing = True
    db.session.commit()
    return None, 204


# POST /submissions/{submission_id}/start-clearing
def invoke_clearing_for_submission(submission_id: str | UUID, light_clearing: bool = False) -> tuple[None, int]:
    submission = get_light_submission(submission_id)
    report = submission.report
    if not report:
        abort(404)

    if (
        submission.processing_state == SubmissionProcessingState.NEEDS_CLEARING
        or submission.has_unresolved_clearing_issues
    ):
        if submission.processing_state != SubmissionProcessingState.NEEDS_CLEARING:
            submission.processing_state = SubmissionProcessingState.NEEDS_CLEARING
            submission.is_processing = False
            db.session.commit()
            assign_next_report_for_support_user(report_id=submission.report_id)
        abort(409, "The submission already requires clearing")
    light_not_required = submission.light_cleared or submission.processing_state == SubmissionProcessingState.COMPLETED
    if light_not_required and light_clearing:
        logger.warning("Light clearing was already completed", submission_id=submission_id)
        submission.light_cleared = True
        db.session.commit()
        # Publish event again, to protect against race conditions.
        current_app.clearing_service.publish_submission_light_cleared_event(submission)
        abort(409, "The submission was already cleared")

    current_app.clearing_service.handle_clearing(report, light_clearing_only=light_clearing)
    return None, 204


# POST /submissions/{submission_id}/finish-external-clearing
def finish_external_clearing_for_submission(submission_id: str | UUID, body: dict) -> tuple[None, int]:
    if isinstance(submission_id, str):
        submission_id = UUID(submission_id)

    finish_external_clearing_request = FINISH_EXTERNAL_CLEARING_REQUEST_SCHEMA.load(body)
    organization_id = finish_external_clearing_request.organization_id
    log = logger.bind(organization_id=organization_id, submission_id=submission_id)
    log.info("Finishing external clearing for submission")

    user_has_permissions = current_user.is_internal_machine_user or current_user.is_member_of_organization(
        organization_id
    )
    if not user_has_permissions:
        abort(403)

    try:
        current_app.clearing_service.finish_external_clearing(
            submission_id=submission_id, finish_external_clearing_request=finish_external_clearing_request
        )
    except ValueError as value_error:
        _reset_clearing_status(submission_id)
        log.info("ValueError during finishing external clearing", exc=value_error)
        abort(400, str(value_error))
    except ValidationError as validation_error:
        _reset_clearing_status(submission_id)
        log.info("ValidationError during finishing external clearing", exc=validation_error)
        abort(400, str(validation_error))
    except Exception as e:
        _reset_clearing_status(submission_id)
        log.exception("Error finishing external clearing", exc=e)
        abort(500, "Error finishing external clearing")
    return None, 204


def _reset_clearing_status(submission_id: UUID):
    submission = SubmissionDAO.get_minimal_submission(submission_id)
    submission.clearing_status = ClearingStatus.PRE_CLEARING
    db.session.commit()


def _copy_emails(source: ReportV2, target: ReportV2) -> None:
    if not source.correspondence_id and not target.correspondence_id:
        return

    from_emails = (
        (db.session.query(Email).filter(Email.correspondence_id == source.correspondence_id)).all()
        if source.correspondence_id
        else []
    )

    if not target.correspondence_id:
        target.correspondence = source.correspondence.copy()
    else:
        for email in from_emails:
            email_copy: Email = email.copy(target.correspondence_id)
            email_copy.type = None
            db.session.add(email_copy)


@lock_func_params(params_to_lock=["from_id", "to_id"], lock_name_prefix="merge_correspondence", lock_expire=120)
def merge_correspondence(from_id: str, to_id: str) -> tuple[None, int]:
    if not current_user.can_light_clear and not current_user.is_internal_machine_user:
        abort(403)

    from_submission, to_submission = _get_and_validate_submissions_for_merge(from_id, to_id)
    return _merge_submissions_internal(from_submission, to_submission)


@lock_func_params(params_to_lock=["from_id", "to_id"], lock_name_prefix="merge_verified_reports", lock_expire=120)
def merge_verified_reports(from_id: str, to_id: str) -> tuple[None, int]:
    if not current_user.can_merge_reports:
        abort(403)

    from_submission, to_submission = _get_and_validate_submissions_for_merge(
        from_id, to_id, is_verified_submissions_merge=True
    )

    return _merge_submissions_internal(from_submission, to_submission)


def _get_and_validate_submissions_for_merge(
    from_id: str, to_id: str, is_verified_submissions_merge: bool = False
) -> tuple[Submission, Submission]:
    submissions: Sequence[Submission] = (
        Submission.query.options(joinedload(Submission.report))
        .join(Submission.report)
        .filter(ReportV2.id.in_([from_id, to_id]))
        .all()
    )
    from_submission = [s for s in submissions if s.report.id == UUID(from_id)][0]
    to_submission = [s for s in submissions if s.report.id == UUID(to_id)][0]

    if from_submission.is_boss:
        abort(400, "Can't merge BOSS submissions")

    if from_submission.is_deleted or to_submission.is_deleted:
        abort(400, "One of the submissions is deleted, merge is not allowed")

    client_submission_ids: list[str] = (
        [i.client_submission_id for i in from_submission.client_submission_ids]
        if from_submission.client_submission_ids is not None
        else []
    )
    if client_submission_ids and SubmissionClientIdDAO.has_unique_submission_client_id(
        client_submission_ids, from_submission.id, from_submission.report.organization_id
    ):
        abort(400, "Can't merge from submission with unique client id")

    if not from_submission.report.correspondence_id or not to_submission.report.correspondence_id:
        abort(400, "Submissions must have correspondence")

    if from_submission.organization_id != to_submission.organization_id:
        abort(400, "Submissions must be from the same organization")

    if from_submission.report.id == to_submission.report.id:
        abort(400, "Submissions must be different")

    if from_submission.report.is_part_of_active_shadow or to_submission.report.is_part_of_active_shadow:
        abort(400, "Can't merge submissions that are part of active shadow. Please try again in 20 mins.")

    if is_verified_submissions_merge:
        if not from_submission.is_verified or not to_submission.is_verified:
            abort(400, "Both submissions must be verified")
    return from_submission, to_submission


def _merge_submissions_internal(from_submission: Submission, to_submission: Submission) -> tuple[None, int]:
    existing_files_checksums = {f.checksum for f in to_submission.files}

    logger.info("Merging correspondence", from_submission_id=from_submission.id, to_submission_id=to_submission.id)

    new_files = [
        f for f in from_submission.files if f.file_type != FileType.EMAIL and f.checksum not in existing_files_checksums
    ]

    if any(f.processing_state in FileProcessingState.processing_states() for f in new_files):
        abort(400, "Can't merge submissions with files in the middle of processing")

    old_to_new_ids = {from_submission.id: to_submission.id}
    copied_files_to_be_processed = []
    copied_files = []
    while True:
        batch = [
            f
            for f in new_files
            if f.id not in copied_files
            and (f.parent_file_id is None or f.parent_file_id in copied_files)
            and (f.replaced_by_file_ids is None or all(r in copied_files for r in f.replaced_by_file_ids))
        ]
        if not batch:
            break
        for file in batch:
            # We want to rerun processing for the copied files as most of the files undergo data consolidation
            # and can be impacted from the data in the existing files. For files without entities,
            # we don't care as their data will be copied later.
            copy = file.copy(old_to_new_ids, current_app.submission_s3_client, should_copy_processed_data=False)
            if file.file_type not in FileType.always_processed_types():
                copy.processing_state = FileProcessingState.CLASSIFIED
                copied_files_to_be_processed.append(copy)
                copy.cache_origin_file_id = file.id if from_submission.is_verified else None
            db.session.add(copy)
            copied_files.append(file.id)

    file_handler = FileHandler()
    for file in copied_files_to_be_processed:
        file_handler.handle_new_file_added(submission=to_submission, file=file)

    _copy_emails(from_submission.report, to_submission.report)

    if from_submission.report.tier is not None:
        to_submission.report.tier = min(
            to_submission.report.tier or from_submission.report.tier, from_submission.report.tier
        )

    db.session.commit()

    handle_delete_report(from_submission.report)

    if not to_submission.is_processing_enabled:
        start_report_processing(to_submission.report)

    return None, 204
