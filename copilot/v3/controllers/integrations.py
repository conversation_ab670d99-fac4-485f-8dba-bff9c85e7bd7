from flask_login import current_user
from static_common.enums.clients.integrations import (
    IntegrationOperation,
    IntegrationStatus,
)

from copilot.schemas.integration_logs import (
    IntegrationLogsBulkSchema,
    IntegrationLogsCreateResponseSchema,
    IntegrationLogsFullDetailsSchema,
    IntegrationLogsLowDetailsSchema,
)
from copilot.services.integration_logs import IntegrationLogsService

integration_logs_create_response_schema = IntegrationLogsCreateResponseSchema()
integration_logs_full_details_schema = IntegrationLogsFullDetailsSchema()
integration_logs_low_details_schema = IntegrationLogsLowDetailsSchema()
integration_logs_bulk_schema = IntegrationLogsBulkSchema()


def get_by_id(integration_log_id: str) -> tuple[dict, int]:
    integration_log = IntegrationLogsService.get_integration_log_by_id(integration_log_id)
    if not integration_log:
        return {}, 404
    return integration_logs_full_details_schema.dump(integration_log), 200


def create_new_integration_log(body: dict) -> tuple[dict, int]:
    integration_log = IntegrationLogsService.create_new_integration_log(body, commit=True)
    return integration_logs_create_response_schema.dump(integration_log), 201


def get_latest_integration_log_by_report_and_operation(report_id: str, operation: str) -> tuple[dict, int]:
    integration_log = IntegrationLogsService.get_latest_integration_log_by_report_and_operation(
        report_id, IntegrationOperation(operation)
    )
    if not integration_log:
        return {}, 404
    return integration_logs_low_details_schema.dump(integration_log), 200


def update_integration_log_by_id(integration_log_id: str, body: dict) -> tuple[dict, int]:
    integration_log = IntegrationLogsService.update_integration_log_by_id(integration_log_id, body, commit=True)
    if not integration_log:
        return {}, 404
    return integration_logs_create_response_schema.dump(integration_log), 200


def bulk_get_integration_error_logs(body: dict) -> tuple[dict, int]:
    if current_user.organization_id != body.get("organization_id"):
        return {}, 403

    integration_logs = IntegrationLogsService.bulk_get_integration_logs_by_status(body, IntegrationStatus.FAILURE)
    return integration_logs_bulk_schema.dump(integration_logs, many=True), 200
