from collections import Counter
from dataclasses import fields
from datetime import datetime
from http import HTTPStatus
from io import BytesIO
from tempfile import NamedTemporaryFile
from typing import Any
from uuid import UUID, uuid4
from zipfile import ZIP_DEFLATED, ZipFile
import json
import os
import re

from common.clients.boss_api_client import BOSS_DOC_ID_KEY
from common.utils.documents import identify_document
from common.utils.s3 import create_s3_key_capi
from dateutil.parser import parse
from events_common.model.types import StepFunctionStatus
from facts_client.model.first_party_source import FirstPartySource
from facts_client.model.processed_file import ProcessedFile as FactsProcessedFile
from file_processing.pdf_utils.pdf_splitter import PDFSplitter
from flask import Response, current_app, redirect
from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy import Float, String, case, func, select
from sqlalchemy.orm import aliased
from sqlalchemy.orm.exc import Object<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StaleDataError
from static_common.constants import NATIONWIDE_FILE_UPDATE_PROPERTIES_TRIGGER
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.fields import FirstPartyFieldsGroupType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.origin import Origin
from static_common.enums.parent import ParentType
from static_common.enums.sensible import SensibleStatus
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.models.file_onboarding import OnboardedFile
from static_common.models.first_party import FirstPartyFields
from static_common.schemas.file_additional_info import FileAdditionalInfoSchema
from static_common.schemas.first_party import FirstPartyFieldsSchema
from werkzeug.datastructures import FileStorage
from werkzeug.exceptions import Conflict
import connexion
import flask
import redis_lock

from copilot.clients.amplitude import AmplitudeClient
from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.kalepa_domain_events.kalepa_events_handler import KalepaEventsHandler
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.files import (
    FILE_EXISTENCE_FACT_MAPPING,
    add_file_to_db,
    check_duplicate_boss_file,
    create_file_existence_fact,
    create_file_logic,
    put_file_metrics_internal,
    transform_client_file_tags,
    upload_file,
)
from copilot.logic.loss_runs.manual_processing import (
    update_manual_processing_cache_for_file_completion,
)
from copilot.logic.nationwide_boss import handle_file_update
from copilot.logic.onboarded_files_transformation import load_data
from copilot.logic.pdf import PDFConverter
from copilot.logic.pds.file_handler import FileHandler
from copilot.logic.sensible import upload_failed_loss_run
from copilot.models import Loss, Organization, Submission, db
from copilot.models.files import ExternalFile, File, FileMetric, FilePatch
from copilot.models.label_studio import FileLabelStudio
from copilot.models.types import PermissionType, ReportShadowType, UserShadowFileState
from copilot.schemas.files import (
    AggregatedFileTypeSchema,
    ExternalFileSchema,
    FileForExternalConsumerSchema,
    FileMetricSchema,
    FilePatchSchema,
    FileSchema,
    FileStatsSchema,
    SplitFileRequestSchema,
)
from copilot.schemas.label_studio import (
    FileLabelStudioSchema,
    FilePageProcessedRequestSchema,
    FileSendToLabelStudioRequestSchema,
)
from copilot.schemas.presigned_url_request import PresignedUrlRequestSchema
from copilot.utils import (
    convert_to_uuid,
    get_s3_url,
    save_grouped_first_party_fields_to_s3,
)
from copilot.v3.controllers.reports import get_report_or_404
from copilot.v3.controllers.submissions import get_light_submission
from copilot.v3.utils.files import (
    _get_processed_files_data,
    compare_filenames,
    maybe_update_parent_file_status,
)

logger = get_logger()

file_schema = FileSchema(exclude=["s3_key", "presigned_url"])
file_for_external_consumer_schema = FileForExternalConsumerSchema()
file_schema_convert_additional_info = FileSchema(
    exclude=["s3_key", "presigned_url"], context={"map_additional_info_to_json": True}
)
file_patch_schema = FilePatchSchema()
get_file_schema = FileSchema()
get_file_stats_schema = FileStatsSchema()
external_file_schema = ExternalFileSchema()
file_additional_info_schema = FileAdditionalInfoSchema()
FLEET_FILE_TYPES = {FileType.DRIVERS, FileType.VEHICLES}
PROCESS_PDF_FILE_TYPES = {FileType.SITE_REPORT, FileType.SAFETY_MANUAL}
ONE_HOUR_IN_SECONDS = 3600
# of only these fields are updated, do not send file update events
# this is to prevent spamming
SKIP_SENDING_FILE_UPDATES_FOR_FIELDS = {"processing_state"}


def _download_file(key: str | None) -> BytesIO | None:
    return flask.current_app.submission_s3_client.get_file_as_file_storage(key)


def process_additional_first_party_data_request(body: dict) -> tuple[dict, int]:
    submission_id = body.get("submission_id")
    submission = get_light_submission(submission_id)
    if not submission.can_be_updated:
        flask.abort(422, "The submission is quoted and cannot be modified")
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
        flask.abort(403)
    try:
        grouped_first_party_fields = FirstPartyFieldsSchema().load(body.get("grouped_first_party_fields"))
        if grouped_first_party_fields and any(it for it in grouped_first_party_fields.fields_groups if it.fields):
            s3_key = save_grouped_first_party_fields_to_s3(
                submission.id, grouped_first_party_fields, body.get("file_name")
            )
            if current_app.submission_s3_client.check_if_file_exists(s3_key):
                KalepaEventsHandler.send_submission_file_uploaded_event(submission, s3_key)
            return {}, 204
    except Exception as e:
        flask.abort(400, f"Error while parsing the grouped_first_party_fields error={e}")
    return {}, 204


def _add_processed_file_to_facts_api(file_content: str, file: File, submission: Submission) -> None:
    document = FactsProcessedFile(
        id=str(
            identify_document(
                DocumentTypeID.PROCESSED_FILE,
                ParentType.SUBMISSION,
                submission.id,
                file_content,
            )
        ),
        parent_id=str(submission.id),
        parent_type=ParentType.SUBMISSION.value,
        document_type_id=DocumentTypeID.PROCESSED_FILE.value,
        body=file_content[:280],
        published_at=datetime.utcnow().isoformat(),
        original_file_name=file.name,
        original_file_type=file.file_type,
        content=file_content,
        source=FirstPartySource(
            source_type_id="FIRST_PARTY",
            organization_id=current_user.organization_id,
            submission_id=str(submission.id),
            original_name=file.name,
            file_id=str(file.id),
        ),
    )
    saved_document = current_app.facts_client.create_or_replace_document(document)
    logger.info(
        "Created processed file",
        document_id=saved_document.id,
        submission_id=submission.id,
    )


def create_file_and_convert_to_pdf(body: dict) -> Response:
    """Endpoint creating PDF file from the given body."""
    logger.info("Requested create pdf file", body=body)

    file_object = connexion.request.files.get("file")
    try:
        if file_object and file_object.filename.lower().endswith(".html"):
            # Check if the file already exists in the submission before converting to pdf
            if _check_email_file_exists():
                form_data = dict(**connexion.request.form)
                logger.warning("Email file already exists", submission_id=form_data["submission_id"])
                flask.abort(409, "File already exists in submission!")

            with NamedTemporaryFile() as temp_file:
                PDFConverter.save_to_file(file_object.read().decode("utf-8"), temp_file)
                file_object.filename = file_object.filename.replace(".html", ".pdf")
                file_object.stream = temp_file
                return _create_file_from_file_object(file_object, body)
        else:
            return _create_file_from_file_object(file_object, body)
    except Conflict:
        flask.abort(409, "File exists in submission!")


def _check_email_file_exists() -> bool:
    existing_email_file = None
    form_data = dict(**connexion.request.form)
    if (
        "submission_id" in form_data
        and "additional_info_json" in form_data
        and form_data.get("file_type") == FileType.EMAIL
    ):
        additional_info = json.loads(form_data["additional_info_json"])
        existing_email_file = File.query.filter(
            File.additional_info["email_id"].astext == additional_info["email_id"],
            File.submission_id == form_data["submission_id"],
            File.file_type == FileType.EMAIL,
        ).one_or_none()
    return existing_email_file is not None


def get_upload_url(body: dict):
    file_name = body.get("file_name")
    if not file_name:
        flask.abort(400, "Missing file name")
    report_id = body.get("report_id")
    if not report_id:
        flask.abort(400, "Missing report id")
    if not current_user.has_report_permission(PermissionType.EDITOR, report_id):
        flask.abort(403)

    report = get_report_or_404(report_id)
    submission_id = report.submission.id

    s3_key = "post/" + create_s3_key_capi(
        user_id=current_user.id,
        filename=file_name,
        submission_id=submission_id,
        submission_business_id=None,
    )

    file = File(
        id=uuid4(),
        name=file_name,
        file_type=FileType.UNKNOWN,
        origin=Origin.API,
        organization_id=report.organization_id,
        s3_key=s3_key,
    )

    client_file_tags = transform_client_file_tags(body.get("tags"))
    file.client_file_tags = client_file_tags
    file.client_file_type = body.get("client_file_type")
    file.external_identifier = file.id

    if file.boss_doc_id and check_duplicate_boss_file(submission_id, file.boss_doc_id):
        logger.warning(
            "Submission already has identical boss file",
            submission_id=file.submission_id,
            file=file.name,
            boss_doc_id=file.boss_doc_id,
        )
        flask.abort(
            409,
            f"File with same boss doc id {file.boss_doc_id} already exists in the submission",
        )

    db.session.add(file)
    db.session.commit()

    response = current_app.submission_s3_client.get_presigned_upload_url(key=file.s3_key)
    if FeatureFlagsClient.is_feature_enabled(FeatureType.INCLUDE_ID_IN_GET_UPLOAD_URL):
        response["id"] = str(file.external_identifier)
    return response, 201


def create_file(body: dict) -> Response:
    logger.info("create_file", body=body)
    file_object = connexion.request.files.get("file")
    return _create_file_from_file_object(file_object, body)


def create_file_external(body: dict) -> Response:
    logger.info("create_file_external", body=body)
    report_id = body.get("report_id") or connexion.request.form.get("report_id")
    if not current_user.has_report_permission(PermissionType.EDITOR, report_id):
        flask.abort(403)
    file_object = connexion.request.files.get("file")
    return _create_file_from_file_object(file_object, body, external=True)


def _check_if_file_uploaded(new_file_size: float, files_already_in_submission: list[File], file: File) -> bool:
    for f in files_already_in_submission:
        if f.classification not in ClassificationDocumentType.supported_acords() or f.id == file.id:
            continue
        if file.checksum and file.checksum == f.checksum:
            return True
        if f.s3_key and flask.current_app.submission_s3_client.check_if_file_exists(f.s3_key):
            object_size = flask.current_app.submission_s3_client.get_object_size(f.s3_key)
            if object_size == new_file_size and compare_filenames(f.name, file.name):
                return True
    return False


def send_file_processing_event(
    file: File,
    status: StepFunctionStatus,
    error: str | None = None,
    sensible_status: str | None = None,
    processing_state: FileProcessingState | None = None,
) -> None:
    if sensible_status:
        file.sensible_status = sensible_status
    if processing_state:
        file.processing_state = processing_state
        db.session.add(file)
        db.session.commit()
    if error:
        KalepaEventsHandler.send_file_processing_finished_event(
            submission_id=file.submission_id,
            file_id=file.id,
            status=status,
            error=str(error),
        )
    else:
        KalepaEventsHandler.send_file_processing_finished_event(
            submission_id=file.submission_id,
            file_id=file.id,
            status=status,
        )


def _handle_secura_external_call(file: File) -> File:
    # Try to detect betterview report
    if file.client_file_type == "Betterview Report":
        file.file_type = FileType.BETTERVIEW_REPORT
        file.classification = ClassificationDocumentType.BETTERVIEW_REPORT_PDF
        file.processing_state = FileProcessingState.CLASSIFIED

    return file


def _create_file_from_file_object(file_object: FileStorage, body: dict, external: bool = False) -> Response:
    replace_file_ids = body.get("replace_files", [])
    if file_object:
        form_data = dict(**connexion.request.form)
        # OpenAPI3.0 doesn't support exploding for multipart/form-data
        form_data["internal_notes"] = body.get("internal_notes", [])
        form_data["issues"] = body.get("issues", [])
        # we need both because NW will send with tags
        client_file_tags = transform_client_file_tags(body.get("client_file_tags") or body.get("tags"))
        form_data["client_file_tags"] = client_file_tags if client_file_tags else None
        file = file_schema.load(form_data)
        report_id = (
            connexion.request.form.report_id if hasattr(connexion.request.form, "report_id") else body.get("report_id")
        )
        submission_id = None
        if report_id:
            report = get_report_or_404(report_id)
            if report.submission:
                submission_id = report.submission.id
            else:
                logger.warning(
                    "Submission not found",
                    report_id=report_id,
                    file_name=file_object.filename,
                )
                flask.abort(422, "The submission is not found")
        file.submission_id = file.submission_id or submission_id or body.get("submission_id")
        logger.info(
            "file submission_id",
            submission_id=file.submission_id,
            file_name=file_object.filename,
            report_id=report_id,
        )
        file.parent_file_id = file.parent_file_id or body.get("parent_file_id")
        file.is_internal = body.get("is_internal", False)
        file.hidden = body.get("hidden", False)

        if external:
            # this is for files less than 10MB and at the time being only Nationwide could call us to create files
            # because we are returning id and BOSS will store this file id for future interactions with the file
            # so, even if we create a shadow report, the reference in BOSS will be valid.
            file_id = uuid4()
            file.id = file_id
            file.external_identifier = file_id

        if external or not file.origin:
            file.origin = Origin.API
    else:
        instance = File()
        if "name" not in connexion.request.form:
            flask.abort(400, "Name is required for file")
        if "s3_key" in connexion.request.form:
            s3_key = connexion.request.form["s3_key"]
            file = File.query.filter(File.s3_key == s3_key, File.submission_id.is_(None)).first()
            if file:
                instance = file
        file = get_file_schema.load(connexion.request.form, instance=instance)

    if (external and Organization.is_secura_for_id(current_user.organization_id)) or os.environ.get(
        "KALEPA_ENV", "prod"
    ) in ["stage", "dev"]:
        file = _handle_secura_external_call(file)

    created_file = create_file_logic(file_object, file, replace_file_ids)
    if external:
        external_file = created_file.to_external_file()
        return Response(external_file_schema.dumps(external_file), status=201)
    result = {"file": file_schema_convert_additional_info.dump(created_file)}
    response = flask.jsonify(result)  # turn pretty print off of this case
    response.status_code = 201  # default flask.jsonify sets 200
    return response


def get_file(submission_id: UUID, id: UUID) -> Any:
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id, submission.report_id):
        flask.abort(403)
    file = File.query.get_or_404(id, description="The file with specified ID wasn't found")
    if file.submission_id != submission.id and not current_user.is_machine_user:
        flask.abort(403)
    return _prepare_file_response(file)


def get_file_by_parent_file_id(submission_id: UUID, id: UUID) -> Any:
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id, submission.report_id):
        flask.abort(403)
    file = File.query.filter(File.parent_file_id == id, File.submission_id == submission.id).first_or_404()
    return _prepare_file_response(file)


# This endpoint looks almost exactly as get_file, but it's used when we do not know
# submission_id upfront
def get_file_by_id(file_id: UUID) -> Any:
    file = File.query.get_or_404(file_id, description="The file with specified ID wasn't found")
    submission = get_light_submission(file.submission_id, throw_not_found_for_deleted=False)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)
    return _prepare_file_response(file)


def get_files(submission_id: UUID) -> Any:
    submission = get_light_submission(submission_id, throw_not_found_for_deleted=False)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)
    files = db.session.query(File).filter(File.submission_id == submission_id).all()

    if not current_user.is_kalepa:
        files = [file for file in files if not file.is_deleted]

    return _prepare_file_response(files, many=True)


def get_file_stats(organization_id: int, created_after: str | None = None) -> Any:
    if not current_user.is_member_of_organization(organization_id):
        flask.abort(403)

    base_filters = [
        File.organization_id == organization_id,
        File.file_type != FileType.MERGED,
        Submission.processing_state == SubmissionProcessingState.COMPLETED,
    ]
    if created_after:
        base_filters.append(File.created_at >= parse(created_after))

    # Some shared columns to reuse
    correctly_classified_count_column = func.count().filter(File.user_file_type.is_(None))
    incorrectly_classified_count_column = func.count().filter(File.user_file_type.isnot(None))
    user_file_type_or_file_type_column = func.coalesce(File.user_file_type, File.file_type)
    acord_form_type_column = File.additional_info.op("#>>")("{acord, form_name}")

    classification_accuracy_column = case(
        [(func.count() == 0, 0)],
        else_=(correctly_classified_count_column.cast(Float) / func.count().cast(Float)),
    ).label("classification_accuracy")

    not_applicable_for_processing_count_column = func.count().filter(
        File.processing_state == FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING
    )
    total_files_without_not_applicable_count_column = func.count() - not_applicable_for_processing_count_column
    processed_files_count_column = func.count().filter(
        File.processing_state.in_(
            [
                FileProcessingState.PROCESSED,
                FileProcessingState.COMPLETED,
            ]
        )
    )
    processing_accuracy_column = case(
        [
            (
                total_files_without_not_applicable_count_column == 0,
                0,
            )
        ],
        else_=processed_files_count_column.cast(Float) / total_files_without_not_applicable_count_column.cast(Float),
    ).label("processing_accuracy")

    # Header query
    classification_stats = (
        db.session.query(
            correctly_classified_count_column.label("correct"),
            incorrectly_classified_count_column.label("incorrect"),
            func.count().filter(File.classification.ilike("unknown")).label("unknown"),
        )
        .join(Submission, Submission.id == File.submission_id)
        .filter(*base_filters)
        .first()
    )

    # First chart query (Classified file types)
    classified_file_type_stats = (
        db.session.query(
            user_file_type_or_file_type_column.label("file_type"),
            func.count().label("count"),
        )
        .join(Submission, Submission.id == File.submission_id)
        .filter(*base_filters)
        .group_by(user_file_type_or_file_type_column)
    ).all()

    # Second chart query (Acord form distribution)
    acord_form_stats = (
        db.session.query(
            acord_form_type_column.label("form_type"),
            func.count().label("count"),
        )
        .join(Submission, Submission.id == File.submission_id)
        .filter(
            *base_filters,
            File.file_type == FileType.ACORD_FORM,
            acord_form_type_column.isnot(None),
        )
        .group_by(
            acord_form_type_column,
        )
    ).all()

    # Files Classification and Files Processing per Document Type tables for non-acord
    classification_and_processing_non_acord_stats = (
        db.session.query(
            user_file_type_or_file_type_column.label("file_type"),
            func.count().label("total_files"),
            correctly_classified_count_column.label("correctly_classified"),
            incorrectly_classified_count_column.label("incorrectly_classified"),
            classification_accuracy_column,
            processed_files_count_column.label("processed"),
            not_applicable_for_processing_count_column.label("not_applicable"),
            processing_accuracy_column,
        )
        .join(Submission, Submission.id == File.submission_id)
        .filter(
            *base_filters,
            user_file_type_or_file_type_column != FileType.ACORD_FORM,
        )
        .group_by(user_file_type_or_file_type_column)
        .all()
    )

    # Files Classification and Files Processing per Document Type tables for acord
    classification_and_processing_acord_stats = (
        db.session.query(
            acord_form_type_column.label("form_type"),
            func.count().label("total_files"),
            correctly_classified_count_column.label("correctly_classified"),
            incorrectly_classified_count_column.label("incorrectly_classified"),
            classification_accuracy_column,
            processed_files_count_column.label("processed"),
            not_applicable_for_processing_count_column.label("not_applicable"),
            processing_accuracy_column,
        )
        .join(Submission, Submission.id == File.submission_id)
        .filter(
            *base_filters,
            user_file_type_or_file_type_column == FileType.ACORD_FORM,
            acord_form_type_column.isnot(None),
        )
        .group_by(acord_form_type_column)
        .all()
    )

    # Files Processing per Submission table
    processing_by_submission_stats = (
        db.session.query(
            Submission.report_id.cast(String).label("report_id"),
            Submission.name,
            func.count().label("total_files"),
            processed_files_count_column.label("processed"),
            not_applicable_for_processing_count_column.label("not_applicable"),
            processing_accuracy_column,
        )
        .join(Submission, File.submission_id == Submission.id)
        .filter(*base_filters)
        .group_by(Submission.report_id, Submission.name)
        .all()
    )

    return _prepare_file_stats_response(
        classification_stats,
        classified_file_type_stats,
        acord_form_stats,
        classification_and_processing_non_acord_stats,
        classification_and_processing_acord_stats,
        processing_by_submission_stats,
    )


def _prepare_file_stats_response(
    classification_stats,
    classified_file_type_stats,
    acord_form_stats,
    classification_and_processing_non_acord_stats,
    classification_and_processing_acord_stats,
    processing_by_submission_stats,
) -> Response:
    response = flask.Response(
        json.dumps(
            get_file_stats_schema.load(
                {
                    "classification_stats": dict(classification_stats),
                    "classified_file_type_stats": [dict(x) for x in classified_file_type_stats],
                    "acord_form_stats": [dict(x) for x in acord_form_stats],
                    "classification_and_processing_non_acord_stats": [
                        dict(x) for x in classification_and_processing_non_acord_stats
                    ],
                    "classification_and_processing_acord_stats": [
                        dict(x) for x in classification_and_processing_acord_stats
                    ],
                    "processing_by_submission_stats": [dict(x) for x in processing_by_submission_stats],
                }
            )
        )
    )
    response.mimetype = "application/json"
    return response


def _prepare_file_response(file: File, many: bool = False) -> Response:
    response = flask.Response(json.dumps(get_file_schema.dump(file, many=many)))
    response.mimetype = "application/json"

    # We currently sign s3 files for just 1 hour, so we cannot cache this response for a longer period of time
    response.headers["Cache-Control"] = f"maxage={ONE_HOUR_IN_SECONDS}"
    return response


def _has_processed_data(file: File) -> bool:
    supported_acords_list = list(ClassificationDocumentType.supported_acords())
    if file.classification not in [
        ClassificationDocumentType.SOV_SPREADSHEET,
        ClassificationDocumentType.VEHICLES_SPREADSHEET,
        *supported_acords_list,
    ]:
        return False
    processed_data_list = _get_processed_files_data(
        [file.id],
        "entity_mapped_data",
        ["file_id", "processed_data", "entity_mapped_data"],
        False,
    )
    if len(processed_data_list) == 0:
        return False
    processed_data = processed_data_list[0]
    load_data(processed_data.data, "entity_mapped_data", False)
    entity_mapped_data = processed_data.data.entity_mapped_data
    if not isinstance(entity_mapped_data, OnboardedFile):
        logger.warning("Entity mapped data is not of instance OnboardedFile", file_id=file.id)
        return False
    if len(entity_mapped_data.entities) or len(entity_mapped_data.entity_information) or len(entity_mapped_data.fields):
        return True
    return False


def _handle_file_processing_state_change(file: File, submission: Submission, file_patch: FilePatch) -> None:
    if file_patch.processing_state == FileProcessingState.PROCESSED and not _is_email_file_for_verified_submission(
        file, submission
    ):
        waiting_for_onboarding = file.classification in ClassificationDocumentType.data_onboarding_classifications()
        waiting_for_entity_mapping = file.classification in ClassificationDocumentType.entity_mapping_classifications()
        if waiting_for_onboarding or waiting_for_entity_mapping:
            file.processing_state = (
                FileProcessingState.WAITING_FOR_DATA_ONBOARDING
                if waiting_for_onboarding
                else FileProcessingState.WAITING_FOR_ENTITY_MAPPING
            )
    elif file_patch.processing_state == FileProcessingState.DATA_ONBOARDED or (
        file.classification in ClassificationDocumentType.skip_onboarding_classifications()
        and file_patch.processing_state == FileProcessingState.WAITING_FOR_DATA_ONBOARDING
    ):
        file.processing_state = FileProcessingState.PROCESSED
    else:
        file.processing_state = file_patch.processing_state


def update_file(submission_id: UUID, id: UUID, body: dict) -> tuple[dict, int]:
    try:
        return _update_file_internal(submission_id, id, body)
    except (ObjectDeletedError, StaleDataError) as e:
        db.session.rollback()
        logger.warning("Could not update file due to integrity error", e=e, submission_id=submission_id)
        flask.abort(409, "The file was deleted or modified by another user")


def _update_file_internal(submission_id: UUID, id: UUID, body: dict) -> tuple[dict, int]:
    file_patch: FilePatch = file_patch_schema.load(body)
    updated_fields = [field.name for field in fields(FilePatch) if getattr(file_patch, field.name) is not None]
    submission = get_light_submission(submission_id, additional_fields=["processing_state"])
    sensible_status_changed = False
    should_ignore_file = False
    is_external_id_set = file_patch.external_identifier is not None
    is_boss_doc_id_set = bool(file_patch.client_file_tags and BOSS_DOC_ID_KEY in file_patch.client_file_tags)
    is_added_to_boss_update = is_external_id_set and is_boss_doc_id_set

    if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
        flask.abort(403)

    if not submission.can_be_updated and not file_patch.processing_state:
        flask.abort(422, "The submission is quoted and cannot be modified")

    file: File = File.query.get_or_404(id, description="The file with specified ID wasn't found")

    if file_patch.processing_state:
        should_ignore_file = (
            file_patch.processing_state == FileProcessingState.IGNORED
            and file.processing_state != FileProcessingState.IGNORED
        )
        if (
            file.processing_state == FileProcessingState.IGNORED
            and file_patch.processing_state != FileProcessingState.IGNORED
        ):
            logger.warning(
                "Cannot change processing state of ignored file",
                file_id=id,
                submission_id=submission.id,
            )
            flask.abort(422, "Cannot change processing state of ignored file")

        if file_patch.processing_state == FileProcessingState.WAITING_FOR_PROCESSING:
            lock_name = FileHandler.get_classification_lock_name(file)
            logger.info("Acquiring lock", lock=lock_name)
            try:
                with redis_lock.Lock(current_app.locks_client, name=lock_name, expire=10):
                    db.session.refresh(file)
                    if file.processing_state != FileProcessingState.CLASSIFIED:
                        flask.abort(422, "File is already being processed")
            except redis_lock.NotAcquired:
                logger.warning("Lock already expired", lock=lock_name)

        file.processing_state = file_patch.processing_state

    original_file_type = file.file_type
    original_user_file_type = file.user_file_type

    if file_patch.is_internal is not None:
        file.is_internal = file_patch.is_internal

    if file_patch.external_identifier is not None:
        file.external_identifier = file_patch.external_identifier

    trigger_classification = False
    if file_patch.hidden is not None:
        if file_patch.hidden is False and file.hidden is True:
            file.classification = "NOT_CLASSIFIED"
            trigger_classification = True

        file.hidden = file_patch.hidden

    new_file_type = file_patch.file_type
    if new_file_type and file.file_type == FileType.LOSS_RUN and new_file_type != FileType.LOSS_RUN:
        try:
            Loss.query.filter_by(file_id=file.id).delete()
            logger.info(
                "Deleted Loss records due to file_type change.",
                file_id=file.id,
                submission_id=submission.id,
                file_type=file.file_type,
                new_file_type=new_file_type,
            )
        except:
            logger.warning(
                "Failed to delete Loss records due to file_type change.", file_id=file.id, submission_id=submission.id
            )

    if new_file_type:
        file.file_type = new_file_type

    if file_patch.classification:
        file.classification = file_patch.classification.value

    if file_patch.custom_file_type_id:
        if _has_processed_data(file):
            flask.abort(
                422,
                "The file has been successfully parsed. Reload the file if file type change is required.",
            )

        file.custom_file_type_id = file_patch.custom_file_type_id
        FileHandler.handle_custom_file_added(file, submission)

    if file_patch.user_file_type:
        old_file_type = file.user_file_type or file.file_type
        file.user_file_type = file_patch.user_file_type
        if old_file_type != file_patch.user_file_type:
            if (
                not current_user.is_cs_manager_or_internal_machine_user
                and file.initial_classification_confidence
                and file.initial_classification_confidence >= 0.95
            ):
                flask.abort(
                    403,
                    "Only CS Managers can change file type after classification with high confidence",
                )
            if _has_processed_data(file):
                flask.abort(
                    422,
                    "The file has been successfully parsed. Reload the file if file type change is required.",
                )
            if file_patch.user_file_type == FileType.MERGED:
                flask.abort(422, "This file is split, please reclassify the child files instead.")
            FileHandler.handle_file_user_type_changed(submission, file)

    if file_patch.content_type:
        file.content_type = file_patch.content_type

    if file_patch.initial_classification:
        file.initial_classification = file_patch.initial_classification.value

    if file_patch.initial_classification_confidence:
        file.initial_classification_confidence = file_patch.initial_classification_confidence

    if file_patch.comment:
        file.comment = file_patch.comment

    if file_patch.is_locked:
        file.is_locked = file_patch.is_locked

    if file_patch.internal_note:
        file.internal_notes = [file_patch.internal_notes] + (file.internal_notes or [])

    if file_patch.internal_notes:
        file.internal_notes = file_patch.internal_notes

    if file_patch.issue:
        file.issues = (file.issues or []) + [file_patch.issue]
    if file_patch.issues:
        file.issues = file_patch.issues

    if file_patch.additional_info:
        file.additional_info = file_additional_info_schema.dump(file_patch.additional_info)

    if file_patch.client_file_tags:
        client_tags = transform_client_file_tags(file_patch.client_file_tags) or {}
        if file.boss_doc_id:
            client_tags[BOSS_DOC_ID_KEY] = file.boss_doc_id

        file.client_file_tags = client_tags
    if file_patch.client_file_type:
        file.client_file_type = file_patch.client_file_type

    if (
        file.classification
        in [
            ClassificationDocumentType.LOSS_RUN_NO_CLAIM_PDF.value,
            ClassificationDocumentType.LOSS_SUMMARY_PDF.value,
        ]
        and file.sensible_status in SensibleStatus.invalid_sensible_statuses  # type: ignore
    ):
        file.sensible_status = SensibleStatus.NO_CLAIM_NUMBERS.value
        sensible_status_changed = True

    # if invalid sensible status but file was classified as Merged, it means it only contained no-claims or summaries
    if (
        file.classification == ClassificationDocumentType.MERGED
        and file.sensible_status in SensibleStatus.invalid_sensible_statuses
    ):
        file.sensible_status = None
        sensible_status_changed = True

    if (
        file.classification == ClassificationDocumentType.LOSS_RUN_PDF.value
        and file.sensible_status == SensibleStatus.NO_LOSSES.value
    ):
        file.sensible_status = SensibleStatus.UNCERTAIN.value
        sensible_status_changed = True
        try:
            upload_failed_loss_run(file)
        except:
            logger.exception("Failed to upload failed loss run file to s3 bucket", file_id=file.id)

    if should_ignore_file:
        _ignore_file(file)
    else:
        add_file_to_db(file)

    if sensible_status_changed:
        maybe_update_parent_file_status(file)

    if (
        file.file_type == FileType.LOSS_RUN.value
        and file.classification
        not in {
            ClassificationDocumentType.LOSS_RUN.value,
            ClassificationDocumentType.LOSS_RUN_PDF.value,
            ClassificationDocumentType.LOSS_RUN_SPREADSHEET.value,
        }
        and FileProcessingState.is_final_state(file.processing_state)
    ):
        update_manual_processing_cache_for_file_completion(submission_id=str(submission_id), file_id=str(file.id))

    if (
        (file_existence_fact := FILE_EXISTENCE_FACT_MAPPING.get(file.file_type))
        and file.file_type != original_file_type
        and file.submission_id
    ):
        create_file_existence_fact(file_existence_fact, file)
    elif (
        (file_existence_fact := FILE_EXISTENCE_FACT_MAPPING.get(file.user_file_type))
        and file.user_file_type != original_user_file_type
        and file.submission_id
    ):
        create_file_existence_fact(file_existence_fact, file)

    if (
        len(set(updated_fields) - SKIP_SENDING_FILE_UPDATES_FOR_FIELDS) > 0
        and Organization.is_nationwide_for_id(submission.organization_id)
        and len(NATIONWIDE_FILE_UPDATE_PROPERTIES_TRIGGER.intersection(set(updated_fields))) > 0
        # it means that the call was made from infra-events right after it was added to boss.
        # So there is no need to update it in BOSS again.
        and not is_added_to_boss_update
    ):
        is_update_successful = handle_file_update(submission, file)

        if not is_update_successful:
            KalepaEventsHandler.send_submission_file_updated_event(submission, file, updated_fields=updated_fields)

    if trigger_classification:
        FileHandler().handle_new_file_added(submission, file)

    return file_schema.dump(file), 201


def update_file_external(report_id: UUID, external_id: UUID, body: dict) -> tuple[dict, int]:
    if not current_user.has_report_permission(PermissionType.EDITOR, report_id):
        flask.abort(403)

    submission = SubmissionDAO.get_minimal_submission(report_id=report_id)

    if not submission.can_be_updated:
        flask.abort(422, "The submission is frozen and cannot be modified")

    file: File = File.query.filter(
        File.external_identifier == external_id, File.submission_id == submission.id
    ).one_or_none()

    if not file:
        logger.warning(
            "File not found by external id, Trying id",
            external_id=external_id,
            report_id=report_id,
        )
        file = File.query.filter(File.id == external_id, File.submission_id == submission.id).one_or_none()
        if not file:
            flask.abort(404, "The file with specified ID wasn't found")
        logger.warning(
            "File found by id, instead of external_id",
            external_id=external_id,
            report_id=report_id,
        )

    external_file: ExternalFile = external_file_schema.load(body)
    external_file.id = file.external_identifier or file.id
    external_file.name = file.name

    file.client_file_type = external_file.client_file_type
    client_tags = transform_client_file_tags(external_file.tags) or {}
    if file.boss_doc_id:
        client_tags[BOSS_DOC_ID_KEY] = file.boss_doc_id
    file.client_file_tags = client_tags
    db.session.commit()

    return external_file_schema.dump(external_file), 200


def _is_email_file_for_verified_submission(file: File, submission: Submission) -> bool:
    return file.file_type == FileType.EMAIL and submission.is_verified


def _ignore_file(file: File) -> None:
    submission = SubmissionDAO.get_minimal_submission(
        file.submission_id,
        additional_fields=["processing_state", "stage"],
        include_report=True,
        report_additional_fields=["shadow_type"],
    )

    if not submission.is_auto_processed:
        logger.warning(
            "Cannot ignore file in submission with disabled processing",
            file_id=id,
            submission_id=submission.id,
        )
        flask.abort(400, "Cannot ignore file in submission with disabled processing")

    if (
        submission.is_processing_completed
        and file.processing_state in FileProcessingState.final_states_with_processed_data()
    ):
        logger.warning(
            "Cannot ignore file from completed submission",
            file_id=id,
            submission_id=submission.id,
        )
        flask.abort(400, "The submission is completed and file was already processed")

    if file.file_type == FileType.EMAIL:
        logger.warning("Cannot ignore email files", file_id=id, submission_id=submission.id)
        flask.abort(400, "Cannot ignore email files")

    if submission.is_verified and not current_user.is_cs_manager_or_internal_machine_user:
        logger.warning(
            "Cannot ignore files in verified submission",
            file_id=id,
            submission_id=submission.id,
        )
        flask.abort(400, "Cannot ignore files in verified submission")

    logger.info("Ignoring file", file_id=file.id, submission_id=submission.id)
    FileHandler().handle_file_ignored(submission, file)


def delete_file_external(report_id: UUID, external_id: UUID) -> tuple[dict, int]:
    if not current_user.has_report_permission(PermissionType.EDITOR, report_id):
        flask.abort(403)

    submission = SubmissionDAO.get_minimal_submission(
        report_id=report_id,
        additional_fields=["processing_state", "stage"],
        include_report=True,
        report_additional_fields=["shadow_type"],
        include_files=True,
        files_additional_fields=[
            "parent_file_id",
            "replaced_by_file_ids",
            "processing_state",
            "client_file_tags",
        ],
    )

    file = File.query.filter(File.external_identifier == external_id, File.submission_id == submission.id).one_or_none()
    if not file:
        logger.warning("File not found", external_id=external_id, report_id=report_id)
        flask.abort(
            404,
            f"File[external_identifier={external_id}, report_id={report_id}] does not exist",
        )

    return _delete_file(submission, file)


def delete_file(submission_id: UUID, id: UUID) -> tuple[dict, int]:
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission_id):
        flask.abort(403)

    submission = SubmissionDAO.get_minimal_submission(
        submission_id,
        additional_fields=["processing_state", "stage"],
        include_report=True,
        report_additional_fields=["shadow_type"],
        include_files=True,
        files_additional_fields=[
            "parent_file_id",
            "replaced_by_file_ids",
            "processing_state",
            "client_file_tags",
        ],
    )
    file = File.query.filter(File.id == id, File.submission_id == submission_id).one_or_none()
    if not file:
        logger.warning("File not found", id=id, submission_id=submission_id)
        flask.abort(404, "File not found")

    return _delete_file(submission, file)


def _handle_boss_delete_file(submission: Submission, file: File):
    bad_request_prefix = "Bad request for file delete"
    parent_files = get_parent_files(submission.files, file)
    files_to_be_checked = [*parent_files, file]
    for f in files_to_be_checked:
        if f.is_rose_file:
            msg = "ROSE file cannot be deleted."
            logger.warning(
                f"{bad_request_prefix} - {msg}",
                file_id=file.id,
                submission_id=submission.id,
                fail_check_file_id=f.id,
            )
            flask.abort(400, msg)

    if current_user.is_nationwide:
        FileHandler().soft_delete_file(submission, file)
        db.session.commit()


def _has_file_processed_data(submission: Submission, file: File) -> bool:
    child_files = get_child_files(submission.files, [file.id])
    files_to_be_checked = [*child_files, file]
    for f in files_to_be_checked:
        if f.replaced_by_file_ids or (
            submission.is_processing_completed
            and f.processing_state in FileProcessingState.final_states_with_processed_data()
        ):
            return True
    return False


def _delete_file(submission: Submission, file: File) -> tuple[dict, int]:
    def _abort_with_warning(msg: str) -> None:
        bad_request_prefix = "Bad request for file delete"
        logger.warning(
            f"{bad_request_prefix} - {msg}",
            file_id=file.id,
            submission_id=submission.id,
        )
        flask.abort(400, msg)

    if not submission.can_be_updated:
        _abort_with_warning("File cannot be deleted for frozen submission.")

    if submission.is_boss:
        _handle_boss_delete_file(submission, file)
        if file.is_deleted:
            return {}, 204

    if file.is_internal and not current_user.is_tier_2_or_internal_machine_user:
        _abort_with_warning("Cannot delete internal files.")

    if submission.processing_state == SubmissionProcessingState.DATA_ONBOARDING and submission.is_processing:
        _abort_with_warning("The submission is being completed, you cannot remove files.")

    if submission.report.shadow_type == ReportShadowType.HAS_ACTIVE_SHADOW or _has_file_processed_data(
        submission, file
    ):
        if not current_user.is_kalepa and FeatureFlagsClient.is_feature_enabled_for_request_user(
            FeatureType.ALLOW_CREATE_OR_REPLACE_FILES_FOR_VERIFIED_SUB
        ):
            for f in [*get_child_files(submission.files, [file.id]), file]:
                f.is_required_shadow_processing = True
                f.user_shadow_state = UserShadowFileState.DELETED
            FileHandler().handle_files_through_user_shadow_report(submission)
            return {}, 204

        if submission.report.shadow_type == ReportShadowType.HAS_ACTIVE_SHADOW:
            _abort_with_warning("File cannot be deleted during submission reprocessing.")

        _abort_with_warning("Processed file cannot be deleted.")

    if not file.is_internal and current_user.is_support and not current_user.is_cs_manager_or_internal_machine_user:
        _abort_with_warning("Cannot delete non-internal files.")

    FileHandler().handle_file_deleted(submission, file)

    return {}, 204


def get_child_files(files: list[File], parent_file_ids: list[UUID]) -> list[File]:
    child_files = [f for f in files if f.parent_file_id in parent_file_ids]
    if not child_files:
        return []
    return child_files + get_child_files(files, [f.id for f in child_files])


def get_parent_files(files: list[File], file: File) -> list[File]:
    parent_file = next((f for f in files if f.id == file.parent_file_id), None)
    if not parent_file:
        return []
    return [parent_file, *get_parent_files(files, parent_file)]


def get_presigned_url(body: dict) -> dict:
    presigned_url_request = PresignedUrlRequestSchema().load(body)
    key = presigned_url_request.s3_key

    bucket_patterns = {
        "INGESTION_S3_BUCKET_NAME": r"^website-screenshots/",
        "COPILOT_S3_BUCKET_NAME": r".*",
    }
    if key and not re.match(bucket_patterns.get(presigned_url_request.bucket_name, r".*"), key):
        flask.abort(400, "Invalid key for ingestion bucket")

    bucket_name = os.environ.get(presigned_url_request.bucket_name)
    if not bucket_name:
        flask.abort(422, "S3 bucket name env var is not set")

    if not key:
        key = create_s3_key_capi(
            current_user.id,
            presigned_url_request.filename,
            presigned_url_request.submission_id,
            None,
        )
    return {
        "presigned_url": get_s3_url(key, bucket_name, presigned_url_request.method),
        "s3_key": key,
    }


def get_file_types() -> list[str]:
    return [e.value for e in FileType]


def get_aggregated_file_types(organization_id: int):
    if not current_user.is_member_of_organization(organization_id):
        flask.abort(403)

    SubFile = aliased(File)

    sample_subquery = (
        select(
            [
                func.json_build_object(
                    "id",
                    SubFile.id,
                    "name",
                    SubFile.name,
                    "submission_id",
                    SubFile.submission_id,
                )
            ]
        )
        .join(Submission)
        .where(
            (SubFile.organization_id == int(organization_id))
            & (SubFile.file_type == File.file_type)
            & (Submission.is_deleted == False)
            & (SubFile.is_deleted == False)
        )
        .order_by(SubFile.id)
        .limit(5)
        .scalar_subquery()
    )

    query = (
        db.session.query(
            func.count(File.id).label("file_count"),
            File.file_type,
            func.array(sample_subquery).label("sampled_files"),
        )
        .join(Submission)
        .filter(File.organization_id == int(organization_id), File.is_deleted == False, Submission.is_deleted == False)
        .group_by(File.file_type)
    )

    results = query.all()
    return AggregatedFileTypeSchema().dump(results, many=True), HTTPStatus.OK


def file_send_to_label_studio(body: dict) -> tuple[dict, int]:
    request = FileSendToLabelStudioRequestSchema().load(body)
    new_pages = request.pages

    file = File.query.get(request.file_id)
    existing_pages = FileLabelStudio.query.filter(FileLabelStudio.file_id == request.file_id).all()
    if not file and existing_pages:
        logger.warning(
            "File was removed. Removing associated LabelStudio pages.",
            file_id=request.file_id,
        )
        db.session.delete(existing_pages)
        db.session.commit()
        flask.abort(HTTPStatus.NOT_FOUND, "File not found")

    if existing_pages:
        existing_page_numbers = {p.page_num for p in existing_pages}

        if len(existing_page_numbers) == request.total_pages:
            return (
                FileLabelStudioSchema().dump(existing_pages, many=True),
                HTTPStatus.OK,
            )
        else:
            logger.warning(
                "Partially created pages. Creating missing ones",
                file_id=request.file_id,
                existing_page_numbers=existing_page_numbers,
            )
            new_pages = [page for page in request.pages if page.page_num not in existing_page_numbers]

    pages = [
        FileLabelStudio(
            file_id=request.file_id,
            total_pages=request.total_pages,
            page_num=page.page_num,
            status=FileProcessingState.WAITING_FOR_HUMAN_INPUT,
            image_s3_key=page.image_s3_key,
            task_id=page.task_id,
        )
        for page in new_pages
    ]

    file.processing_state = FileProcessingState.WAITING_FOR_HUMAN_INPUT

    try:
        db.session.add_all(pages)
        db.session.add(file)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger.exception(
            "Could not register page status for Label Studio processing in database",
            file_id=request.file_id,
        )
        flask.abort(HTTPStatus.INTERNAL_SERVER_ERROR, str(e))

    return FileLabelStudioSchema().dump(pages, many=True), HTTPStatus.CREATED


def update_file_label_studio_status(body: dict) -> tuple[dict | None, int]:
    request = FilePageProcessedRequestSchema().load(body)

    page = FileLabelStudio.query.filter(
        FileLabelStudio.file_id == request.file_id,
        FileLabelStudio.page_num == request.page_num,
    ).first()

    if not page:
        flask.abort(
            HTTPStatus.NOT_FOUND,
            f"Missing record for {request.page_num=} of {request.file_id=}",
        )

    page.status = FileProcessingState.PROCESSED
    page.processed_data = request.processed_data
    db.session.add(page)

    processed_pages_count = _get_processed_file_pages_count(request.file_id)
    if all_pages_processed := (processed_pages_count == page.total_pages):
        file = File.query.get(request.file_id)
        file.processing_state = FileProcessingState.PROCESSING
        db.session.add(file)

    try:
        db.session.commit()
    except Exception:
        logger.exception(
            "Could not save page processed data from Label Studio in database",
            file_id=request.file_id,
        )

    return {
        "page": FileLabelStudioSchema().dump(page),
        "processed_pages_count": processed_pages_count,
        "total_pages": page.total_pages,
        "all_pages_processed": all_pages_processed,
    }, HTTPStatus.OK


def _get_processed_file_pages_count(file_id: UUID) -> int:
    return FileLabelStudio.query.filter(
        FileLabelStudio.file_id == file_id,
        FileLabelStudio.status == FileProcessingState.PROCESSED,
    ).count()


def get_label_studio_file_page(file_id: str, page_num: int) -> tuple[dict | None, int]:
    page = FileLabelStudio.query.filter(
        FileLabelStudio.file_id == file_id,
        FileLabelStudio.page_num == page_num,
    ).first()

    if not page:
        flask.abort(HTTPStatus.NOT_FOUND, f"Missing record for {file_id=} and {page_num=}")

    return FileLabelStudioSchema().dump(page), HTTPStatus.OK


def get_label_studio_file_all_pages(file_id: str) -> tuple[dict | None, int]:
    pages = FileLabelStudio.query.filter(FileLabelStudio.file_id == file_id).all()
    if not pages:
        flask.abort(HTTPStatus.NOT_FOUND, f"Missing pages for for File {file_id}")

    return FileLabelStudioSchema().dump(pages, many=True), HTTPStatus.OK


def find_files_to_download(submission_id: UUID):
    # Subquery to find file ids with a file_type of raw
    subquery = (
        db.session.query(File.id)
        .filter(File.submission_id == submission_id)
        .filter(File.file_type == FileType.RAW_EMAIL.name)
        .subquery()
    )

    files = (
        db.session.query(File.s3_key)
        .filter(File.submission_id == submission_id)
        .filter(File.is_internal.is_(False))
        .filter(File.name != "email_body.pdf")
        .filter(
            # Parent file id is null or has raw email file type
            (File.parent_file_id.is_(None))
            | File.parent_file_id.in_(subquery)
        )
        .all()
    )
    return files


def download_files(submission_id: str | UUID) -> dict[str, str]:
    submission_id = convert_to_uuid(submission_id)
    submission = get_light_submission(submission_id)
    if not current_user.has_submission_permission(PermissionType.VIEWER, submission.id):
        flask.abort(403)
    files = find_files_to_download(submission_id)
    file_objs = [_download_file(key) for (key,) in files]

    archive = BytesIO()

    try:
        with ZipFile(archive, "w", ZIP_DEFLATED) as zip_archive:
            for file_obj in file_objs:
                filename = getattr(file_obj, "filename", None) or getattr(file_obj, "name")
                with zip_archive.open(filename, "w") as zip_file:
                    zip_file.write(file_obj.read())
        archive.seek(0)
        key = f"tmp/{submission_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.zip"
        upload_file(archive, key)
    finally:
        archive.close()

    url = flask.current_app.submission_s3_client.get_presigned_url(key=key, expiration=1800)

    return {"url": url}


def get_number_of_entities(fields: FirstPartyFields, field_type: FirstPartyFieldsGroupType) -> int:
    if not fields or not fields.fields_groups:
        return 0
    return sum(len(group.entities or []) for group in fields.fields_groups if group.type == field_type)


# noinspection PyUnusedLocal
def consolidate_acord_data(submission_id: str, body: dict | None = None) -> tuple[dict | None, int]:
    from copilot.logic.acords import consolidate_acord_data as cad

    submission = get_light_submission(submission_id)
    cad(submission, ers_client=current_app.ers_client_v3)
    return None, 204


def get_file_metrics(file_id: str, include_score_calculations: bool = False) -> tuple[dict | None, int]:
    File.query.get_or_404(file_id, description="The file with specified ID wasn't found")
    metrics = FileMetric.query.filter_by(file_id=file_id).all()
    exclude = []
    if not include_score_calculations:
        exclude.append("score_calculation")
    return FileMetricSchema(exclude=exclude).dump(metrics, many=True), HTTPStatus.OK


def put_file_metrics(file_id: str, body: list[dict]) -> tuple[dict | None, int]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    try:
        requested_metrics: list[FileMetric] = FileMetricSchema().load(body, many=True)
    except Exception as e:
        logger.exception("Failed to parse file metrics request", body=body, file_id=file_id)
        flask.abort(400, f"The payload is malformed - {e!s}")

    File.query.get_or_404(file_id, description="The file with specified ID wasn't found")

    if any(str(m.file_id) != file_id for m in requested_metrics):
        flask.abort(400, "All metrics in the request must have the same file_id")

    if duplicate_names := _duplicate_metric_names(requested_metrics):
        flask.abort(400, f"Multiple values found for following metrics: {duplicate_names}")

    put_file_metrics_internal(file_id, requested_metrics)

    return None, HTTPStatus.CREATED


def get_s3_file_url(report_id: str, s3_key: str, name: str | None = None, event_name: str | None = None) -> Any:
    report = get_report_or_404(report_id)

    if not current_user.has_report_permission(PermissionType.VIEWER, report_id):
        flask.abort(403)

    presigned_url = flask.current_app.submission_s3_client.generate_presigned_url_for_key(s3_key, filename=name)

    if not presigned_url:
        flask.abort(404, "File not found")

    if event_name:
        AmplitudeClient.track_basic_info(current_user, report_id, report.submission, event_name)

    return {"url": presigned_url}


def get_file_url_by_file_id(submission_id: str, file_id: str, event_name: str):
    submission = get_light_submission(submission_id)

    if not current_user.has_report_permission(PermissionType.VIEWER, submission.report_id):
        flask.abort(403)

    file = File.query.get_or_404(file_id, description="The file with specified ID wasn't found")

    presigned_url = flask.current_app.submission_s3_client.generate_presigned_url_for_key(
        file.s3_key, filename=file.name
    )

    if not presigned_url:
        flask.abort(404, "The file with specified ID wasn't found")
    AmplitudeClient.track_file_downloaded(current_user, submission, file, event_name)

    return {"url": presigned_url}


def download_submission_file_by_id(submission_id: str, file_id: str):
    return redirect(get_file_url_by_file_id(submission_id, file_id)["url"], code=301)


def download_file_by_s3_key(report_id: str, s3_key: str, name: str | None = None, event_name: str | None = None) -> Any:
    return redirect(get_s3_file_url(report_id, s3_key, name, event_name)["url"], code=301)


def _duplicate_metric_names(metrics: list[FileMetric]) -> list[str]:
    metric_names = [m.metric_name for m in metrics]
    name_counter = Counter(metric_names)
    return [k for k, v in name_counter.items() if v > 1]


def get_files_for_external_consumers(report_id: str) -> tuple[dict, int]:
    """
    This endpoint should be used by Kalepa external clients (m2m) to get the files of a report.
    The files returned are only the ones that are not internal and with details that are safe to be
    shared with external clients.
    """
    if not current_user.is_machine_user:
        flask.abort(403)

    report = get_report_or_404(report_id)
    if current_user.organization_id != report.organization_id:
        flask.abort(403)

    files = db.session.query(File).filter(File.submission_id == report.submission.id).all()
    filtered_files = []
    for file in files:
        if file.is_internal:
            continue
        filtered_files.append(file)

    return (
        file_for_external_consumer_schema.dump(filtered_files, many=True),
        HTTPStatus.OK,
    )


def split_pdf_file(file_id: UUID, body: dict) -> tuple[dict, int]:
    file = File.query.get_or_404(file_id, description="The file with specified ID wasn't found")
    submission = get_light_submission(file.submission_id)
    if not current_user.has_submission_permission(PermissionType.EDITOR, submission.id):
        flask.abort(403)

    if not file.name.lower().endswith(".pdf") or file.file_type != FileType.UNKNOWN:
        flask.abort(400, "Only unknown PDF files can be split")

    if db.session.query(db.exists().where(File.parent_file_id == file.id)).scalar():
        flask.abort(400, "File already split")

    file_content = current_app.submission_s3_client.get_file_as_bytes(file.s3_key)

    pdf_splitter = PDFSplitter(file_content)

    split_request = SplitFileRequestSchema().load(body)
    page_ranges = {(page_range.start, page_range.end): page_range for page_range in split_request.page_ranges}

    pdf_splitter_page_ranges = list(page_ranges.keys())

    if not pdf_splitter.page_count:
        flask.abort(400, "Can't split invalid file")

    if not pdf_splitter.validate_page_ranges(pdf_splitter_page_ranges):
        flask.abort(400, "Invalid page ranges")

    split_files = pdf_splitter.split_pdf_with_gaps(pdf_splitter_page_ranges)
    created_files = []
    for page_range, file_content in split_files.items():
        parent_filename, parent_extension = os.path.splitext(file.name)
        split_file_metadata = page_ranges.get(page_range)
        file_name_type = (
            f"_{split_file_metadata.file_type}"
            if split_file_metadata and split_file_metadata.file_type != FileType.UNKNOWN
            else ""
        )
        file_name_page_range = f"_{page_range[0] + 1:02d}_{page_range[1] + 1:02d}"
        file_name = f"{parent_filename}{file_name_type}{file_name_page_range}{parent_extension}"

        user_file_type = (
            split_file_metadata.file_type
            if split_file_metadata and split_file_metadata.file_type != FileType.UNKNOWN
            else None
        )
        split_file = File(
            submission_id=submission.id,
            user_file_type=user_file_type,
            custom_file_type_id=split_file_metadata.custom_file_type_id if split_file_metadata else None,
            is_internal=False,
            parent_file_id=file.id,
            origin=file.origin,
            internal_notes=[f"File generated by splitting file {file.id} by the user."],
        )
        file_object = FileStorage(BytesIO(file_content), file_name)
        created_files.append(create_file_logic(file_object, split_file))

    file.file_type = FileType.MERGED
    file.classification = ClassificationDocumentType.MERGED.value
    file.processing_state = FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING
    db.session.commit()

    return {"files": file_schema_convert_additional_info.dump(created_files, many=True)}, 201
