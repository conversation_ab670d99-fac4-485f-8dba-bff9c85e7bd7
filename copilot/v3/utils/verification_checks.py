from collections import namedtuple
from collections.abc import Callable, Sequence
from dataclasses import dataclass
import json
import os

from common.logic.entity_resolution.entity import get_all_premises_ids
from entity_resolution_service_client_v3 import Entity
from facts_client.model.facts_count import FactsCount
from flask import g
from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy import func, or_
from sqlalchemy.engine.row import Row
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.parent import ParentType
from static_common.enums.source_types import SourceTypeID
from static_common.enums.submission import SubmissionMode
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
import flask

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.constants import (
    ON_BEHALF_OF_FALSE_POSITIVES,
    PHRASES_INDICATING_CORR_CONTACT,
)
from copilot.models import Loss, db
from copilot.models.files import SubmissionFilesData
from copilot.models.organization import Organization
from copilot.models.policy import default_loss_grouping
from copilot.models.reports import Submission
from copilot.models.types import CoverageType, VerificationCheckStages
from copilot.models.verification_check import ResultStatus, VerificationCheckResult
from copilot.schemas.verification_check import VerificationCheckResultSchema
from copilot.v3.utils.db_session import no_expire_on_commit
from copilot.v3.utils.submission import (
    check_contractor_experience_exists,
    check_project_exposure_exists,
    check_submission_permits_exists,
    check_submissions_vehicles_exists,
)

logger = get_logger()

MIN_FACT_COUNT = 10
SUPPORT_USERS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
]
TRANSPORT_WORDS_FOR_FMCSA_CHECK = ["trucking", "transport", "logistic"]
REQUIRED_FACTS_GC = [
    FactSubtypeID.PAYROLL,
    FactSubtypeID.TOTAL_SALES,
]
REQUIRED_PROPERTY_FACTS = [
    FactSubtypeID.BUILDING_VALUE,
    FactSubtypeID.TIV,
]

verification_result_schema = VerificationCheckResultSchema(
    many=True, only=["name", "status", "error_message", "is_hard_check"]
)
RunParams = namedtuple("RunParams", "submission_id, stage, manual, force_manual, force_verify, run_id")

verification_stage_map = {
    VerificationCheckStages.ENTITY_MAPPING: SubmissionFilesData.entity_mapped_data,
    VerificationCheckStages.DATA_ONBOARDING: SubmissionFilesData.onboarded_data,
    VerificationCheckStages.COMPLETED: SubmissionFilesData.loaded_data,
}


def is_do_validation_enabled():
    if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True and FeatureFlagsClient.is_feature_enabled(
        FeatureType.ENABLE_VALIDATION_DO
    ):
        return True
    return False


@dataclass
class VerificationCheck:
    name: str
    function: Callable
    dependent_checks: list["VerificationCheck"] | None
    is_hard_check: bool | None = False
    is_hard_check_for_non_managers: bool | None = False
    is_hard_check_for_non_tier_2: bool | None = False
    is_quiet: bool | None = False
    is_quiet_for_non_managers: bool | None = False
    applicable_stages: list[VerificationCheckStages] | None = None


@dataclass
class CheckResult:
    success: bool
    error_message: str | None = None
    skipped: bool | None = False


def _get_entities_for_submission(submission: Submission) -> dict[str, Entity]:
    if "entities_for_submission" in g:
        return g.entities_for_submission
    businesses_ids = list({str(sb.business_id) for sb in submission.businesses if sb.business_id})
    entities_dict = flask.current_app.ers_client_v3.get_entities(entity_ids=businesses_ids)
    g.entities_for_submission = entities_dict
    return entities_dict


def _fetch_facts_counts(submission: Submission) -> dict[str, FactsCount]:
    if "facts_counts" in g:
        return g.facts_counts
    if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is not True:
        return {}
    business_ids = {str(sb.business_id) for sb in submission.businesses if sb.business_id}
    counts = flask.current_app.facts_client.count_facts(
        parent_ids=list(business_ids),
        parent_type=ParentType.BUSINESS.value,
        organization_id=submission.organization_id,
    )
    result = {c.parent_id: c for c in counts}
    g.facts_counts = result
    return result


def _fetch_facts_counts_first_party(submission: Submission) -> dict[str, FactsCount]:
    if "facts_counts_first_party" in g:
        return g.facts_counts_first_party
    if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is not True:
        return {}
    business_ids = {str(sb.business_id) for sb in submission.businesses if sb.business_id}
    counts = flask.current_app.facts_client.count_facts(
        parent_ids=list(business_ids),
        parent_type=ParentType.BUSINESS.value,
        organization_id=submission.organization_id,
        source_type_id=SourceTypeID.FIRST_PARTY.value,
    )
    result = {c.parent_id: c for c in counts}
    g.facts_counts_first_party = result
    return result


def _fetch_fact_counts_for_premises_and_structures(
    submission: Submission, fact_subtype_ids: Sequence[str]
) -> dict[str, FactsCount]:
    if "premises_structures_counts" in g:
        return g.premises_structures_counts
    entitites = _get_entities_for_submission(submission)
    premises_ids = set()
    for entity in entitites.values():
        premises_ids.update(get_all_premises_ids(entity))
    counts = flask.current_app.facts_client.count_facts(
        parent_ids=list(premises_ids),
        parent_type=ParentType.BUSINESS.value,
        organization_id=submission.organization_id,
        fact_subtype_ids=fact_subtype_ids,
        include_relationships=True,
    )
    result = {c.parent_id: c for c in counts}
    g.premises_structures_counts = result
    return result


def _check_existing_businesses(submission: Submission) -> CheckResult:
    success = any(submission.businesses)
    error_message = "Submission is missing businesses" if not success else None
    return CheckResult(success, error_message)


def _check_unconfirmed_businesses(submission: Submission) -> CheckResult:
    success = not submission.has_unconfirmed_businesses()
    error_message = None
    if not success:
        unconfirmed_businesses = len([b for b in submission.businesses if b.business_id is None])
        error_message = (
            f"Submission has {unconfirmed_businesses} unconfirmed business. "
            "First party facts will be populated once all businesses are confirmed."
        )
    return CheckResult(success, error_message)


def _check_assigned_underwriters(submission: Submission) -> CheckResult:
    success = any(
        submission_user.user.email not in SUPPORT_USERS for submission_user in submission.assigned_underwriters
    )
    error_message = "Submission has not assigned underwriters"
    return CheckResult(success, error_message)


def _check_submission_naics(submission: Submission) -> CheckResult:
    if not submission.primary_naics_code:
        return CheckResult(False, "Submission has no primary naics code")
    return CheckResult(True)


def _check_submission_gl_iso_code(submission: Submission) -> CheckResult:
    if submission.report.organization_id != ExistingOrganizations.SECURA.value:
        return CheckResult(True, error_message="GL ISO only required for SECURA", skipped=True)
    if not submission.iso_gl_code:
        return CheckResult(False, "Submission does not have ISO GL code")
    return CheckResult(True)


def _check_missing_fmcsa_ingestion(submission: Submission) -> CheckResult:
    has_vehicles_or_drivers_file = any(x.file_type in [FileType.VEHICLES, FileType.DRIVERS] for x in submission.files)
    if not has_vehicles_or_drivers_file:
        return CheckResult(True)

    report_name = submission.report.name or ""
    businesses_ids_to_check = set()
    if any(transport_word in submission.name.lower() for transport_word in TRANSPORT_WORDS_FOR_FMCSA_CHECK) or any(
        transport_word in report_name.lower() for transport_word in TRANSPORT_WORDS_FOR_FMCSA_CHECK
    ):
        businesses_ids_to_check.update([b.business_id for b in submission.businesses])
    else:
        for submission_business in submission.businesses:
            if submission_business.requested_name:
                requested_name = submission_business.requested_name.lower()
                if any(transport_word in requested_name for transport_word in TRANSPORT_WORDS_FOR_FMCSA_CHECK):
                    businesses_ids_to_check.add(submission_business.business_id)

    if not businesses_ids_to_check:
        return CheckResult(True)

    try:
        all_entities = {str(sb.business_id) for sb in submission.businesses if sb.business_id}
        all_scrapers = flask.current_app.lambda_client.invoke_select_scrapers_bulk_lambda(list(all_entities))
        missing_fmcsa_ingestion = True
        for business_id in businesses_ids_to_check:
            selected_scrapers = all_scrapers.get(business_id)
            if not selected_scrapers:
                logger.warning(
                    "Missing selected scrapers for related entity",
                    business_id=business_id,
                    all_entities=all_entities,
                    all_scrapers=all_scrapers,
                )
                continue
            if selected_scrapers["scrape_fmcsa"] or selected_scrapers["scrape_safer_fmcsa"]:
                missing_fmcsa_ingestion = False
                break
        if missing_fmcsa_ingestion:
            return CheckResult(
                False,
                (
                    "Submission did not ingested data from FMCSA although based on the files "
                    "and words it looks that there should be FMCSA record"
                ),
            )
    except Exception:
        logger.exception("Failed to check FMCSA ingestion", submission_id=submission.id)
        raise

    return CheckResult(True)


def _check_missing_vehicle_ingestion(submission: Submission) -> CheckResult:
    naics_code = submission.primary_naics_code
    if not naics_code:
        message = "NAICS Code is missing in Submission"
        logger.warning(message, submission_id=str(submission.id))
        return CheckResult(True, error_message=message, skipped=True)
    if not submission.is_transportation_submission:
        return CheckResult(True)
    if check_submissions_vehicles_exists(submission):
        return CheckResult(True)
    return CheckResult(
        False,
        f"Submission does not have vehicles although there are NAICS codes (48 or 49). Affected NAICS: {naics_code}",
    )


def _check_broken_buildzoom_ingestion(submission: Submission) -> CheckResult:
    naics_code = submission.primary_naics_code
    if not naics_code:
        message = "NAICS Code is missing in Submission"
        logger.warning(message, submission_id=str(submission.id))
        return CheckResult(True, error_message=message, skipped=True)
    construction_submission = submission.is_construction_submission or submission.mode in [
        SubmissionMode.GC_PROJECT,
        SubmissionMode.PRACTICE,
    ]
    if not construction_submission:
        return CheckResult(True)

    try:
        missing_buildzoom_profile = True
        entities = {str(sb.business_id) for sb in submission.businesses if sb.business_id}
        all_scrapers = flask.current_app.lambda_client.invoke_select_scrapers_bulk_lambda(list(entities))
        for entity_id in entities:
            selected_scrapers = all_scrapers.get(entity_id)
            if not selected_scrapers:
                logger.warning(
                    "Missing selected scrapers for entity",
                    entity_id=entity_id,
                    all_scrapers=all_scrapers,
                )
                continue
            if selected_scrapers["scrape_buildzoom"]:
                missing_buildzoom_profile = False
                break

        if missing_buildzoom_profile:
            return CheckResult(True)

        if check_submission_permits_exists(submission):
            return CheckResult(True)
    except Exception:
        logger.exception("Failed to check Buildzoom ingestion", submission_id=submission.id)
        raise

    return CheckResult(False, "Submission does not have permits although there is a buildzoom profile")


def _check_broker_brokerage(submission: Submission) -> CheckResult:
    condition = not submission.brokerage_contact_id or not submission.broker_id or not submission.brokerage_id

    if condition:
        return CheckResult(
            False,
            (
                "Correspondence Contact/Brokerage Contact or Broker/agent or brokerage/agency is missing. "
                "Proceed only if you are 100% confident this is correct."
            ),
        )
    return CheckResult(True)


def _check_contractor_first_party_facts(submission: Submission) -> CheckResult:
    if submission.mode == SubmissionMode.GC_PROJECT:
        if not check_project_exposure_exists(submission):
            return CheckResult(
                False, "Submission has a Project mode, but there are no first party observations for project exposures"
            )
    elif submission.mode == SubmissionMode.PRACTICE:
        if not check_contractor_experience_exists(submission):
            return CheckResult(
                False,
                "Submission has a Practice mode, but there are no first party observations for contractor experience",
            )
    return CheckResult(True)


def _check_no_files_ingested(submission: Submission) -> CheckResult:
    success = any(file.file_type != FileType.EMAIL for file in submission.files if file.file_type)
    error_message = "Submission is missing ingested files" if not success else None
    return CheckResult(success, error_message)


def _check_sov_ingestion(submission: Submission) -> CheckResult:
    first_party_data_file_prefix = f"first-party-data-fields/{submission.id}"
    if not flask.current_app.submission_s3_client.check_if_file_with_prefix_exists(
        file_prefix=first_party_data_file_prefix
    ):
        return CheckResult(False, "Submission has not loaded SOV file")
    return CheckResult(True)


def _check_gc_project(submission: Submission) -> CheckResult:
    if not submission.primary_naics_code:
        return CheckResult(True, error_message="NAICS Code is missing in Submission", skipped=True)
    if not submission.is_construction_submission:
        return CheckResult(True)
    has_exactly_one_coverage = len(submission.coverages) == 1
    if has_exactly_one_coverage:
        if submission.coverages[0].coverage.name == "property":
            return CheckResult(True)
    for sb in submission.businesses:
        if (
            sb.entity_role == SubmissionBusinessEntityRole.GENERAL_CONTRACTOR
            or sb.entity_role == SubmissionBusinessEntityRole.PROJECT
        ):
            return CheckResult(True)
    return CheckResult(False, "Construction (NAICS=23) submission does not have GC/Project set")


def _check_no_gc_or_project_for_non_construction(submission: Submission) -> CheckResult:
    from copilot.logic.pds.gc import ALLOWED_NAICS_CODES

    if not submission.primary_naics_code:
        return CheckResult(True, error_message="NAICS Code is missing in Submission", skipped=True)
    if submission.has_gc_or_project and submission.primary_naics_code not in ALLOWED_NAICS_CODES:
        return CheckResult(
            False,
            (
                "Construction entities detected - please remove entity roles if non-construction, adjust NAICS if"
                " actually construction"
            ),
        )
    return CheckResult(True)


def _check_has_at_least_one_fni(submission: Submission) -> CheckResult:
    num_of_fnis = sum(
        1
        for sb in submission.businesses
        if sb.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
    )
    if num_of_fnis == 0:
        return CheckResult(False, "Submission has no First named insured set!")
    return CheckResult(True)


def _check_has_more_than_one_fni(submission: Submission) -> CheckResult:
    num_of_fnis = sum(
        1
        for sb in submission.businesses
        if sb.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
    )
    if num_of_fnis > 1:
        return CheckResult(False, "Submission has more than one First named insured set!")
    return CheckResult(True)


def _check_news_and_lawsuits(submission: Submission) -> CheckResult:
    grouped_loss_runs = (
        db.session.query(func.sum(Loss.sum_of_total_net_incurred))
        .filter(Loss.submission_id == submission.id)
        .group_by(*default_loss_grouping)
    ).all()
    largest_loss = max([lr[0] for lr in grouped_loss_runs]) if grouped_loss_runs else None
    if not largest_loss or largest_loss < 100 * 1000:
        return CheckResult(True, skipped=True)
    businesses_ids = list({str(sb.business_id) for sb in submission.businesses if sb.business_id})
    facts_client = flask.current_app.facts_client
    legal_filling_counts = facts_client.get_document_counts(
        businesses_ids, ParentType.BUSINESS, DocumentTypeID.LEGAL_FILING
    )
    if any(x.count for x in legal_filling_counts):
        return CheckResult(True)
    news_counts = facts_client.get_document_counts(businesses_ids, ParentType.BUSINESS, DocumentTypeID.NEWS)
    if any(x.count for x in news_counts):
        return CheckResult(True)
    return CheckResult(False, "No news/lawsuits despite significant losses")


def _has_coverages(submission: Submission) -> CheckResult:
    if len(submission.coverages) > 0:
        return CheckResult(True)
    return CheckResult(False, "Submission has no coverages assigned")


def _has_limit_and_attachment_point_for_excess(submission: Submission) -> CheckResult:
    excess_coverages = [coverage for coverage in submission.coverages if coverage.coverage_type == CoverageType.EXCESS]
    for coverage in excess_coverages:
        if not coverage.limit or not coverage.attachment_point:
            return CheckResult(
                False,
                (
                    f"One of excess coverages ({coverage.coverage.display_name}) does not specify limit"
                    " or attachment point"
                ),
            )
    return CheckResult(True)


def _has_property_information(submission: Submission) -> CheckResult:
    if not any(sc.coverage.name in {"property", "garageDealers"} for sc in submission.coverages or []):
        return CheckResult(True, skipped=True)
    facts_counts = _fetch_fact_counts_for_premises_and_structures(submission, REQUIRED_PROPERTY_FACTS)
    found_subtypes = set()
    for fc in facts_counts.values():
        found_subtypes.update(fc.fact_subtype_ids)
    if len(found_subtypes) == len(REQUIRED_PROPERTY_FACTS):
        return CheckResult(True)
    return CheckResult(
        False,
        f"{[r.value for r in REQUIRED_PROPERTY_FACTS]} are required when coverage is property, found {found_subtypes}",
    )


def _check_entity_with_most_facts(submission: Submission) -> CheckResult:
    project_or_gc = submission.projects or submission.general_contractors
    if not project_or_gc or not project_or_gc[0].business_id:
        return CheckResult(True, skipped=True)
    all_counts = _fetch_facts_counts_first_party(submission)
    max_counts = 0
    entity_ids_for_max = set()
    for entity_id, counts in all_counts.items():
        if counts.count > max_counts:
            max_counts = counts.count
            entity_ids_for_max = {entity_id}
        if counts.count == max_counts:
            entity_ids_for_max.add(entity_id)
    sb_with_roles = [sb for sb in submission.businesses if str(sb.business_id) in entity_ids_for_max and sb.entity_role]
    if sb_with_roles:
        return CheckResult(True)
    sb_with_max = [sb for sb in submission.businesses if str(sb.business_id) in entity_ids_for_max][0]
    return CheckResult(
        False,
        f"""The entity ({sb_with_max.requested_name} @ {sb_with_max.requested_address}) has not been assigned a role
            despite having the most data points mapped to it. Please double check to confirm this is correct.""",
    )


def _check_data_from_supplementals(submission: Submission) -> CheckResult:
    gc = submission.general_contractors
    if submission.projects or not gc or not gc[0].business_id:
        return CheckResult(True, skipped=True)
    entity_type = "GENERAL CONTRACTOR"
    required_fact_subtypes = REQUIRED_FACTS_GC
    entity_id = str(gc[0].business_id)
    entity_fact_count = _fetch_facts_counts(submission)[entity_id]
    missing_fact_subtypes = []
    existing_fact_subtypes = set(entity_fact_count.fact_subtype_ids)
    for fs in required_fact_subtypes:
        if fs not in existing_fact_subtypes:
            missing_fact_subtypes.append(fs.value)
    if missing_fact_subtypes:
        return CheckResult(False, f"Facts {missing_fact_subtypes} are missing for {entity_type}")
    return CheckResult(True)


def _check_correspondence_contact_or_broker_email(submission: Submission) -> CheckResult:
    is_on_behalf_of = False
    broker = submission.broker
    brokerage_contact = submission.brokerage_contact
    email_body = submission.report.email_body or ""
    for on_behalf_false_positive in ON_BEHALF_OF_FALSE_POSITIVES:
        email_body = email_body.lower().replace(on_behalf_false_positive, " ")

    for phrase in PHRASES_INDICATING_CORR_CONTACT:
        if phrase in email_body:
            is_on_behalf_of = True
            break

    if not is_on_behalf_of and brokerage_contact.email != broker.email:
        return CheckResult(False, "Expecting Correspondence Contact email to be the same as Broker email")

    if is_on_behalf_of and brokerage_contact.email == broker.email:
        return CheckResult(False, "Expecting different emails for Correspondence Contact and Broker")

    return CheckResult(True)


def _check_there_is_no_loss_more_than_25_million(submission: Submission) -> CheckResult:
    TOTAL_NET_INCURRED_THRESHOLD = 25_000_000
    losses_above_threshold = (
        db.session.query(Loss)
        .filter(Loss.submission_id == submission.id)
        .filter(Loss.sum_of_total_net_incurred > TOTAL_NET_INCURRED_THRESHOLD)
        .all()
    )
    if losses_above_threshold:
        return CheckResult(
            False,
            f"There are {len(losses_above_threshold)} losses above {TOTAL_NET_INCURRED_THRESHOLD} total net incurred.",
        )
    return CheckResult(True)


def _expand_and_cleanup_values(values: list[Row]) -> list[float]:
    expanded_values = []
    for row in values:
        value = row[0]
        try:
            if "values" in value:
                expanded_values.extend(json.loads(value)["values"])
            elif value:
                expanded_values.append(json.loads(value))
        except Exception as e:
            logger.info("Failed to expand and cleanup values", error=str(e), value=value)
    return expanded_values


def _check_fact_over_threshold(
    fact_subtype_id: FactSubtypeID,
    threshold: float,
    submission: Submission,
) -> CheckResult:
    verification_stage = VerificationCheckStages.try_parse_str(submission.processing_state.value)
    if not verification_stage:
        return CheckResult(success=True, skipped=True)

    fields_query = (
        db.session.query(
            func.jsonb_array_elements(verification_stage_map[verification_stage]["fields"]).label("fields")
        )
        .filter(SubmissionFilesData.submission_id == submission.id)
        .subquery()
    )
    values = (
        db.session.query(func.jsonb_array_elements(fields_query.c.fields.op("->")("values")).op("->")("value"))
        .filter(fields_query.c.fields.op("->>")("fact_subtype_id") == fact_subtype_id.value)
        .all()
    )
    if expanded_values := _expand_and_cleanup_values(values):
        values_over_threshold = []
        for v in expanded_values:
            try:
                if v is not None and float(v) > threshold:
                    values_over_threshold.append(v)
            except (TypeError, ValueError):
                return CheckResult(
                    success=False,
                    skipped=False,
                    error_message=f"Invalid value was found; value={v}, fact={fact_subtype_id.value}",
                )
        if values_over_threshold:
            return CheckResult(
                False,
                (
                    f"There are observations {[str(v) for v in values_over_threshold]} of {fact_subtype_id.value} which"
                    f" is over the threshold of {threshold}."
                ),
            )
    return CheckResult(True)


def _check_total_sales_over_1_billion(submission):
    return _check_fact_over_threshold(FactSubtypeID.TOTAL_SALES, 1000000000, submission)


def _check_total_sales_over_100_billion(submission):
    return _check_fact_over_threshold(FactSubtypeID.TOTAL_SALES, 100_000_000_000, submission)


def _check_payroll_over_500_million(submission):
    return _check_fact_over_threshold(FactSubtypeID.PAYROLL, 500000000, submission)


def _check_payroll_over_50_billion(submission):
    return _check_fact_over_threshold(FactSubtypeID.PAYROLL, 50_000_000_000, submission)


def _check_building_area_over_10_million(submission):
    return _check_fact_over_threshold(FactSubtypeID.BUILDING_SIZE, 10000000, submission)


def _check_building_area_over_1_billion(submission):
    return _check_fact_over_threshold(FactSubtypeID.BUILDING_SIZE, 1_000_000_000, submission)


def _check_number_of_units_over_10k(submission):
    return _check_fact_over_threshold(FactSubtypeID.NUMBER_OF_UNITS, 10000, submission)


def _check_number_of_units_over_1_million(submission):
    return _check_fact_over_threshold(FactSubtypeID.NUMBER_OF_UNITS, 1_000_000, submission)


def _check_org_group_is_set(submission: Submission) -> CheckResult:
    if submission.organization_id != ExistingOrganizations.BishopConifer.value:
        return CheckResult(True)
    if submission.report.org_group:
        return CheckResult(True)
    return CheckResult(False, "Submission has no org group set")


def _check_aru_property_description_or_occupancy(submission: Submission) -> CheckResult:
    if not Organization.is_aru_for_id(submission.organization_id):
        return CheckResult(True)
    fields_query = (
        db.session.query(func.jsonb_array_elements(SubmissionFilesData.onboarded_data["fields"]).label("fields"))
        .filter(SubmissionFilesData.submission_id == submission.id)
        .subquery()
    )
    values = (
        db.session.query(func.jsonb_array_elements(fields_query.c.fields.op("->")("values")).op("->")("value"))
        .filter(
            or_(
                fields_query.c.fields.op("->>")("fact_subtype_id") == FactSubtypeID.PROPERTY_DESCRIPTION.value,
                fields_query.c.fields.op("->>")("fact_subtype_id") == FactSubtypeID.OCCUPANCY.value,
            )
        )
        .all()
    )
    if values:
        return CheckResult(True)
    return CheckResult(False, "Submission has neither Property Description nor Occupancy set")


def _check_secura_sub_has_sic_code(submission: Submission) -> CheckResult:
    if Organization.is_secura_for_id(submission.organization_id) and not submission.sic_code:
        return CheckResult(False, "Submission has no SIC code set")
    return CheckResult(True)


# Dependent checks
FILE_INGESTED_DEPENDENT_CHECK = [
    VerificationCheck(
        "Missing SOV (first party data)",
        _check_sov_ingestion,
        [],
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
]

EXISTING_AND_CONFIRMED_BIZ_DEP = [
    VerificationCheck(
        "FMCSA Ingestion (if needed)",
        _check_missing_fmcsa_ingestion,
        [],
        is_quiet=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Vehicles Populated (if needed)",
        _check_missing_vehicle_ingestion,
        [],
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Contractor missing first-party exposures (if needed)",
        _check_contractor_first_party_facts,
        [],
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
]

BROKER_BROKERAGE_DEP = [
    VerificationCheck(
        "Broker email or Correspondence Contact",
        _check_correspondence_contact_or_broker_email,
        [],
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
]

# Main checks
VERIFICATION_CHECKS = [
    VerificationCheck(
        "Existing Businesses",
        _check_existing_businesses,
        [
            VerificationCheck(
                "Confirmed Businesses",
                _check_unconfirmed_businesses,
                EXISTING_AND_CONFIRMED_BIZ_DEP,
                is_hard_check=True,
                applicable_stages=[VerificationCheckStages.COMPLETED],
            )
        ],
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "No news/lawsuits despite significant losses",
        _check_news_and_lawsuits,
        [],
        is_quiet=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Assigned Underwriter(s)",
        _check_assigned_underwriters,
        [],
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Correspondence Contact, Broker and Brokerage Populated",
        _check_broker_brokerage,
        BROKER_BROKERAGE_DEP,
        is_hard_check_for_non_tier_2=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Has Coverages",
        _has_coverages,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Has Excess coverages limit and attachment point specified",
        _has_limit_and_attachment_point_for_excess,
        [],
        is_quiet_for_non_managers=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Has Property Information",
        _has_property_information,
        [],
        is_quiet=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Construction: Entity with most facts not assigned a role",
        _check_entity_with_most_facts,
        [],
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Construction Practice Policy: Total Sales or Payroll Missing",
        _check_data_from_supplementals,
        [],
        is_quiet=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Submission Level NAICS Populated",
        _check_submission_naics,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Submission Level GL ISO Code",
        _check_submission_gl_iso_code,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "GC or Project exist for construction submissions",
        _check_gc_project,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Construction entity assigned to non-construction NAICS",
        _check_no_gc_or_project_for_non_construction,
        [],
        is_hard_check_for_non_tier_2=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Files Ingested",
        _check_no_files_ingested,
        FILE_INGESTED_DEPENDENT_CHECK,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Submission does not have permits although there is a buildzoom profile",
        _check_broken_buildzoom_ingestion,
        [],
        is_quiet=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Total Net Incurred is not more than 25 million",
        _check_there_is_no_loss_more_than_25_million,
        [],
        is_hard_check_for_non_tier_2=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Total Sales over 1 billion",
        _check_total_sales_over_1_billion,
        [],
        is_hard_check_for_non_tier_2=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING, VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Total Sales over 100 billion",
        _check_total_sales_over_100_billion,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING, VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Payroll over 500 million",
        _check_payroll_over_500_million,
        [],
        is_hard_check_for_non_tier_2=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING, VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Payroll over 50 billion",
        _check_payroll_over_50_billion,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING, VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Building Area over 10 million",
        _check_building_area_over_10_million,
        [],
        is_hard_check_for_non_tier_2=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING, VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Building Area over 1 billion",
        _check_building_area_over_1_billion,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING, VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Number of Units over 10 thousands",
        _check_number_of_units_over_10k,
        [],
        is_hard_check_for_non_tier_2=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING, VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Number of Units over 1 million",
        _check_number_of_units_over_1_million,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING, VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "At least one FNI is set",
        _check_has_at_least_one_fni,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Do not have more than one FNI set",
        _check_has_more_than_one_fni,
        [],
        is_hard_check_for_non_managers=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "Org group is set",
        _check_org_group_is_set,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
    VerificationCheck(
        "ARU: Ensure We Have Either Property Description or Occupancy",
        _check_aru_property_description_or_occupancy,
        [],
        is_hard_check_for_non_tier_2=True,
        applicable_stages=[VerificationCheckStages.DATA_ONBOARDING] if is_do_validation_enabled() else [],
    ),
    VerificationCheck(
        "Secura: Ensure submission has SIC assigned",
        _check_secura_sub_has_sic_code,
        [],
        is_hard_check=True,
        applicable_stages=[VerificationCheckStages.COMPLETED],
    ),
]


def verify_checks(
    submission: Submission,
    run_params: RunParams,
    skip: bool = False,
) -> tuple[list[VerificationCheckResult], bool, bool, list]:
    with no_expire_on_commit():
        verification_checks = [vc for vc in VERIFICATION_CHECKS if run_params.stage in vc.applicable_stages]
        result = _run_checks(submission, verification_checks, run_params, skip)
        db.session.commit()
        output = verification_result_schema.dump(result[0])
        return (result[0], result[1], result[2], output)


def _run_checks(
    submission: Submission,
    verification_checks: list[VerificationCheck],
    run_params: RunParams,
    skip: bool = False,
) -> tuple[list[VerificationCheckResult], bool, bool]:
    verify_schema = VerificationCheckResultSchema()
    result = []
    all_passed = True
    hard_checks_failed = False
    for verification_check in verification_checks:
        data = run_params._asdict()
        data["name"] = verification_check.name
        skip_for_check = skip
        is_quiet = verification_check.is_quiet or (
            verification_check.is_quiet_for_non_managers and not current_user.is_cs_manager_or_internal_machine_user
        )

        if skip_for_check:
            data.update({"status": ResultStatus.SKIPPED})
        else:
            try:
                check_result = verification_check.function(submission)
                if check_result.success:
                    if not check_result.skipped:
                        data.update({"status": ResultStatus.SUCCESS})
                    else:
                        data.update({"status": ResultStatus.SKIPPED, "error_message": check_result.error_message})
                        skip_for_check = True
                else:
                    data.update({"status": ResultStatus.ERROR, "error_message": check_result.error_message})
                    if not is_quiet:
                        skip_for_check = True
                        all_passed = False
                        if verification_check.is_hard_check:
                            logger.info("Hard check failed!", name=verification_check.name, submission=submission)
                            hard_checks_failed = True
                        if (
                            verification_check.is_hard_check_for_non_managers
                            and not current_user.is_cs_manager_or_internal_machine_user
                        ):
                            hard_checks_failed = True
                        if (
                            verification_check.is_hard_check_for_non_tier_2
                            and not current_user.is_tier_2_or_internal_machine_user
                        ):
                            hard_checks_failed = True
            except:
                db.session.rollback()
                logger.exception("Error in verification check", name=verification_check.name, submission=submission)
                data.update({"status": ResultStatus.SKIPPED, "error_message": "Exception during running verification"})
                skip_for_check = True

        check: VerificationCheckResult = verify_schema.load(data)
        check.is_hard_check = verification_check.is_hard_check
        db.session.add(check)
        if not is_quiet:
            result.append(check)
        if verification_check.dependent_checks:
            result_dep_check, all_passed_dep_check, hard_checks_failed_dep_check = _run_checks(
                submission, verification_check.dependent_checks, skip=skip_for_check, run_params=run_params
            )
            result.extend(result_dep_check)
            all_passed = all_passed and all_passed_dep_check
            hard_checks_failed = hard_checks_failed or hard_checks_failed_dep_check
    return result, all_passed, hard_checks_failed
