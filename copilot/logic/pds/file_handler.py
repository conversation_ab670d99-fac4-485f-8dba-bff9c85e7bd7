from collections import defaultdict
from datetime import datetime
from uuid import UUID, uuid4
import json
import os

from flask import current_app
from infrastructure_common.logging import get_logger
from sqlalchemy.orm import load_only
from sqlalchemy.orm.attributes import flag_modified
from static_common.constants import ORGS_TO_PROCESS_FINANCIAL_STATEMENTS
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.fields import FieldType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.origin import Origin
from static_common.enums.step_function import StepFunctionStatus
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.models.business_resolution_data import BusinessResolutionData
from static_common.models.file_onboarding import OnboardedFile, ResolvedDataField
from static_common.schemas.business_resolution_data import BusinessResolutionDataSchema
from static_common.schemas.file_onboarding import (
    LeanOnboardedFileSchema,
    LeanResolvedDataFieldSchema,
)
from static_common.utils.document_type_mappings import (
    detect_document_type,
    get_file_document_suffix,
)
import flask
import redis_lock

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.constants import (
    SHADOW_CREATION_CUT_OFF_TIME,
    STUCK_FOR_ENGINEERING_SLACK_CHANNEL_ID,
    SUBMISSION_STATE_TO_FILE_STATE,
)
from copilot.kalepa_domain_events.kalepa_events_handler import KalepaEventsHandler
from copilot.logic.bishop_conifer import (
    has_submission_only_cancellable_policy_file,
    has_submission_only_quote_files,
)
from copilot.logic.dao.file_dao import FileDAO
from copilot.logic.dao.processed_file_dao import ProcessedFileDAO
from copilot.logic.dao.report_dao import ReportDAO
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.onboarded_files_transformation import (
    get_or_create_entity_field_by_name,
    get_or_create_value_for_entity,
)
from copilot.logic.pds.data_consolidation import (
    FinancialStatementConsolidator,
    consolidate_acords_and_em_files,
    consolidate_submission_level_data,
    get_consolidation_lock_name,
)
from copilot.logic.pds.data_consolidation_process import (
    handle_existing_consolidation_process,
    maybe_finish_consolidation_process,
    maybe_finish_enhance_shell_processing,
)
from copilot.logic.pds.event_handler import ExecutionStatus, PDSEventHandler
from copilot.logic.pds.file_cleanup import clean_up
from copilot.logic.pds.file_metrics_review import FileMetricsReview
from copilot.logic.pds.stuck_submission_service import INVALID_FILE_EXTENSIONS
from copilot.logic.pds.submission_fields_consolidation.submission_fields_consolidator import (
    get_naics,
)
from copilot.models import (
    File,
    Loss,
    Organization,
    ReportV2,
    Settings,
    Submission,
    SubmissionPriority,
    User,
    db,
)
from copilot.models.files import (
    CustomFileType,
    EnhancedFile,
    FileMetric,
    FilesClearing,
    LoadedProcessedFile,
    ProcessedFile,
)
from copilot.models.ifta import IFTAData
from copilot.models.pds_metrics import PDSStats
from copilot.models.reports import RUSH_PRIORITY
from copilot.models.submission_history import track_submission_history_event
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.types import (
    MissingDataStatus,
    ReportShadowType,
    SubmissionActionType,
    SubmissionConsolidationStatus,
    UserShadowFileState,
)
from copilot.models.workers_comp_experience import (
    WorkersCompExperience,
    WorkersCompStateRatingInfo,
)
from copilot.v3.utils.files import update_file_processing_state

logger = get_logger()

FILE_PROCESSING_RETRY_COUNT_THRESHOLD = 2


class FileHandler(PDSEventHandler):
    df_schema = LeanResolvedDataFieldSchema(many=True)
    brd_schema = BusinessResolutionDataSchema(many=True)

    def __init__(self) -> None:
        super().__init__()
        self._process_file_state_machine_arn = self.step_function_arn(os.environ.get("PROCESS_FILE_STEP_FUNCTION"))
        self.file_metrics_reviewer = FileMetricsReview()

    def invoke_file_processing(self, file: File, restart_when_running: bool = False) -> None:
        log = logger.bind(file_id=file.id, submission_id=file.submission_id)
        if file.execution_arn:
            status = self._get_execution_status(file.execution_arn)
            if status == ExecutionStatus.RUNNING:
                if restart_when_running:
                    log.warning("Stopping file processing SF from CAPI")
                    self._stop_execution(file.execution_arn, "Stopped from Copilot", "File stuck in processing")
                else:
                    log.info("File processing already running", execution_arn=file.execution_arn)
                    return
            elif status == ExecutionStatus.SUCCEEDED:
                log.info("File processing execution already succeeded")
                db.session.refresh(file)
                later_states = (
                    FileProcessingState.data_extraction_complete_states()
                    | FileProcessingState.final_states_without_processed_data()
                )
                if file.processing_state not in later_states:
                    log.error("Execution SF succeeded, but file is not in correct state")
                else:
                    return
            if file.retry_count >= FILE_PROCESSING_RETRY_COUNT_THRESHOLD:
                log.warning("File processing execution failed 3 times")
                update_file_processing_state(
                    file,
                    FileProcessingState.PROCESSING_FAILED,
                    sensible_status=None,
                    send_event=True,
                    error_message="Invoking files processing after getting to retry limits.",
                )
                return
            log.info("Retrying file processing")
            file.retry_count += 1
            file.processing_state_updated_at = datetime.utcnow()

        request = {"submission_id": str(file.submission_id), "file_id": str(file.id)}
        name = f"{file.id!s}-{self._get_now_str()}"
        db.session.commit()
        log.info("Starting file processing")
        file.execution_arn = self._start_execution(self._process_file_state_machine_arn, name, json.dumps(request))
        db.session.commit()

    def retry_file_classification(self, file: File) -> None:
        log = logger.bind(file_id=file.id, submission_id=file.submission_id)
        submission = SubmissionDAO.get_minimal_submission(file.submission_id, raise_404=False)
        if file.retry_count >= FILE_PROCESSING_RETRY_COUNT_THRESHOLD:
            log.warning("File is stuck - classification failed", updated_at=file.processing_state_updated_at)
            update_file_processing_state(
                file,
                FileProcessingState.CLASSIFICATION_FAILED,
                sensible_status=None,
                send_event=True,
                error_message=f"File stuck in {file.processing_state} state for more than 5 minutes.",
            )
            return
        log.info("Retrying classification")
        file.retry_count += 1
        file.processing_state_updated_at = datetime.utcnow()
        db.session.commit()
        KalepaEventsHandler.send_submission_file_added_event(submission, file)

    def _file_requires_autoconfirmation(self, pf: ProcessedFile | None) -> bool:
        try:
            file_id = pf.file_id if pf else None
            if not pf or not pf.entity_mapped_data:
                logger.info("Skipping file resolution", file_id=file_id)
                return False
            if not pf.entity_mapped_data.get("entity_information"):
                pf.business_resolution_data = None
                db.session.add(pf)
                logger.info("Skipping file resolution", file_id=file_id)
                return False
            if not pf.business_resolution_data or not pf.business_resolution_data.get("resolution_data"):
                return True

            entity_info: list[ResolvedDataField] = self.df_schema.load(pf.entity_mapped_data.get("entity_information"))
            brd: list[BusinessResolutionData] = self.brd_schema.load(pf.business_resolution_data)
            already_resolved = {(b.requested_name, b.requested_address, b.entity_idx) for b in brd}
            might_need_resolution = defaultdict(lambda: (None, None, None))
            for ei in entity_info:
                if ei.name == EntityFieldID.NAME.value:
                    for v in ei.values:
                        might_need_resolution[v.entity_idx] = (
                            v.value,
                            might_need_resolution[v.entity_idx][1],
                            v.entity_idx,
                        )
                if ei.name == EntityFieldID.ADDRESS.value:
                    for v in ei.values:
                        might_need_resolution[v.entity_idx] = (
                            might_need_resolution[v.entity_idx][0],
                            v.value,
                            v.entity_idx,
                        )
            pending_resolution = set(might_need_resolution.values()) ^ already_resolved

            if not pending_resolution and len(might_need_resolution) == len(brd):
                logger.info(
                    "Skipping file resolution",
                    file_id=file_id,
                    already_resolved=already_resolved,
                    might_need_resolution=might_need_resolution,
                )
                return False
            return True
        except Exception as e:
            logger.error("Unexpected exception in _file_requires_confirmation", exc_info=e)
            return True

    def move_files_to_next_stage(self, submission: Submission, target_state: SubmissionProcessingState) -> None:
        logger.info(
            "Moving files to next stage",
            submission_id=submission.id,
            target_state=target_state,
            current_state=submission.processing_state,
        )
        if submission.processing_state == SubmissionProcessingState.DATA_ONBOARDING:
            for file in submission.files:
                if file.processing_state == FileProcessingState.WAITING_FOR_DATA_ONBOARDING:
                    file.processing_state = FileProcessingState.DATA_ONBOARDED
        elif target_state == SubmissionProcessingState.AUTOCONFIRMING:
            current_file_state = SUBMISSION_STATE_TO_FILE_STATE.get(submission.processing_state)
            file_ids = [f.id for f in submission.files if f.processing_state == current_file_state]
            load_only_fields = load_only(
                ProcessedFile.entity_mapped_data, ProcessedFile.business_resolution_data, ProcessedFile.file_id
            )
            pfs = db.session.query(ProcessedFile).options(load_only_fields)
            pfs = pfs.filter(ProcessedFile.file_id.in_(file_ids)).filter(ProcessedFile.entity_mapped_data.is_not(None))
            pf_map: dict[UUID, ProcessedFile] = {pf.file_id: pf for pf in pfs.all()}
            for file in submission.files:
                if not file.processing_state == current_file_state:
                    continue
                if self._file_requires_autoconfirmation(pf_map.get(file.id)):
                    file.processing_state = FileProcessingState.AUTOCONFIRMING
                else:
                    file.processing_state = FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION
        elif (
            submission.processing_state != SubmissionProcessingState.BUSINESS_DEDUPLICATION
            or target_state != SubmissionProcessingState.DATA_ONBOARDING
        ):
            for file in submission.files:
                if file.processing_state == SUBMISSION_STATE_TO_FILE_STATE.get(submission.processing_state):
                    file.processing_state = SUBMISSION_STATE_TO_FILE_STATE[target_state]
        else:
            entity_mapped_files = [f.id for f in submission.entity_mapped_files()]
            logger.info("Entity mapped files", entity_mapped_files=entity_mapped_files)
            for file in submission.files:
                if file.processing_state == SUBMISSION_STATE_TO_FILE_STATE[submission.processing_state]:
                    # We want to skip files that are not due for DO, but we want to include ones that went through EM
                    logger.info("Moving file", file_id=file.id, classification=file.classification)
                    if (
                        file.classification in ClassificationDocumentType.skip_onboarding_classifications()
                        and file.id not in entity_mapped_files
                    ):
                        file.processing_state = FileProcessingState.WAITING_FOR_COMPLETION
                    else:
                        file.processing_state = SUBMISSION_STATE_TO_FILE_STATE[
                            SubmissionProcessingState.DATA_ONBOARDING
                        ]

    def handle_file_reprocessing_requested(self, submission: Submission, file: File):
        self.remove_child_files(file, submission, remove_processed_data=True)
        self.remove_file_extracted_data(file, remove_processed_data=True)
        file.classification = None
        file.file_type = FileType.UNKNOWN
        file.execution_arn = None
        file.initial_processing_state = None
        file.processing_state = FileProcessingState.NOT_CLASSIFIED
        track_submission_history_event(
            submission.id,
            SubmissionActionType.PDS_FILE_REPROCESSING_REQUESTED,
            {"file_id": str(file.id), "file_type": file.file_type},
        )
        self.handle_new_file_added(submission, file)

    def handle_file_ignored(self, submission: Submission | None, file: File):
        if submission is None:
            submission = SubmissionDAO.get_minimal_submission(
                additional_fields=["processing_state"],
                id=file.submission_id,
                include_files=True,
                files_additional_fields=["id", "parent_file_id"],
            )
        if file.processed_file == FileProcessingState.REPLACED:
            logger.info("Can't ignore replaced file", file_id=file.id, submission_id=submission.id)
            db.session.rollback()
            flask.abort(400, "Can't ignore replaced file")
        ignored_files = self.mark_file_ignored(file, submission.files, remove_processed_data=False)
        ignored_files.append(file)
        for ignored_file in ignored_files:
            self.remove_file_extracted_data(ignored_file, remove_processed_data=False)
        file.processing_state = FileProcessingState.IGNORED
        db.session.commit()

        if submission.processing_state == SubmissionProcessingState.PROCESSING:
            KalepaEventsHandler.send_submission_file_processing_cancelled(submission.id)

    def handle_file_deleted(self, submission: Submission, file: File):
        logger.info("Handling file deleted", file_id=file.id, submission_id=submission.id)
        self.remove_file(file, submission, remove_processed_data=True)
        db.session.commit()

        if submission.processing_state == SubmissionProcessingState.PROCESSING:
            KalepaEventsHandler.send_submission_file_processing_cancelled(submission.id)

    def handle_files_through_user_shadow_report(self, submission: Submission) -> None:
        from copilot.logic.reports import handle_delete_report

        files_to_add = {f.checksum: f for f in submission.files if f.user_shadow_state == UserShadowFileState.CREATED}
        files_to_remove = {
            f.checksum: f
            for f in submission.files
            if f.user_shadow_state in [UserShadowFileState.DELETED, UserShadowFileState.REPLACED]
        }

        if submission.report.shadow_type == ReportShadowType.HAS_ACTIVE_SHADOW:
            shadow_report_id = ReportDAO.get_shadow_report_id(submission.report_id)
            shadow_submission = SubmissionDAO.get_minimal_submission(include_files=True, report_id=shadow_report_id)
            if shadow_submission.processing_state == SubmissionProcessingState.COMPLETED:
                handle_delete_report(shadow_submission.report)
            else:
                for f in (f for f in shadow_submission.files if f.checksum in files_to_remove):
                    self.remove_file(f, shadow_submission, remove_processed_data=True)

                existing_shadow_files = {f.checksum for f in shadow_submission.files}
                for f in (f for checksum, f in files_to_add.items() if checksum not in existing_shadow_files):
                    shadow_file = f.copy(
                        old_to_new_ids={submission.id: shadow_submission.id},
                        submission_s3_client=current_app.submission_s3_client,
                        for_shadow=True,
                    )
                    shadow_submission.files.append(shadow_file)
                submission.report.is_user_waiting_for_shadow = True
                if shadow_submission.priority:
                    shadow_submission.priority.overwritten_priority = RUSH_PRIORITY
                else:
                    shadow_submission.priority = SubmissionPriority(overwritten_priority=RUSH_PRIORITY)
                db.session.commit()
                return

        # Create a new shadow if there's no active shadow, or it was completed and deleted above
        shadow_report = submission.report.create_shadow_report()
        submission.report.is_user_waiting_for_shadow = True
        shadow_submission = shadow_report.submission
        shadow_submission.priority = SubmissionPriority(overwritten_priority=RUSH_PRIORITY)
        file_ids_to_process = [f.id for f in shadow_submission.files if f.checksum in files_to_add]
        # We need to remove (soft delete) deleted/replaced child files from shadow submission
        # They are copied and soft deleted to prevent reappearing after restart processing of the parent file
        for f in (f for f in shadow_submission.files if f.checksum in files_to_remove):
            self.remove_file(f, shadow_submission, remove_processed_data=True, soft_delete_if_child_file=True)

        from copilot.logic.files_clearing import update_files_clearing_for_submission

        update_files_clearing_for_submission(
            shadow_submission,
            FilesClearing(file_ids_to_process=file_ids_to_process),
        )
        if submission.report.shadow_type == ReportShadowType.CHAINED_SHADOW:
            handle_delete_report(submission.report)
        db.session.commit()

    @staticmethod
    def reprocess_files_using_cached_data(files: list[File]) -> None:
        files_for_reprocessing = [
            f
            for f in files
            if f.processing_state
            in (FileProcessingState.final_states_with_processed_data() | {FileProcessingState.WAITING_FOR_COMPLETION})
        ]
        for file in files_for_reprocessing:
            if (
                file.classification in ClassificationDocumentType.pds_classifications()
                or file.file_type in FileType.not_processed_due_for_em()
            ):
                file.processing_state = FileProcessingState.CACHE_PROCESSED.value
            else:
                # Files like LR, IFTA file level cache is not supported
                # we have to process them again together with files for full reprocess
                file.processing_state = FileProcessingState.CLASSIFIED.value
                FileHandler.remove_file_extracted_data(file, remove_processed_data=True)

    @staticmethod
    def soft_delete_file(submission: Submission, file: File) -> None:
        logger.info("Soft deleting file", file_id=file.id, submission_id=submission.id)

        client_file_tags = file.client_file_tags
        file.is_deleted = True
        file.processing_state = FileProcessingState.IGNORED
        file.is_required_shadow_processing = False
        file.external_identifier = None
        file.client_file_tags = None

        KalepaEventsHandler.send_submission_file_deleted_event(
            submission, file, is_soft_delete=True, client_file_tags=client_file_tags
        )

    def remove_file(
        self,
        file: File,
        submission: Submission,
        remove_processed_data: bool = False,
        soft_delete_if_child_file: bool = True,
    ) -> None:
        for other_file in [f for f in submission.files if f.replaced_by_file_ids]:
            if file.id in other_file.replaced_by_file_ids:
                other_file.replaced_by_file_ids = [f for f in other_file.replaced_by_file_ids if f != file.id]
                logger.info(
                    "Removed deleted file from replaced files",
                    file_id=other_file.id,
                    replaced_by=other_file.replaced_by_file_ids,
                )
                if not other_file.replaced_by_file_ids:
                    logger.info("Replaced file has no more replacements, marking as ignored", file_id=other_file.id)
                    other_file.processing_state = FileProcessingState.IGNORED
                    other_file.replaced_by_file_ids = None
                db.session.add(other_file)
        self.remove_child_files(file, submission, remove_processed_data)
        self.remove_file_extracted_data(file, remove_processed_data)
        logger.info("Removing file", file_id=file.id, submission_id=submission.id, is_internal=file.is_internal)
        if soft_delete_if_child_file and file.parent_file_id is not None:
            self.soft_delete_file(submission, file)
        else:
            KalepaEventsHandler.send_submission_file_deleted_event(submission, file)
            db.session.delete(file)

    def remove_child_files(self, file: File, submission: Submission, remove_processed_data: bool = False) -> None:
        for child_file in [f for f in submission.files if f.parent_file_id == file.id]:
            self.remove_file(child_file, submission, remove_processed_data, soft_delete_if_child_file=False)

    def mark_file_ignored(
        self, file: File, submission_files: list[File], remove_processed_data: bool = False
    ) -> list[File]:
        ignored_files = []
        for child_file in [f for f in submission_files if f.parent_file_id == file.id]:
            if child_file.processing_state == FileProcessingState.REPLACED:
                logger.info(
                    "Can't ignore replaced child file",
                    file_id=child_file.id,
                    submission_id=child_file.submission_id,
                    parent_file_id=file.id,
                )
                db.session.rollback()
                flask.abort(400, "Can't ignore replaced child file")
            ignored_files.extend(self.mark_file_ignored(child_file, submission_files, remove_processed_data))
            child_file.processing_state = FileProcessingState.IGNORED
            db.session.add(child_file)
            ignored_files.append(child_file)
        return ignored_files

    @staticmethod
    def remove_file_extracted_data(file: File, remove_processed_data: bool = False) -> None:
        if file.sensible_status is not None:
            file.sensible_status = None

        db.session.query(SubmissionLevelExtractedData).filter_by(file_id=file.id).delete()

        if remove_processed_data:
            db.session.query(ProcessedFile).filter_by(file_id=file.id).delete()
            db.session.query(FileMetric).filter_by(file_id=file.id).delete()
            db.session.query(EnhancedFile).filter_by(file_id=file.id).delete()

        file_types_to_file_data_classes = {
            FileType.LOSS_RUN: Loss,
            FileType.IFTA: IFTAData,
            FileType.WORK_COMP_EXPERIENCE: WorkersCompExperience,
        }
        if file.file_type in file_types_to_file_data_classes:
            db.session.query(file_types_to_file_data_classes[file.file_type]).filter_by(file_id=file.id).delete()

        if file.classification == ClassificationDocumentType.ACORD_130:
            db.session.query(WorkersCompStateRatingInfo).filter_by(file_id=file.id).delete()

        current_app.ers_client_v3.delete_relations_by_file(file_id=str(file.id))

    @staticmethod
    def handle_custom_file_added(file: File, submission: Submission) -> None:
        log = logger.bind(file_id=file.id, submission_id=submission.id)

        custom_file_type = CustomFileType.query.get_or_404(file.custom_file_type_id, "Custom file type not found")

        if not custom_file_type.is_enabled:
            flask.abort(404, "Custom file type is not enabled")

        classification = ClassificationDocumentType.CUSTOM.value
        if document_type := detect_document_type(file.name):
            log.info("Document type detected for custom file", document_type=document_type)
            if classification_suffix := get_file_document_suffix(document_type):
                file.custom_classification = f"{custom_file_type.file_type_name}{classification_suffix}"
                classification += classification_suffix
            else:
                file.custom_classification = custom_file_type.file_type_name
        else:
            log.info("No document type detected for custom file")
            file.custom_classification = custom_file_type.file_type_name

        file.processing_state = FileProcessingState.CLASSIFIED
        file.file_type = FileType.CUSTOM
        file.classification = classification
        db.session.commit()
        KalepaEventsHandler.send_submission_file_classified_event(submission, file)

    def handle_new_file_added(
        self,
        submission: Submission,
        file: File,
        starting_processing_submission: bool = False,
    ) -> None:
        log = logger.bind(
            submission_id=file.submission_id,
            file_id=file.id,
            starting_processing_submission=starting_processing_submission,
        )
        if file.is_deleted:
            log.info("Skipping deleted file")
            return
        log.info("Handling new file added")
        if not starting_processing_submission:
            self._handle_missing_data_for_new_file(file, submission)
        self._handle_processing_dependency_for_new_file(file, submission)

        org_settings = self._get_org_settings(submission)
        is_file_processed_independently_from_auto_processing = (
            self._is_file_processed_independently_from_auto_processing(submission, file, org_settings)
        )
        is_auto_processed_or_classify_files_for_shells = (
            submission.is_auto_processed or org_settings.classify_files_for_shells
        )
        # If submission is not supposed to be processed and the file is not one of the always processed types do nothing
        if not submission.can_be_updated or not (
            is_auto_processed_or_classify_files_for_shells or is_file_processed_independently_from_auto_processing
        ):
            log.info("File added to submission that is not supposed to be processed")
            KalepaEventsHandler.send_submission_file_added_event(submission, file, start_file_processing=False)
            return

        # If the submission is not completed we need to update it's processing state
        if (
            submission.is_auto_processed
            and submission.processing_state not in SubmissionProcessingState.final_states()
            and (
                file.file_type not in FileType.processed_without_entities_types()
                or submission.processing_state == SubmissionProcessingState.PROCESSING
            )
        ):
            current_state = submission.processing_state
            submission.processing_state = SubmissionProcessingState.CLASSIFYING
            if not file.is_document_ingestion:
                # If support has already started loading the sub, we need to reset the files
                if current_state in SubmissionProcessingState.submission_wizard_started_states():
                    submission.report.going_back = True
                submission.is_processing = True
        # There is race condition where the file might be set to classified,
        # but then the restart has wiped the classification, and we should re-trigger it.
        if (
            file.classification is None
            and file.processing_state not in FileProcessingState.finished_classification_states()
        ):
            file.processing_state = FileProcessingState.NOT_CLASSIFIED
        db.session.commit()
        # If we start processing the submission we need to move the already processed files to the correct state
        # and if they are already moved we don't do anything.
        if (
            starting_processing_submission
            and is_file_processed_independently_from_auto_processing
            and submission.processing_state != SubmissionProcessingState.ENHANCING_SHELL
        ):
            if (
                file.processing_state in FileProcessingState.finished_processing_states()
                or file.processing_state == FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION
            ):
                log.info("Handle file processed independently from auto processing")
                self.handle_file(file=file, submission=submission)
                db.session.commit()
                return
            elif file.processing_state in FileProcessingState.already_consolidated_states():
                log.info("Handle file already consolidated")
                self._maybe_finish_classification(submission, file, log)
                return

        if file.custom_file_type_id:
            log.info("File added with custom type")
            self.handle_custom_file_added(file, submission)
            return

        # If the file is already classified send the event
        if file.processing_state != FileProcessingState.NOT_CLASSIFIED:
            if file.processing_state in FileProcessingState.finished_classification_states():
                log.info("File already classified")
                KalepaEventsHandler.send_submission_file_classified_event(submission, file)
                return
            logger.warning("File added that is not in initial state", file_id=file.id, state=file.processing_state)
            return
        # Finally send the event that new file has been added so we can start with classification and processing
        if not (starting_processing_submission and org_settings.classify_files_for_shells):
            KalepaEventsHandler.send_submission_file_added_event(submission, file)

    @staticmethod
    def handle_file_user_type_changed(submission: Submission, file: File) -> None:
        if not submission.is_auto_processed or submission.processing_state in SubmissionProcessingState.final_states():
            return
        if submission.processing_state in SubmissionProcessingState.submission_wizard_started_states():
            submission.report.going_back = True
        submission.is_processing = True
        file.execution_arn = None
        file.initial_processing_state = None
        # Remove the old processed data as there might be leakage of old data into the new one.
        file.processed_file = None
        if file.user_file_type != FileType.CUSTOM:
            file.custom_file_type_id = None
            file.custom_classification = None
            file.processing_state = FileProcessingState.NOT_CLASSIFIED
            submission.processing_state = SubmissionProcessingState.CLASSIFYING
            KalepaEventsHandler.send_submission_file_type_updated(file)

    def _handle_file_classified_for_shadow_report(self, submission: Submission, file: File) -> None:
        if not (shadow_report_id := ReportDAO.get_shadow_report_id(submission.report_id)):
            return
        shadow_submission = SubmissionDAO.get_minimal_submission(include_files=True, report_id=shadow_report_id)
        if existing_file := next((f for f in shadow_submission.files if f.checksum == file.checksum), None):
            # We need that in case of delay in the event processing.
            # We could already copy file, but without is_required_shadow_processing flag
            if existing_file.is_required_shadow_processing != file.is_required_shadow_processing:
                existing_file.is_required_shadow_processing = file.is_required_shadow_processing
                db.session.commit()
            return
        old_to_new_ids = {submission.id: shadow_submission.id}
        shadow_files_checksums = {f.checksum: f.id for f in shadow_submission.files}
        for submission_file in submission.files:
            if shadow_file_id := shadow_files_checksums.get(submission_file.checksum):
                old_to_new_ids[submission_file.id] = shadow_file_id
            else:
                old_to_new_ids[submission_file.id] = uuid4()
        if file.parent_file_id:
            parent_file = next((f for f in submission.files if f.id == file.parent_file_id), None)
            if parent_file.checksum not in shadow_files_checksums:
                logger.info(
                    "Parent file not found in shadow report, copying it",
                    file_id=str(parent_file.id),
                    submission_id=str(submission.id),
                )
                shadow_parent_file = parent_file.copy(
                    old_to_new_ids=old_to_new_ids,
                    submission_s3_client=current_app.submission_s3_client,
                    for_shadow=True,
                )
                shadow_submission.files.append(shadow_parent_file)
        shadow_file_copy = file.copy(
            old_to_new_ids=old_to_new_ids, submission_s3_client=current_app.submission_s3_client, for_shadow=True
        )
        shadow_submission.files.append(shadow_file_copy)
        db.session.commit()
        self._handle_file_classified(shadow_file_copy)

    def _notify_classification_failed(self, file: File) -> None:
        try:
            if flask.current_app.is_prod:
                flask.current_app.slack_client.send_slack_message(
                    STUCK_FOR_ENGINEERING_SLACK_CHANNEL_ID,
                    f"File {file.name} (ID: {file.id} SUBMISSION_ID: {file.submission_id}) has failed classification.",
                )
            if file.is_document_ingestion:
                flask.current_app.slack_client.send_slack_message(
                    STUCK_FOR_ENGINEERING_SLACK_CHANNEL_ID,
                    (
                        f"[DOCUMENT INGESTION] File {file.name} (ID: {file.id} SUBMISSION_ID: {file.submission_id}) has"
                        " failed classification. We will keep it's state classifying, but please review ASAP."
                    ),
                )
                file.processing_state = FileProcessingState.CLASSIFYING
                db.session.commit()
        except Exception as e:
            logger.exception("Slack notification failed", e=e)

    def _notify_shadow_for_old_submission(self, submission: Submission) -> None:
        try:
            if flask.current_app.is_prod:
                flask.current_app.slack_client.send_slack_message(
                    STUCK_FOR_ENGINEERING_SLACK_CHANNEL_ID,
                    (
                        "Files were added to submission"
                        f" https://copilot.kalepa.com/report/{submission.report_id} ({submission.name}) with ID:"
                        f" {submission.id}. The submission was created {submission.created_at}, which is older than 6"
                        " months, hence we didn't created shadow submission."
                    ),
                )
        except Exception as e:
            logger.exception("Slack notification failed", e=e)

    def _maybe_finish_classification(self, submission: Submission, file: File, log) -> None:
        if (
            submission.is_auto_processed
            and submission.all_files_classified
            and submission.processing_state
            in [SubmissionProcessingState.CLASSIFYING, SubmissionProcessingState.FILES_CLEARING]
        ):
            if self._should_cancel_processing_based_on_files_classifications(submission):
                self._tag_email_as_not_submission_or_cancel_processing(
                    submission, "No submission related files were found."
                )
                return
            log.info("All files classified. Moving to next stage.")
            submission.processing_state = SubmissionProcessingState.PROCESSING
            db.session.commit()
            self._handle_data_consolidation(file, force=True)
            self._check_all_files_ready(submission, file.is_document_ingestion)
        elif not submission.is_auto_processed and submission.all_files_classified:
            org_settings = self._get_org_settings(submission)
            if org_settings.classify_files_for_shells:
                log.info("All files classified for shell submission. Moving to data consolidation")
                self._handle_data_consolidation(file, force=True)

    @staticmethod
    def _tag_email_as_not_submission_or_cancel_processing(submission: Submission, explanation: str) -> None:
        from copilot.logic.pds.email_handler import EmailHandler

        email = EmailHandler.get_email_for_classification(report=submission.report, pick_root=False)
        if not EmailHandler.maybe_mark_as_not_submission(
            email=email, submission=submission, organization_id=submission.organization_id, explanation=explanation
        ):
            from copilot.logic.reports import cancel_report_processing

            logger.info("Cancelling submission processing.", submission_id=submission.id, explanation=explanation)
            cancel_report_processing(submission.report)

    @staticmethod
    def _should_cancel_processing_based_on_files_classifications(submission: Submission) -> bool:
        if Organization.is_bishop_conifer_for_id(submission.report.organization_id):
            file_classifications_for_processing_cancellation = {
                ClassificationDocumentType.ACORD_35,
                ClassificationDocumentType.ACORD_175,
            }
            if any(f.classification in file_classifications_for_processing_cancellation for f in submission.files):
                return True

            keywords_preventing_cancellation = {"application"}
            email = submission.report.email_subject or ""
            email += submission.report.email_body_text[:45]
            if any(keyword in email.lower() for keyword in keywords_preventing_cancellation):
                return False

            def is_file_acceptable_for_processing_cancellation(f):
                return (
                    f.file_type in {FileType.EMAIL, FileType.HTML_DOCUMENT, FileType.OTHER, FileType.UNKNOWN}
                    and (
                        f.processing_state not in {FileProcessingState.NOT_CLASSIFIED, FileProcessingState.CLASSIFYING}
                    )
                ) or (
                    f.file_type in {FileType.RAW_EMAIL, FileType.ARCHIVE}
                    and f.processing_state in FileProcessingState.finished_processing_states()
                )

            if all(is_file_acceptable_for_processing_cancellation(f) for f in submission.files):
                return True

            if has_submission_only_quote_files(submission):
                return True

        return False

    @staticmethod
    def _should_cancel_processing_based_on_processed_files(file: File) -> bool:
        if Organization.is_bishop_conifer_for_id(file.organization_id):
            if has_submission_only_cancellable_policy_file(file.submission):
                return True

        return False

    @staticmethod
    def _file_is_processed_via_manual_intervention(file: File, report: ReportV2) -> bool:
        if file.file_type != FileType.LOSS_RUN:
            return False
        return report.lr_manually_processing_enabled

    @staticmethod
    def get_classification_lock_name(file: File) -> str:
        return f"file_classified_for_submission_{file.submission_id}"

    def _handle_file_classified(self, file: File) -> None:
        log = logger.bind(submission_id=file.submission_id, file_id=file.id)
        if (
            file.processing_state == FileProcessingState.CLASSIFICATION_FAILED
            and file.submission.processing_state != SubmissionProcessingState.FILES_CLEARING
        ):
            log.info("Classification failed for file.")
            self._notify_classification_failed(file)
            self._maybe_finish_classification(file.submission, file, log)
            return
        if file.processing_state != FileProcessingState.CLASSIFIED:
            log.warning("File classified that is not in CLASSIFIED state", processing_state=file.processing_state)
            return
        if file.file_type == FileType.CORRESPONDENCE_EMAIL:
            log.info("File is correspondence email. Skipping processing.")
            file.processing_state = FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING
            db.session.commit()
            return
        lock_name = self.get_classification_lock_name(file)
        log.info("Acquiring lock", lock=lock_name)
        try:
            with redis_lock.Lock(current_app.locks_client, name=lock_name, expire=15):
                db.session.refresh(file)
                if file.processing_state != FileProcessingState.CLASSIFIED:
                    log.warning(
                        "File classified that is not in CLASSIFIED state", processing_state=file.processing_state
                    )
                    return
                submission = SubmissionDAO.get_minimal_submission(
                    id=file.submission_id,
                    additional_fields=["processing_state", "is_auto_processed", "created_at"],
                    include_report=True,
                    report_additional_fields=[
                        "additional_data",
                        "owner_id",
                        "organization_id",
                        "shadow_type",
                        "correspondence_id",
                        "name",
                        "routing_tags",
                        "tier",
                    ],
                    include_files=True,
                    files_additional_fields=["*"],
                )
                if submission is None:
                    log.error("Submission not found")
                    flask.abort(404)
                if submission.processing_state == SubmissionProcessingState.FILES_CLEARING:
                    log.info("Submission is in FILES_CLEARING state. Skipping processing.")
                    return
                report = submission.report

                if self._file_is_processed_via_manual_intervention(file, report):
                    log.info("File can only be processed via manual intervention")
                    self._maybe_finish_classification(submission, file, log)
                    return

                org_settings = self._get_org_settings(submission)
                is_file_processing_disabled = (
                    file.is_document_ingestion
                    and org_settings.di_disabled_file_types
                    and (
                        file.file_type in org_settings.di_disabled_file_types
                        or (
                            file.custom_file_type
                            and file.custom_file_type.file_type_name in org_settings.di_disabled_file_types
                        )
                    )
                )
                if is_file_processing_disabled:
                    log.info("File processing is disabled for this file type.")
                    file.processing_state = FileProcessingState.IGNORED
                    db.session.commit()
                    return

                is_file_processed_independently_from_auto_processing = (
                    self._is_file_processed_independently_from_auto_processing(submission, file, org_settings)
                )
                if (
                    submission.processing_state == SubmissionProcessingState.CANCELLED
                    and not is_file_processed_independently_from_auto_processing
                ):
                    return

                if submission.is_processing_completed and self._handle_file_classified_for_completed_submission(
                    file, submission
                ):
                    return

                if (
                    report.shadow_type == ReportShadowType.IS_ACTIVE_SHADOW
                    and submission.processing_state != SubmissionProcessingState.FILES_CLEARING
                    and file.is_required_shadow_processing
                ):
                    submission.is_processing = False
                    submission.processing_state = SubmissionProcessingState.FILES_CLEARING
                    submission.stuck_reason = "Shadow report, file clearing required"
                    db.session.commit()
                    return
                if (file.processing_state not in FileProcessingState.finished_processing_states()) and (
                    is_file_processed_independently_from_auto_processing or submission.is_auto_processed
                ):
                    if file.hidden:
                        file.processing_state = FileProcessingState.IGNORED
                        db.session.commit()
                    else:
                        file.processing_state = FileProcessingState.PROCESSING
                        self.invoke_file_processing(file)
                self._maybe_finish_classification(submission, file, log)
        except redis_lock.NotAcquired:
            log.warning("Lock already expired", lock=lock_name)

    def _handle_file_classified_for_completed_submission(self, file: File, submission: Submission) -> bool:
        log = logger.bind(submission_id=file.submission_id, file_id=file.id)
        report = submission.report
        file_types_processed_for_completed_submissions_without_shadow_report = (
            FileType.always_processed_types() | FileType.processed_without_entities_types()
        )
        if (
            FeatureFlagsClient.is_feature_enabled(FeatureType.PROCESS_BOSS_FILES_WITH_CHILD_FILES)
            and submission.is_boss
            and not file.is_rose_file
            and file.file_type not in FileType.always_processed_types()
        ) or file.is_document_ingestion:
            log.info("File is not applicable for processing after submission is completed.")
            file.processing_state = FileProcessingState.IGNORED
            db.session.commit()
        elif (
            file.classification in ClassificationDocumentType.pds_classifications()
            and file.classification
            not in [ClassificationDocumentType.EMAIL, ClassificationDocumentType.CORRESPONDENCE_EMAIL]
        ) or submission.any_file_requires_shadow_processing:
            log.info("File requires shadow report for processing.")
            file.is_required_shadow_processing = True
            db.session.commit()
        elif (
            file.file_type not in file_types_processed_for_completed_submissions_without_shadow_report
            and file.file_type != FileType.MERGED
        ):
            file.processing_state = FileProcessingState.IGNORED
            db.session.commit()
        if report.shadow_type == ReportShadowType.HAS_ACTIVE_SHADOW:
            log.info("Adding file to existing active shadow report.")
            self._handle_file_classified_for_shadow_report(submission, file)
            return True
        if submission.is_required_shadow_processing:
            shadow_creation_cut_off_time = datetime.now(submission.created_at.tzinfo) - SHADOW_CREATION_CUT_OFF_TIME
            if submission.created_at < shadow_creation_cut_off_time:
                log.info("Submission is too old for shadow report creation.")
                self._notify_shadow_for_old_submission(submission)
                return True
            log.info("Creating shadow report for completed submission.")
            shadow_report = report.create_shadow_report()
            shadow_report.submission.stuck_reason = "Shadow report, file clearing required"
            if report.shadow_type == ReportShadowType.CHAINED_SHADOW:
                from copilot.logic.reports import handle_delete_report

                handle_delete_report(report)
            db.session.commit()
            return True
        if file.is_required_shadow_processing:
            log.info("Waiting for classifying other files before creating a shadow report.")
            return True
        if file.processing_state == FileProcessingState.IGNORED:
            log.info("Submission is completed and file is not applicable for processing .")
            return True
        return False

    @staticmethod
    def _create_pds_stats_for_processed_file(loaded_pf: LoadedProcessedFile) -> PDSStats:
        number_of_autoconfirmed_entities = 0
        number_of_not_confirmed_entities = 0
        idx_of_entities_without_name = []
        idx_of_entities_without_address = []
        for brd in loaded_pf.business_resolution_data or []:
            if brd.entity_id:
                number_of_autoconfirmed_entities += 1
            else:
                number_of_not_confirmed_entities += 1
            if not brd.requested_name:
                idx_of_entities_without_name.append(brd.entity_idx)
            if not brd.requested_address:
                idx_of_entities_without_address.append(brd.entity_idx)
        return PDSStats(
            file_id=loaded_pf.processed_data.files[0],
            number_of_autoconfirmed_entities=number_of_autoconfirmed_entities,
            number_of_not_confirmed_entities=number_of_not_confirmed_entities,
            number_of_extracted_entities=len(loaded_pf.processed_data.entities),
            idx_of_entities_without_name=idx_of_entities_without_name,
            idx_of_entities_without_address=idx_of_entities_without_address,
        )

    def _should_move_file_through_di(self, file: File) -> bool:
        if file.processing_state == FileProcessingState.WAITING_FOR_HUMAN_INPUT:
            return False
        user_email = None
        if user := User.query.filter(User.id == file.user_id).first():
            user_email = user.email
        file_types = FeatureFlagsClient.is_feature_enabled(FeatureType.MOVE_FILES_THROUGH_DI, user_email)
        logger.info("Should move file through DI", file_id=file.id, ff_file_types=file_types, file_type=file.file_type)
        if not file_types:
            return False
        try:
            file_types = {FileType.try_parse_str(ft.strip()) for ft in file_types.split(",")}
        except Exception:
            logger.exception("Error while parsing file types")
            return False
        return file.file_type in file_types

    def handle_processed_file(self, file: File) -> FileProcessingState:
        has_failed_critical_check = self.file_metrics_reviewer.review(file)
        if (
            file.file_type not in FileType.processed_without_entities_types()
            and file.processing_state != FileProcessingState.WAITING_FOR_HUMAN_INPUT
        ):
            loaded_pf = clean_up(file)
            if loaded_pf:
                try:
                    file.pds_stats = self._create_pds_stats_for_processed_file(loaded_pf)
                except Exception as e:
                    logger.error(
                        "Error while creating PDS stats",
                        error=str(e),
                        exc_info=e,
                        file_id=file.id,
                        submission_id=file.submission_id,
                    )
        if has_failed_critical_check:
            target_state = self._handle_not_processed_file(file, FileProcessingState.PROCESSING_FAILED)
        elif file.file_type in FileType.processed_without_entities_types():
            if file.is_document_ingestion:
                target_state = FileProcessingState.WAITING_FOR_DATA_ONBOARDING
            else:
                target_state = FileProcessingState.COMPLETED
        elif file.classification not in ClassificationDocumentType.pds_classifications():
            return self._handle_not_processed_file(file, FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING)
        elif self._should_move_file_through_di(file):
            target_state = FileProcessingState.WAITING_FOR_HUMAN_INPUT
        else:
            processed_file = (
                db.session.query(ProcessedFile)
                .options(load_only(ProcessedFile.id))
                .filter(ProcessedFile.file_id == file.id)
                .first()
            )
            if not processed_file:
                logger.warning("Successfully processed file with entities has no processed data", file_id=file.id)
                target_state = self._handle_not_processed_file(file, FileProcessingState.PROCESSING_FAILED)
            elif file.is_document_ingestion:
                if file.file_type in {
                    FileType.SUPPLEMENTAL_FORM,
                    FileType.EMAIL,
                    FileType.SOV,
                    FileType.DRIVERS,
                    FileType.VEHICLES,
                    FileType.BUDGET,
                    FileType.PROJECT_SCHEDULE,
                } or file.classification in {
                    ClassificationDocumentType.ACORD_125,
                    ClassificationDocumentType.ACORD_126,
                    ClassificationDocumentType.ACORD_131,
                }:
                    target_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
                else:
                    target_state = FileProcessingState.WAITING_FOR_DATA_ONBOARDING
            elif file.classification in ClassificationDocumentType.entity_mapping_classifications():
                target_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
            elif ProcessedFileDAO.has_business_resolution_data(processed_file_id=processed_file.id) and not (
                file.classification in ClassificationDocumentType.skip_bc_classifications()
                and file.processed_file.all_entities_resolved
            ):
                target_state = FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION
            elif file.classification in ClassificationDocumentType.skip_onboarding_classifications():
                # We don't extract entities from Employee Handbook, so we attach all extracted facts to the FNI.
                # In the rare cases when there is no FNI, we drop those facts. This is critical for NW ML. As such
                # we need this quick hack to ensure that we don't drop those facts as for EM we will create a dummy FNI.
                if file.file_type == FileType.EMPLOYEE_HANDBOOK and Organization.is_nationwide_ml_for_id(
                    file.organization_id
                ):
                    target_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
                else:
                    target_state = FileProcessingState.WAITING_FOR_COMPLETION
            elif file.classification in ClassificationDocumentType.data_onboarding_classifications():
                target_state = FileProcessingState.WAITING_FOR_DATA_ONBOARDING
            else:
                logger.error(
                    "File has classification that wasn't captured by any condition",
                    file_id=file.id,
                    classification=file.classification,
                )
                # Marking as processing failed, so it doesn't interfere with the flow of other files.
                target_state = FileProcessingState.PROCESSING_FAILED
        return target_state

    def _handle_not_processed_file(self, file: File, target_state: FileProcessingState) -> FileProcessingState:
        return self._handle_file_without_data(file, target_state)

    def _handle_file_without_data(self, file: File, target_state: FileProcessingState) -> FileProcessingState:
        if file.is_document_ingestion:
            if target_state == FileProcessingState.PROCESSING_FAILED:
                target_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
            if (
                target_state == FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING
                and file.file_type == FileType.ACORD_FORM
            ):
                target_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
            processed_file = (
                db.session.query(ProcessedFile)
                .options(load_only(ProcessedFile.id))
                .filter(ProcessedFile.file_id == file.id)
                .first()
            )
            has_extracted_data = (
                not LeanOnboardedFileSchema().load(processed_file.processed_data).is_empty if processed_file else False
            )
            if target_state == FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING and has_extracted_data:
                target_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
            if not processed_file:
                file.processed_file = ProcessedFile()
                file.processed_file.processed_data = LeanOnboardedFileSchema().dump(
                    OnboardedFile(files=[file.id])  # type: ignore[arg-type]
                )
            if target_state == FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING and file.file_type == FileType.MERGED:
                target_state = FileProcessingState.COMPLETED
            return target_state
        if FileHandler._should_not_processed_file_be_moved_to_em(file):
            logger.info("File failed processing. Marking it for EM.", file_id=file.id)
            target_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
            if not file.processed_file:
                file.processed_file = ProcessedFile()
                file.processed_file.processed_data = LeanOnboardedFileSchema().dump(
                    OnboardedFile(files=[file.id])  # type: ignore[arg-type]
                )
        elif FileHandler._should_not_processed_file_be_moved_to_do(file):
            logger.info("File failed processing. Marking it for DO.", file_id=file.id)
            target_state = FileProcessingState.WAITING_FOR_DATA_ONBOARDING
            if not file.processed_file:
                file.processed_file = ProcessedFile()
                file.processed_file.processed_data = LeanOnboardedFileSchema().dump(
                    OnboardedFile(files=[file.id])  # type: ignore[arg-type]
                )
        return target_state

    @staticmethod
    def _should_not_processed_file_be_moved_to_do(file: File) -> bool:
        """
        Returns True if the file should be moved to DO after failing processing.

        Checks if the file is of a type that should be moved to DO after failing processing.
        If the file type is CONSOLIDATED_FINANCIAL_STATEMENT, check if the organization is in the list of
        organizations that should be processed.
        """
        if file.file_type not in FileType.not_processed_due_for_do():
            return False

        if file.file_type == FileType.CONSOLIDATED_FINANCIAL_STATEMENT:
            return file.organization_id in ORGS_TO_PROCESS_FINANCIAL_STATEMENTS

        return True

    @staticmethod
    def _should_not_processed_file_be_moved_to_em(file: File) -> bool:
        if file.file_type in FileType.not_processed_due_for_em():
            # We want to exclude some not supported ACORDs as they don't contain relevant information
            # * ACORD OFCLADCOV (Additional coverages and endorsements).
            # * ACORD 829 (Forms and Endorsements Schedule).
            # * ACORD 45 (Additional Interest Schedule).
            # * APLIED 125CIS (Contact Information Schedule).
            # * ACORD OFBAADCV (Additional Coverages and Endorsements).
            # * ACORD OFPRCINFO.
            # * ACORD OFREMARK.
            # * ACORD APPLIED 98 - Additional Coverages Overflow
            # * ACORD 146 - Equipment Floater Section
            # * ACORD 101 (Additional Remarks schedule) is very rarely useful
            # * ACORD 137 (Commercial Inland Marine Section) is very rarely useful
            # * ACORD OFPRIORINF - Prior Insurance Information
            # * ACORD OFSUPPNAME - Other Named Insured
            NOT_SUPPORTED_ACORDS_EXCLUDED_FROM_EM = {
                "OFCLADCOV",
                "ACORD_829",
                "ACORD_45",
                "APPLIED_125",
                "OFBAADCV",
                "OFPRCINFO",
                "OFREMARK",
                "APPLIED_98",
                "ACORD_146",
                "OFAPPINFCNI",
                "ACORD_101",
                "ACORD_137",
                "OFPRIORINF",
                "OFSUPPNAME",
                "ACORD_25",
                "ACORD_28",
                "ACORD_152",
            }
            NOT_SUPPORTED_ACORD_VERSIONS_EXCLUDED_FROM_EM = {
                "APPLIED_130_PCS_2000_08",
            }
            NOT_SUPPORTED_SUPPLEMENTALS_EXCLUDED_FROM_EM = {
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_BENEFITS_PLAN.name,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_SHAREHOLDER_INFO.name,
                ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_EMPLOYEE_INFO.name,
            }
            if (
                file.file_type == FileType.ACORD_FORM
                and file.additional_info_obj
                and file.additional_info_obj.acord
                and (
                    file.additional_info_obj.acord.form_name in NOT_SUPPORTED_ACORDS_EXCLUDED_FROM_EM
                    or file.additional_info_obj.acord.version_id in NOT_SUPPORTED_ACORD_VERSIONS_EXCLUDED_FROM_EM
                )
            ) or file.classification in NOT_SUPPORTED_SUPPLEMENTALS_EXCLUDED_FROM_EM:
                return False
            return True
        return False

    @staticmethod
    def _maybe_reset_file_states(submission, target_state: FileProcessingState) -> None:
        logger.info("Going back. Resetting file states.", submission_id=submission.id, target_state=target_state)
        if target_state == SUBMISSION_STATE_TO_FILE_STATE[SubmissionProcessingState.ENTITY_MAPPING]:
            for file in submission.entity_mapped_files():
                file.processing_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
        if (
            target_state == SUBMISSION_STATE_TO_FILE_STATE[SubmissionProcessingState.BUSINESS_CONFIRMATION]
            or target_state == SUBMISSION_STATE_TO_FILE_STATE[SubmissionProcessingState.ENTITY_MAPPING]
        ):
            for file in submission.files_with_entities():
                if file.initial_processing_state != FileProcessingState.CACHE_PROCESSED:
                    file.processing_state = FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION
        db.session.commit()

    @staticmethod
    def _check_all_files_ready(submission: Submission, is_document_ingestion: bool = False):
        if is_document_ingestion:
            if submission.all_files_ready and submission.processing_state == SubmissionProcessingState.PROCESSING:
                logger.info("All files processed", submission_id=submission.id)
                submission.processing_state = SubmissionProcessingState.DATA_ONBOARDING
                submission.is_processing = False
                db.session.commit()
            return
        if submission.all_files_ready:
            logger.info("All files processed", submission_id=submission.id)
            submission.report.going_back = False
            KalepaEventsHandler.send_submission_processing_finished(submission.id)  # type: ignore[arg-type]
            db.session.commit()

    def _handle_file_processed(self, file: File) -> None:
        if not file:
            logger.warning("Processing finished for missing file", file_id=file.id)
            return
        logger.info("Processing finished for file", file_id=file.id, processing_state=file.processing_state)
        if self._should_cancel_processing_based_on_files_classifications(
            file.submission
        ) or self._should_cancel_processing_based_on_processed_files(file):
            self._tag_email_as_not_submission_or_cancel_processing(
                file.submission, "No submission related files were found."
            )
            return

        if file.processing_state == FileProcessingState.PROCESSING_FAILED:
            target_state = self._handle_not_processed_file(file, FileProcessingState.PROCESSING_FAILED)
        elif file.processing_state == FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING:
            target_state = self._handle_not_processed_file(file, FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING)
        elif file.processing_state == FileProcessingState.PROCESSED:
            if self._should_cancel_processing_based_on_processed_files(file):
                self._tag_email_as_not_submission_or_cancel_processing(
                    file.submission, "Canceled based on extracted data from files."
                )
                return

            target_state = self.handle_processed_file(file)
        elif file.processing_state == FileProcessingState.CACHE_PROCESSED and file.is_document_ingestion:
            target_state = FileProcessingState.WAITING_FOR_DATA_ONBOARDING
        elif file.processing_state == FileProcessingState.CACHE_PROCESSED:
            target_state = FileProcessingState.WAITING_FOR_COMPLETION
        else:
            logger.error("Unrecognized processing state", file_id=file.id, processing_state=file.processing_state)
            return
        submission = SubmissionDAO.get_minimal_submission(
            id=file.submission_id,
            include_report=True,
            report_additional_fields=["additional_data", "shadow_type"],
            include_files=True,
            files_additional_fields=["*"],
        )
        file.initial_processing_state = file.processing_state
        file.processing_state = target_state
        logger.info("Target state for file set", file_id=file.id, target_state=target_state)
        if submission.report.going_back:
            self._maybe_reset_file_states(submission, target_state)
        maybe_finish_enhance_shell_processing(submission)
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error("Failed to update file processing state", file_id=file.id, e=e)
        self._check_all_files_ready(submission, file.is_document_ingestion)

    @staticmethod
    def _handle_description_data(
        submission_level_extracted_data: list[SubmissionLevelExtractedData],
    ) -> SubmissionLevelExtractedData | None:
        description_data_list = [
            sub_lvl_data
            for sub_lvl_data in submission_level_extracted_data
            if sub_lvl_data.field == EntityInformation.DESCRIPTION.value
        ]

        description_data = next((desc for desc in description_data_list if desc.source_details == "GENERATED"), None)

        if not description_data and description_data_list:
            description_data = description_data_list[0]

        return description_data

    @staticmethod
    def _handle_generic_data(
        submission_level_extracted_data: list[SubmissionLevelExtractedData], field: str
    ) -> SubmissionLevelExtractedData | None:
        return next(
            (sub_lvl_data for sub_lvl_data in submission_level_extracted_data if sub_lvl_data.field == field),
            None,
        )

    @staticmethod
    def _enrich_processed_data_with_submission_level_extracted_data(file: File) -> None:
        submission_level_extracted_data = SubmissionLevelExtractedData.query.filter(
            SubmissionLevelExtractedData.file_id == file.id,
        ).all()

        if not submission_level_extracted_data:
            logger.warning("No submission_level_extracted data", file_id=file.id)
            return

        extracted_data_to_enrich: list[tuple[FieldType, SubmissionLevelExtractedData]] = []

        if naics_data := FileHandler._handle_generic_data(
            submission_level_extracted_data, EntityInformation.NAICS_CODES.value
        ):
            extracted_data_to_enrich.append((FieldType.TEXT, naics_data))

        if description_data := FileHandler._handle_description_data(submission_level_extracted_data):
            extracted_data_to_enrich.append((FieldType.TEXT, description_data))

        if coverages_data := FileHandler._handle_generic_data(
            submission_level_extracted_data, EntityInformation.COVERAGES_DETAILS.value
        ):
            extracted_data_to_enrich.append((FieldType.TEXT, coverages_data))

        if effective_data := FileHandler._handle_generic_data(
            submission_level_extracted_data, EntityInformation.POLICY_EFFECTIVE_START_DATE
        ):
            extracted_data_to_enrich.append((FieldType.DATETIME, effective_data))

        if expiration_data := FileHandler._handle_generic_data(
            submission_level_extracted_data, EntityInformation.POLICY_END_DATE
        ):
            extracted_data_to_enrich.append((FieldType.DATETIME, expiration_data))

        file_processed_data = LeanOnboardedFileSchema().load(file.processed_file.processed_data)
        entity_idx = file_processed_data.get_or_create_submission_entity_idx(file.submission_id)
        for field_type, data_to_enrich in extracted_data_to_enrich:
            resolved_data_field = get_or_create_entity_field_by_name(
                file_processed_data, data_to_enrich.field, field_type
            )
            resolved_data_value = get_or_create_value_for_entity(
                resolved_data_field,
                entity_idx,
                0,
                True,
            )
            if not resolved_data_value.value:
                if data_to_enrich.field == EntityInformation.NAICS_CODES:
                    resolved_data_value.value = get_naics(json.loads(data_to_enrich.value))
                else:
                    resolved_data_value.value = data_to_enrich.value

        file.processed_file.processed_data = LeanOnboardedFileSchema().dump(file_processed_data)
        db.session.commit()

    def _handle_waiting_for_facts_and_suggestions_resolution(self, file: File, submission: Submission) -> None:
        if file.is_document_ingestion:
            self._enrich_processed_data_with_submission_level_extracted_data(file)

        parent_classification: ClassificationDocumentType | None = None
        parent_file_type: FileType | None = None
        if file.parent_file_id and submission and submission.files:
            parent_file = next((f for f in submission.files if f.id == file.parent_file_id), None)
            if parent_file:
                parent_classification = parent_file.classification
                parent_file_type = parent_file.file_type

        current_app.workflows_client.invoke_copilot_workers_facts_matching_and_normalization(
            submission_id=file.submission_id,
            file_id=file.id,
            file_type=file.file_type,
            s3_key=file.s3_key,
            organization_id=file.organization_id,
            classification=file.classification,
            parent_file_id=file.parent_file_id,
            parent_classification=parent_classification,
            parent_file_type=parent_file_type,
        )

    def _handle_facts_and_suggestions_resolution_finished(self, file: File) -> None:
        if (
            file.classification not in ClassificationDocumentType.pds_classifications()
            or file.initial_processing_state
            in [FileProcessingState.PROCESSING_FAILED, FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING]
        ):
            if file.processed_file and file.processed_file.processed_data:
                file.processing_state = FileProcessingState.WAITING_FOR_ENTITY_MAPPING
                db.session.commit()
                return
            else:
                file.processing_state = self._handle_file_without_data(file, file.initial_processing_state)
                db.session.commit()
                return
        else:
            if file.is_document_ingestion:
                if file.classification in ClassificationDocumentType.di_data_consolidation_classifications():
                    file.processing_state = FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION
                    return self._handle_finished_file(file)
                else:
                    file.processing_state = FileProcessingState.PROCESSED
                    db.session.commit()
                    KalepaEventsHandler.send_file_processing_finished_event(
                        submission_id=file.submission_id, file_id=file.id, status=StepFunctionStatus.SUCCESS
                    )
                    return
            if file.classification in ClassificationDocumentType.data_consolidation_classifications():
                file.processing_state = FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION
                return self._handle_finished_file(file)
            else:
                file.processing_state = FileProcessingState.PROCESSED
                db.session.commit()
                KalepaEventsHandler.send_file_processing_finished_event(
                    submission_id=file.submission_id, file_id=file.id, status=StepFunctionStatus.SUCCESS
                )
                return

    @staticmethod
    def _handle_data_consolidation(file, force: bool = False):
        if (
            file.file_type != FileType.ACORD_FORM
            and file.classification not in ClassificationDocumentType.data_consolidation_classifications()
            and not force
        ):
            return
        submission = SubmissionDAO.get_minimal_submission(
            id=file.submission_id,
            include_files=True,
            include_report=True,
            files_additional_fields=["processing_state", "classification", "checksum"],
            report_additional_fields=["organization_id"],
            additional_fields=["report_id", "is_auto_processed", "processing_state"],
        )
        org_settings = FileHandler._get_org_settings(submission)
        files_to_check = submission.files
        if submission.processing_state == SubmissionProcessingState.ENHANCING_SHELL:
            files_to_check = submission.files_for_enhanced_shell_processing()
        elif submission.report.is_document_ingestion:
            files_to_check = submission.files_for_di_consolidation()
        elif not submission.is_auto_processed:
            files_to_check = submission.always_processed_files()
        files_data_extracted = all(
            f.processing_state not in FileProcessingState.data_not_extracted_states()
            for f in files_to_check
            if f.classification in ClassificationDocumentType.data_consolidation_classifications()
        )
        all_files_classified_if_required = (
            not (submission.is_auto_processed or org_settings.classify_files_for_shells)
        ) or submission.all_files_classified
        if all_files_classified_if_required and files_data_extracted:
            log = logger.bind(submission_id=submission.id)
            log.info("All files data extracted, ready for data consolidation.")
            lock_name = get_consolidation_lock_name(submission.id)
            try:
                log.info("Trying to acquire Consolidate Data Lock", lock_name=lock_name)
                with redis_lock.Lock(current_app.locks_client, name=lock_name, expire=60):
                    log.info("Acquired Consolidate Data Lock", lock_name=lock_name)
                    consolidation_process = submission.create_consolidation_process()
                    handle_existing_consolidation_process(consolidation_process)
                    if consolidation_process.status == SubmissionConsolidationStatus.CANCELLED:
                        log.info("Consolidation process cancelled because there is already pending one.")
                        return
                    consolidate_submission_level_data(submission, consolidation_process)
                    fni = consolidate_acords_and_em_files(submission, consolidation_process)
                    FinancialStatementConsolidator(submission, consolidation_process).consolidate(fni)
                    db.session.add(consolidation_process)
                    maybe_finish_consolidation_process(submission, consolidation_process)
                    db.session.commit()
            except redis_lock.NotAcquired:
                log.warning("Lock already expired", lock_name=lock_name)
            except Exception as e:
                db.session.rollback()
                log.exception("Failed to consolidate acord data", e=str(e))

    def handle(self, file_id: UUID, submission_id: UUID) -> None:
        if not file_id:
            if not submission_id:
                logger.warning("At least one of file_id and submission_id should be provided")
            submission = SubmissionDAO.get_minimal_submission(
                id=submission_id,
                include_report=True,
                additional_fields=["is_auto_processed", "created_at", "report_id"],
                report_additional_fields=["additional_data", "shadow_type", "organization_id"],
                include_files=True,
                files_additional_fields=[
                    "processing_state",
                    "classification",
                    "user_file_type",
                    "submission_id",
                    "file_type",
                    "user_id",
                    "parent_file_id",
                ],
            )
            if file_waiting_for_consolidation := next(
                (
                    f
                    for f in submission.files
                    if f.processing_state == FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION
                ),
                None,
            ):
                self._handle_data_consolidation(file_waiting_for_consolidation, True)
            is_document_ingestion = any(f.is_document_ingestion for f in submission.files)
            self._check_all_files_ready(submission, is_document_ingestion)
            return
        file = FileDAO.get_minimal_file(
            id_=file_id,
            include_processed_file=True,
            additional_fields=["*"],
        )
        self.handle_file(file)

    def handle_file(self, file: File, submission: Submission = None) -> None:
        if not file:
            logger.info("File was removed!")
            return
        logger.info("Handling file", file_id=file.id, processing_state=file.processing_state)
        if file.processing_state == FileProcessingState.NOT_CLASSIFIED and submission:
            self.handle_new_file_added(submission, file)
            return
        if file.processing_state in FileProcessingState.finished_classification_states():
            self._handle_file_classified(file=file)
            return
        if file.processing_state == FileProcessingState.PROCESSING:
            logger.info("File is still processing", file_id=file.id)
            return
        if file.processing_state == FileProcessingState.WAITING_FOR_PROCESSING:
            logger.info("File is waiting for processing", file_id=file.id)
            return

        if file.processing_state == FileProcessingState.DATA_CONSOLIDATED:
            # This means that we just processed event for another accord and consolidated them. Nothing more to do
            # with this one it is waiting for auto resolution and the event should have already been sent.
            return

        if file.processing_state == FileProcessingState.WAITING_FOR_FACTS_AND_SUGGESTIONS_RESOLUTION:
            self._handle_waiting_for_facts_and_suggestions_resolution(file=file, submission=submission)
            return

        if file.processing_state == FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED:
            self._handle_facts_and_suggestions_resolution_finished(file)
            return

        return self._handle_finished_file(file)

    def _handle_finished_file(self, file: File) -> None:
        potential_states_for_data_consolidation = {
            FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            FileProcessingState.CACHE_PROCESSED,
            FileProcessingState.PROCESSING_FAILED,
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
            FileProcessingState.IGNORED,  # ACORD form can be ignored and we need to trigger consolidation if needed
        }
        if file.processing_state in potential_states_for_data_consolidation:
            initial_file_state = file.processing_state
            # When the file is failed it might be a file that failed classification
            force = True if file.processing_state == FileProcessingState.PROCESSING_FAILED else False
            # lock to prevent race condition for 2 FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED events
            lock_name = f"handle_consolidation_for_submission_{file.submission_id}"
            with redis_lock.Lock(current_app.locks_client, name=lock_name, expire=300):
                self._handle_data_consolidation(file=file, force=force)
                if initial_file_state == FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION:
                    db.session.commit()
                    return

        if file.processing_state == FileProcessingState.IGNORED:
            self.handle_file_ignored(file.submission, file)
        elif file.processing_state in FileProcessingState.finished_processing_states():
            self._handle_file_processed(file=file)
        elif file.processing_state in FileProcessingState.final_states_without_processed_data():
            self._check_all_files_ready(file.submission, file.is_document_ingestion)
        else:
            # Temporarily until we have both flows we assume that if file is in a state after processing it's processed
            file.processing_state = FileProcessingState.PROCESSED
            self._handle_file_processed(file=file)

    @staticmethod
    def _handle_processing_dependency_for_new_file(file: File, submission: Submission) -> None:
        parent_raw_email_ids = (
            [f.id for f in submission.files if f.file_type == FileType.RAW_EMAIL and f.parent_file_id is None]
            if submission.origin == Origin.API
            else []
        )
        if (
            # For BOSS subs we don't want to break dependency for the RAW_EMAIL files
            (file.parent_file_id is not None and file.parent_file_id not in parent_raw_email_ids)
            or file.id in parent_raw_email_ids
            # If the file is external, then we always want to break the dependency, so that we don't leak data.
            # If it's internal and verified, there is potential race condition if the dependency complete
            # before the shadow, so we want to break the dependency.
            or (file.is_internal and not submission.is_verified)
            # If there is no processing dependency, there is nothing to remove
            or not submission.report.processing_dependencies
        ):
            return
        from copilot.logic.reports import remove_cross_org_processing_dependencies

        remove_cross_org_processing_dependencies(submission.report)

    @staticmethod
    def _handle_missing_data_for_new_file(file: File, submission: Submission) -> None:
        if not submission.missing_data_status:
            return
        if submission.missing_data_status == MissingDataStatus.NO_FILES or (
            submission.missing_data_status in (MissingDataStatus.INVALID_FILES, MissingDataStatus.NO_DATA)
            and not file.name.endswith(tuple(INVALID_FILE_EXTENSIONS))
        ):
            submission.missing_data_status = None
            submission.stuck_reason = None

    @staticmethod
    def _is_file_processed_independently_from_auto_processing(
        submission: Submission, file: File, org_settings: Settings
    ) -> bool:
        enhancing_shell = submission.processing_state == SubmissionProcessingState.ENHANCING_SHELL or (
            submission.is_auto_processed and org_settings.enhanced_shells_enabled
        )
        if_file_processed_for_enhancement_shell = enhancing_shell and submission.is_file_for_enhanced_shell_processing(
            file
        )
        organization_id = submission.report.organization_id
        return (
            Organization.is_file_processed_independently_from_auto_processing(
                organization_id, file.file_type, file.classification
            )
            or if_file_processed_for_enhancement_shell
        ) and submission.origin not in [Origin.SYNC, Origin.COPY]

    @staticmethod
    def _get_org_settings(submission: Submission) -> Settings:
        default_settings = Settings()
        return (
            Settings.query.filter(Settings.organization_id == submission.report.organization_id).first()
            or default_settings
        )

    @staticmethod
    def get_replace_map(currently_replaced_files: list[File]) -> dict[UUID, list[File]]:
        file_replace_map = defaultdict(list)
        for f in currently_replaced_files:
            for replaced_by_file_id in f.replaced_by_file_ids:
                file_replace_map[replaced_by_file_id].append(f)
        return file_replace_map

    @staticmethod
    def handle_file_replaced(
        submission: Submission, replacing_file: File, file_to_replace: File, file_replace_map: dict[UUID, list[File]]
    ) -> None:
        if file_to_replace.is_internal:
            for replaced_file in file_replace_map.get(file_to_replace.id, []):
                updated_replaced_by_file_ids = {
                    fid for fid in replaced_file.replaced_by_file_ids if fid != file_to_replace.id
                }
                updated_replaced_by_file_ids.add(replacing_file.id)
                replaced_file.replaced_by_file_ids = list(updated_replaced_by_file_ids)
                flag_modified(replaced_file, "replaced_by_file_ids")
                db.session.add(replaced_file)
            logger.info(
                "Removing file due to being replaced",
                file_id=file_to_replace.id,
                submission_id=file_to_replace.submission_id,
                is_internal=file_to_replace.is_internal,
            )
            FileHandler.soft_delete_file(submission, file_to_replace)
        else:
            file_to_replace.processing_state = FileProcessingState.REPLACED
            if not (replaced_by_file_ids := file_to_replace.replaced_by_file_ids):
                replaced_by_file_ids = []
            replaced_by_file_ids.append(replacing_file.id)
            file_to_replace.replaced_by_file_ids = replaced_by_file_ids
            flag_modified(file_to_replace, "replaced_by_file_ids")
            db.session.add(file_to_replace)
