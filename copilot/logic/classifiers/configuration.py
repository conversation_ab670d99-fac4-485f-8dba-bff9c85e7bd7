# ruff: noqa: E501
from dataclasses import replace
from uuid import uuid4

from llm_common.models.llm_model import LLMModel
from static_common.enums.classification import (
    ClassifierOutputType,
    ExtractionType,
    InputProcessingType,
)
from static_common.enums.document_type import DocumentTypeID
from static_common.enums.file_type import FileType
from static_common.models.openai import ChatCompletionPrompt, Message

from copilot.logic.classifiers.models import (
    AutoconfigData,
    AutoconfigurationResources,
    LLMGeneratedPhrase,
    LLMPhrases,
)
from copilot.models import (
    ClassifierConfig,
    ClassifierConfigVersion,
    ClassifierToConfigVersion,
    ClassifierVersion,
    CustomizableClassifierV2,
    LLMConfigVersion,
    PhrasesConfigVersion,
    PhrasesWithLLMConfigVersion,
    db,
)

KV_PAIRS_INPUT_TYPES = {FileType.SUPPLEMENTAL_FORM}
IMAGE_INPUT_TYPES = set()
NO_OCR_INPUT_TYPES = {FileType.SUPPLEMENTAL_FORM}

DEFAULT_EXTRACTION_TYPES = [ExtractionType.PHRASES_WITH_LLM]

EXTRACTION_TYPE_BY_OUTPUT_TYPE = {
    ClassifierOutputType.BOOLEAN: [ExtractionType.PHRASES_WITH_LLM, ExtractionType.PHRASES],
}

PREFERRED_MODELS = {
    LLMModel.OPENAI_GPT_4_1,
    LLMModel.OPENAI_GPT_4_1_MINI,
    LLMModel.OPENAI_GPT_4_1_NANO,
    LLMModel.CLAUDE_3_7_SONNET,
    LLMModel.GEMINI_2_0_FLASH,
    LLMModel.GEMINI_2_0_FLASH_LITE,
    LLMModel.TOGETHER_AI_LLAMA_4_MAVERICK,
    LLMModel.TOGETHER_AI_LLAMA_4_SCOUT,
    LLMModel.TOGETHER_AI_DEEPSEEK_V3,
}

INPUT_PROCESSING_TYPE_TO_EXTRACTION_TYPES = {
    InputProcessingType.OCR_TEXT: [ExtractionType.PHRASES, ExtractionType.PHRASES_WITH_LLM],
    InputProcessingType.KV_PAIRS: [ExtractionType.PHRASES, ExtractionType.PHRASES_WITH_LLM],
    InputProcessingType.IMAGE: [ExtractionType.LLM],
}


def configure_classifier(classifier: CustomizableClassifierV2, resources: AutoconfigurationResources | None = None):
    if not resources:
        resources = AutoconfigurationResources.from_app()

    configurator = CustomizableClassifierAutoconfigurator(resources)
    for version in classifier.versions:
        configurator.autoconfigure(classifier, version)


class CustomizableClassifierAutoconfigurator:
    def __init__(self, resources: AutoconfigurationResources):
        self._resources = resources

    def autoconfigure(
        self, classifier: CustomizableClassifierV2, version: ClassifierVersion
    ) -> CustomizableClassifierV2 | None:
        self._resources.log = self._resources.log.bind(classifier_id=classifier.id)

        if not (autoconfig_data := self._build_autoconfiguration_data(classifier, version)):
            self._resources.log.error("Failed to autoconfigure classifier")
            return None

        configs = []

        document_input_types = []
        other_input_types = []
        for input_type in classifier.input_types:
            if DocumentTypeID.try_parse_str(input_type):
                document_input_types.append(input_type)
            else:
                other_input_types.append([input_type])

        for input_types in [*other_input_types, document_input_types]:
            if config := self._autoconfigure_input_types(autoconfig_data, input_types):
                configs.append(config)

        version.configs.extend(configs)

        db.session.flush()

        return classifier

    def _build_autoconfiguration_data(
        self, classifier: CustomizableClassifierV2, version: ClassifierVersion
    ) -> AutoconfigData | None:
        description = None
        if fact_subtype := self._resources.fact_subtypes.get(classifier.fact_subtype_id):
            description = fact_subtype.description
        description = description or version.classifier_description
        self._resources.log = self._resources.log.bind(description=description)

        phrases = self._create_phrases(description)

        if not phrases:
            self._resources.log.error("No phrases created during autoconfiguration")
            return None
        else:
            self._resources.log.info("Phrases created during autoconfiguration", phrases_count=len(phrases))

        if not (prompt := self._create_prompt(description)):
            self._resources.log.warning("No prompt created during autoconfiguration, cannot autoconfigure")
            return None

        return AutoconfigData(
            classifier=classifier, version=version, description=description, phrases=phrases, prompt=prompt
        )

    def _create_phrases(self, description: str) -> list[LLMGeneratedPhrase]:
        generation_prompt_message = f"""
            You are an AI assistant responsible for generating phrases for a data extraction pipeline. Your task is to create a set of phrases that will be used to match and extract specific values from text. Each phrase you generate should be designed to identify and extract the value described in the input.

            You will be given a description of the value to be extracted. Here is the value description:

            <value_description>
            {description}
            </value_description>

            Based on this description, you need to generate a list of phrases. Each phrase consists of three parts:

            1. phrase: A string that will be matched in the text
            2. exclude: A list of strings which, if matched, should invalidate the phrase
            3. weight: A number indicating how important the phrase is in the matching process (use a scale of 0-1, where 1 is most important)

            When generating phrases, follow these guidelines:

            1. Create at least 3 unique phrases, but no more than 10.
            2. Phrases should be specific enough to accurately identify the desired value, but general enough to catch variations.
            3. Include common synonyms or alternative wordings in your phrases.
            4. Consider potential contexts where the value might appear and create phrases accordingly.
            5. Use the 'exclude' list to eliminate false positives or ambiguous matches.
            6. Assign higher weights to more specific or reliable phrases, and lower weights to more general ones.

            Here are two examples of good outputs:

            Example 1 (for extracting a person's age):
            phrase: age of
            exclude: stone age, ice age, dark age
            weight: 0.7

            phrase: years old
            exclude: years older, years older than
            weight: 0.9

            Example 2 (for extracting a company's revenue):
            phrase: annual revenue of
            exclude: industry revenue, market revenue
            weight: 0.8

            phrase: reported earnings of
            exclude: reported earnings per share
            weight: 0.7

            Now, based on the value description provided, generate a list of phrases following the guidelines and format described above.
        """
        generation_prompt = ChatCompletionPrompt(messages=[Message(role="user", content=generation_prompt_message)])
        params = [replace(p, output_model_json=LLMPhrases, return_json=True) for p in self._resources.llm_params]

        try:
            return self._resources.llm_client.get_llm_response(params, generation_prompt).phrases
        except Exception:
            self._resources.log.exception("Failed to generate classifier phrases")
            return []

    def _create_prompt(self, description: str) -> str | None:
        generation_prompt_message = f"""
            You are tasked with automatically generating a question that will be used to extract specific datapoints from text samples.
            This question will be part of a larger prompt for an LLM-based system. Your goal is to create a clear, concise, and effective question based on the given description of the value being extracted.

            You will be provided with a description of the value to be extracted. Here is the value description:

            <value_description>
            {description}
            </value_description>

            To generate an effective question, follow these guidelines:
            1. Start with a clear interrogative (e.g., "What", "How", "When", "Where", "Who")
            2. Be specific about the information you're seeking
            3. Use language that directly relates to the value description
            4. Keep the question concise and to the point
            5. Avoid ambiguity or vagueness
            6. Ensure the question can be answered using the information typically found in text samples

            Your output should be a single question.

            Here are examples of good and bad questions:

            Good:
            - Value description: The total revenue of the company for the most recent fiscal year
            - Generated question: What was the company's total revenue for the most recent fiscal year?

            Bad:
            - Value description: The total revenue of the company for the most recent fiscal year
            - Generated question: Can you tell me about the company's financial performance?

            The bad question is too vague and doesn't specifically ask for the total revenue or specify the time period.

            Now, based on the provided value description, generate a clear and effective question that will help extract the desired information from text samples.
        """

        generation_prompt = ChatCompletionPrompt(messages=[Message(role="user", content=generation_prompt_message)])

        try:
            return self._resources.llm_client.get_llm_response(self._resources.llm_params, generation_prompt)
        except Exception:
            self._resources.log.exception("Failed to generate classifier prompt")
            return None

    def _autoconfigure_input_types(
        self, autoconfig_data: AutoconfigData, input_types: list[str]
    ) -> ClassifierConfig | None:
        log = self._resources.log.bind(input_types=input_types)

        existing_config = next((c for c in autoconfig_data.version.configs if input_types == c.input_types), None)

        if existing_config:
            # remove mappings to configs without version if present
            ClassifierToConfigVersion.query.filter_by(
                classifier_version_id=autoconfig_data.version.id,
                classifier_config_id=existing_config.id,
                classifier_config_version_id=None,
            ).delete()

        if existing_config and existing_config.versions:
            log.info("Configuration is already present for input type, skipping autoconfiguration")
            return None

        parsed_input_types = [
            FileType.try_parse_str(input_type) or DocumentTypeID.try_parse_str(input_type) or input_type
            for input_type in input_types
        ]

        if not (applicable_input_processing_types := self._get_applicable_input_processing_types(parsed_input_types)):
            log.warning("No applicable input processing types found, cannot autoconfigure")
            return None

        log.info(
            "Applicable input processing types", applicable_input_processing_types=applicable_input_processing_types
        )

        config = existing_config or ClassifierConfig(id=uuid4(), input_types=input_types)
        db.session.add(config)
        config.versions = self._get_config_versions(autoconfig_data, config, applicable_input_processing_types)

        return config

    def _get_applicable_input_processing_types(
        self, parsed_input_types: list[FileType | DocumentTypeID | str]
    ) -> list[InputProcessingType]:
        applicable_input_processing_types = []

        if any(it in KV_PAIRS_INPUT_TYPES for it in parsed_input_types):
            applicable_input_processing_types.append(InputProcessingType.KV_PAIRS)

        if any(it in IMAGE_INPUT_TYPES for it in parsed_input_types):
            applicable_input_processing_types.append(InputProcessingType.IMAGE)

        if any(it not in NO_OCR_INPUT_TYPES for it in parsed_input_types):
            applicable_input_processing_types.append(InputProcessingType.OCR_TEXT)

        return applicable_input_processing_types

    def _get_config_versions(
        self,
        autoconfig_data: AutoconfigData,
        config: ClassifierConfig,
        input_processing_types: list[InputProcessingType],
    ) -> list[ClassifierConfigVersion]:
        versions = []
        for input_processing_type in input_processing_types:
            for extraction_type in INPUT_PROCESSING_TYPE_TO_EXTRACTION_TYPES[input_processing_type]:
                if extraction_type not in EXTRACTION_TYPE_BY_OUTPUT_TYPE.get(
                    autoconfig_data.classifier.output_type, DEFAULT_EXTRACTION_TYPES
                ):
                    continue

                if extraction_type == ExtractionType.LLM:
                    versions.extend(
                        [
                            LLMConfigVersion(
                                id=uuid4(),
                                classifier_config_id=config.id,
                                input_processing_type=input_processing_type,
                                extraction_type=extraction_type,
                                is_autogenerated=True,
                                llm_model=model,
                                prompt=autoconfig_data.prompt,
                            )
                            for model in PREFERRED_MODELS
                        ]
                    )
                elif extraction_type == ExtractionType.PHRASES:
                    id = uuid4()
                    versions.append(
                        PhrasesConfigVersion(
                            id=id,
                            classifier_config_id=config.id,
                            input_processing_type=input_processing_type,
                            extraction_type=extraction_type,
                            is_autogenerated=True,
                            phrases=[p.to_classifier_phrase(id) for p in autoconfig_data.phrases],
                        )
                    )
                elif extraction_type == ExtractionType.PHRASES_WITH_LLM:
                    for model in PREFERRED_MODELS:
                        id = uuid4()
                        versions.append(
                            PhrasesWithLLMConfigVersion(
                                id=id,
                                classifier_config_id=config.id,
                                input_processing_type=input_processing_type,
                                extraction_type=extraction_type,
                                is_autogenerated=True,
                                llm_model=model,
                                prompt=autoconfig_data.prompt,
                                phrases=[p.to_classifier_phrase(id) for p in autoconfig_data.phrases],
                            )
                        )

        version_links = [
            ClassifierToConfigVersion(
                classifier_version_id=autoconfig_data.version.id,
                classifier_config_id=config.id,
                classifier_config_version_id=version.id,
            )
            for version in versions
        ]

        db.session.add_all(versions)
        db.session.flush()
        db.session.add_all(version_links)

        return versions
