from collections import defaultdict
from collections.abc import Callable, Sequence
from dataclasses import asdict, dataclass, field, replace
from random import random
from typing import Any
from uuid import UUID
import ast

from common.logic.identification.fact_value_resolution import get_unit, get_value_type
from common.logic.identification.legal_entity import is_name_legal_entity
from datascience_common.fact_subtypes.suggestion.field_value_normalization import (
    ValueSuggestionResult,
)
from facts_client_v2.model.fact_subtype import FactSubtype
from flask import current_app
from infrastructure_common.logging import get_logger
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType, FirstPartyFieldsGroupType
from static_common.enums.group import GroupID
from static_common.enums.parent import ParentType
from static_common.enums.shareholder_type import ShareholderType
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
    SubmissionBusinessEntityType,
)
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.business_resolution_data import BusinessResolutionData
from static_common.models.file_onboarding import (
    AcordLocationInformation,
    AdditionalData,
    OnboardedFile,
    ResolvedDataField,
    ResolvedDataValue,
    SubmissionEntity,
    SuggestedField,
)
from static_common.models.first_party import (
    FirstPartyField,
    FirstPartyFieldEntity,
    FirstPartyFields,
    FirstPartyFieldValue,
)
from static_common.schemas.business_resolution_data import BusinessResolutionDataSchema
from static_common.schemas.file_onboarding import (
    LeanOnboardedFileSchema,
    ShareholderInformationSchema,
)
from static_common.schemas.first_party import (
    FirstPartyFieldsGroup,
    FirstPartyFieldsSchema,
)
from static_common.taxonomies.industry_classification import NaicsCode

from copilot.constants import BUSINESS_GROUPS, DUMMY_ID
from copilot.exceptions import (
    BadLocations,
    EntityNotInERS,
    NoEntityAssociated,
    UnconfirmedLocations,
    UnsupportedProcessedDataFormat,
)
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.entity_deduplicator import EntityDeduplicator
from copilot.logic.facts_identification import suggest_value_for_fact_subtype
from copilot.logic.files_entity_merger import FilesEntityMerger
from copilot.logic.pds.file_source_resolver import FileSourceResolver
from copilot.models import db
from copilot.models.files import ProcessedFile
from copilot.models.files_merging import MergeDataRequest
from copilot.models.reports import Submission, SubmissionBusiness
from copilot.models.shareholders import Shareholder
from copilot.utils import convert_to_uuid, flatten, is_valid_uuid

logger = get_logger()
onboarded_file_schema = LeanOnboardedFileSchema()


@dataclass
class OnboardedFileData:
    fields: dict[str, ResolvedDataField] = field(default_factory=dict)
    entity_information: dict[str, ResolvedDataField] = field(default_factory=dict)
    entities: list[SubmissionEntity] = field(default_factory=list)
    entity_ids_map: dict[int, int] = field(default_factory=dict)
    files: list[UUID] = field(default_factory=list)


@dataclass
class ProcessedFileWithClassification:
    data: ProcessedFile
    classification: ClassificationDocumentType


FILES_DATA_WEIGHT = {
    ClassificationDocumentType.ACORD_125: 100,
    ClassificationDocumentType.ACORD_126: 100,
    ClassificationDocumentType.ACORD_130: 100,
    ClassificationDocumentType.ACORD_131: 100,
    ClassificationDocumentType.ACORD_140: 100,
    ClassificationDocumentType.ACORD_823: 100,
    ClassificationDocumentType.APPLIED_130: 100,
    ClassificationDocumentType.ACORD_211: 100,
    ClassificationDocumentType.APPLIED_126: 100,
    ClassificationDocumentType.APPLIED_125: 100,
    ClassificationDocumentType.OFAPPINFCNI: 100,
    "default": 0,
}

ENTITY_ROLE_MAP = {
    SubmissionEntityType.GENERAL_CONTRACTOR: SubmissionBusinessEntityRole.GENERAL_CONTRACTOR,
    SubmissionEntityType.PROJECT: SubmissionBusinessEntityRole.PROJECT,
}

FACT_SUBTYPES_EXCLUDED_FROM_DEDUPLICATION = {
    FactSubtypeID.VEHICLE_INFORMATION_NUMBER,
}


def _get_fact_subtypes() -> dict[str, FactSubtype]:
    return current_app.facts_client_v2.get_fact_subtypes_as_dict()


def calculate_entity_id(name: str | None, address: str | None) -> str:
    name = (name or "").lower().strip()
    address = (address or "").lower().strip()
    return f"{name} {address}".strip()


def _append_or_create_entity_information_field(
    entity_information: dict[str, ResolvedDataField],
    data_value: ResolvedDataValue,
    field_name: str,
    field_type: FieldType,
) -> None:
    if field_name in entity_information:
        entity_information[field_name].values.append(data_value)
    else:
        entity_information[field_name] = ResolvedDataField(
            name=field_name,
            values=[data_value],
            value_type=field_type,
            display_as_fact=False,
        )


def _create_fields_for_first_party_entity(
    entity_information: dict[str, ResolvedDataField], entity: FirstPartyFieldEntity, entity_idx: int, file_idx: int
) -> None:
    if entity.requested_name:
        data_value = ResolvedDataValue(
            value=entity.requested_name,
            entity_idx=entity_idx,
            file_idx=file_idx,
        )
        field_name = EntityFieldID.NAME.value
        field_type = FieldType.TEXT
        _append_or_create_entity_information_field(entity_information, data_value, field_name, field_type)
    if entity.requested_address:
        data_value = ResolvedDataValue(
            value=entity.requested_address,
            entity_idx=entity_idx,
            file_idx=file_idx,
        )
        field_name = EntityFieldID.ADDRESS.value
        field_type = FieldType.TEXT
        _append_or_create_entity_information_field(entity_information, data_value, field_name, field_type)
    if getattr(entity, "requested_name_aliases", None):
        data_value = ResolvedDataValue(
            value=entity.requested_name_aliases,
            entity_idx=entity_idx,
            file_idx=file_idx,
        )
        field_name = EntityFieldID.NAME_ALIASES.value
        field_type = FieldType.TEXT_ARRAY
        _append_or_create_entity_information_field(entity_information, data_value, field_name, field_type)


def _create_new_entity(entity: FirstPartyFieldEntity | FirstPartyFieldValue) -> SubmissionEntity | None:
    if (
        entity.parent_type is None
        and entity.requested_name is None
        and entity.requested_address is None
        and entity.parent_id is None
    ):
        return None
    entity_type = (
        SubmissionEntityType[entity.parent_type]
        if entity.parent_type in SubmissionEntityType.get_all()
        else SubmissionEntityType.BUSINESS
    )
    location_info = None
    if fp_location_info := getattr(entity, "acord_location_information", None):
        location_info = AcordLocationInformation(
            location_number=fp_location_info.location_number, building_number=fp_location_info.building_number
        )

    if entity.parent_id:
        entity_id = str(entity.parent_id)
    else:
        entity_id = calculate_entity_id(entity.requested_name, entity.requested_address)
    return SubmissionEntity(
        type=entity_type,
        entity_role=getattr(entity, "entity_role", None),
        entity_named_insured=getattr(entity, "entity_named_insured", None),
        id=entity_id,
        resolved=False,
        remote_id=entity.remote_id,
        acord_location_information=location_info,
    )


def _update_entities_ids(
    new_names: list[ResolvedDataValue], entity_informations: list[ResolvedDataField], entities: list[SubmissionEntity]
) -> None:
    address_entity_information = next((x for x in entity_informations if x.name == EntityFieldID.ADDRESS.value), None)
    if not address_entity_information:
        return
    idx_to_address = {x.entity_idx: x.value for x in address_entity_information.values}
    idx_to_id = {}
    for name in new_names:
        if name.entity_idx not in idx_to_address:
            continue
        idx_to_id[name.entity_idx] = calculate_entity_id(name.value, idx_to_address[name.entity_idx])

    for idx, entity in enumerate(entities):
        if idx in idx_to_id:
            if not is_valid_uuid(entity.id):
                entity.id = idx_to_id[idx]
            entity.remote_id = idx_to_id[idx]


def _maybe_add_name_from_aliases(
    entity_information_values: list[ResolvedDataField], entities: list[SubmissionEntity]
) -> None:
    name_aliases_entity_information = next(
        (x for x in entity_information_values if x.name == EntityFieldID.NAME_ALIASES.value), None
    )
    if not name_aliases_entity_information:
        return

    name_entity_information = next((x for x in entity_information_values if x.name == EntityFieldID.NAME.value), None)
    name_values = []
    should_remove_aliases = True
    if not name_entity_information:
        for resolved_data_value in name_aliases_entity_information.values:
            if not resolved_data_value.value:
                continue
            if len(resolved_data_value.value) > 1:
                should_remove_aliases = False
            name_values.append(
                ResolvedDataValue(
                    value=resolved_data_value.value.pop(0),
                    entity_idx=resolved_data_value.entity_idx,
                    file_idx=resolved_data_value.file_idx,
                )
            )
        if name_values:
            entity_information_values.append(
                ResolvedDataField(
                    name=EntityFieldID.NAME.value,
                    values=name_values,
                    value_type=FieldType.TEXT,
                    display_as_fact=False,
                )
            )
    else:
        indices_with_names = [
            name_value.entity_idx for name_value in name_entity_information.values if name_value.value
        ]
        for resolved_data_value in name_aliases_entity_information.values:
            if not resolved_data_value.value:
                continue
            if resolved_data_value.entity_idx in indices_with_names:
                should_remove_aliases = False
                continue
            if len(resolved_data_value.value) > 1:
                should_remove_aliases = False
            name_values.append(
                ResolvedDataValue(
                    value=resolved_data_value.value.pop(0),
                    entity_idx=resolved_data_value.entity_idx,
                    file_idx=resolved_data_value.file_idx,
                )
            )
        if name_values:
            name_entity_information.values.extend(name_values)
    if should_remove_aliases:
        entity_information_values.remove(name_aliases_entity_information)
    if name_values:
        _update_entities_ids(name_values, entity_information_values, entities)


def first_party_fields_loader(data: dict, file_id: UUID) -> OnboardedFile:
    log = logger.bind(file_id=file_id)
    data_obj = FirstPartyFieldsSchema().load(data)
    entities = []
    entity_id_to_idx: dict[str, int] = {}
    entity_information: dict[str, ResolvedDataField] = {}
    fields: dict[str, ResolvedDataField] = {}
    files = [file_id]
    file_idx = 0
    fact_subtypes = _get_fact_subtypes()
    # The assumption here is that there is data in exactly one of the groups
    for group in data_obj.fields_groups:
        for entity in group.entities:
            if not (submission_entity := _create_new_entity(entity)):
                continue
            parent_entity_id = calculate_entity_id(entity.requested_name, entity.requested_address)
            if (
                parent_entity_id in entity_id_to_idx
                and submission_entity.type in SubmissionEntityType.entities_with_business_parent_type()
            ):
                submission_entity.parent_idx = entity_id_to_idx.get(parent_entity_id)
                # always add business entities.
            entity_idx = len(entities)
            entities.append(submission_entity)
            if submission_entity.id not in entity_id_to_idx:
                entity_id_to_idx[submission_entity.id] = entity_idx
            _create_fields_for_first_party_entity(entity_information, entity, entity_idx, file_idx)

        for field_ in group.fields:
            resolved_values = []
            for value in field_.values:
                if value.parent_id:
                    entity_id = str(value.parent_id)
                    if entity_id not in entity_id_to_idx:
                        parent_entity_id = calculate_entity_id(value.requested_name, value.requested_address)
                        if submission_entity := _create_new_entity(value):
                            if parent_entity_id in entity_id_to_idx:
                                submission_entity.parent_idx = entity_id_to_idx.get(parent_entity_id)
                            else:
                                log.error(
                                    "Unrecognised entity in file",
                                    field=field_.name,
                                    value=value,
                                )
                            entity_idx = len(entities)
                            entity_id_to_idx[submission_entity.id] = entity_idx
                            entities.append(submission_entity)
                            _create_fields_for_first_party_entity(entity_information, value, entity_idx, file_idx)
                else:
                    entity_id = calculate_entity_id(value.requested_name, value.requested_address)
                entity_idx = entity_id_to_idx.get(entity_id)
                resolved_values.append(
                    ResolvedDataValue(
                        value=value.value,
                        entity_idx=entity_idx,
                        file_idx=file_idx,
                        explanations=value.explanations,
                        observed_name=field_.name,
                        evidences=value.evidences,
                    )
                )
            name = field_.name
            fact_subtype_id = None
            naics_code_id = None
            if fact_subtype := _get_fact_subtype(field_.fact_subtype_id or field_.name, fact_subtypes):
                name = fact_subtype.get("display_name") or field_.name
                fsid = fact_subtype.get("id")
                fact_subtype_id = FactSubtypeID.try_parse_str(fsid)
                naics_code_id = NaicsCode.try_parse_str(fsid)
            if name in fields:
                fields[name].values += resolved_values
            else:
                value_types = get_value_type(fact_subtype)
                fields[name] = ResolvedDataField(
                    name=name,
                    values=resolved_values,
                    value_type=value_types[0] if value_types else FieldType.TEXT,
                    display_as_fact=fact_subtype_id is not None or naics_code_id is not None,
                    fact_subtype_id=fact_subtype_id,
                    naics_code=naics_code_id,
                    unit=get_unit(fact_subtype),
                )
    entity_information_values = list(entity_information.values())
    _maybe_add_name_from_aliases(entity_information_values, entities)
    return OnboardedFile(
        fields=list(fields.values()),
        entity_information=entity_information_values,
        entities=entities,
        files=files,
    )


def _get_fact_subtype(name: str, fact_subtypes: dict[str, FactSubtype]) -> FactSubtype | None:
    return fact_subtypes.get(name)


def onboarded_file_loader(data: dict, file_id: UUID) -> OnboardedFile:
    for e in data.get("entities", []):
        e["entity_role"] = e.get("entity_role") or ENTITY_ROLE_MAP.get(e.get("type"))
    return onboarded_file_schema.load(data)


def recalculate_entity_id(onboarded_data: OnboardedFile, entity_idx: int) -> str:
    name = None
    address = None
    for field_ in onboarded_data.entity_information:
        if field_.name == "Name":
            name = next((value.value for value in field_.values if value.entity_idx == entity_idx), None)
        if field_.name == "Address":
            address = next((value.value for value in field_.values if value.entity_idx == entity_idx), None)
    return calculate_entity_id(name, address)


def _get_field_key(field_: ResolvedDataField, entities: Sequence[SubmissionEntity]) -> str:
    """
    During processing we have tried to resolve the field name to a fact subtype.
    If we haven't there is a good reason for that, hence we don't want to merge here fact subtype with name.

    For example if we have field "Driver Name" in VEHICLE and DRIVER file
    we do not want to merge them despite the same name.
    """

    entity_types: list[SubmissionEntityType] = [
        entities[x.entity_idx].type for x in field_.values if x.entity_idx is not None
    ]
    distinct_type: str = "Unknown"
    if len(entity_types) == 0:
        distinct_type = "Non-Fleet"
    elif all(x == SubmissionEntityType.EQUIPMENT for x in entity_types):
        distinct_type = "Equipment"
    elif all(x == SubmissionEntityType.DRIVER for x in entity_types):
        distinct_type = "Driver"
    elif all(x == SubmissionEntityType.VEHICLE for x in entity_types):
        distinct_type = "Vehicle"
    elif all(
        x not in {SubmissionEntityType.DRIVER, SubmissionEntityType.VEHICLE, SubmissionEntityType.EQUIPMENT}
        for x in entity_types
    ):
        distinct_type = "Non-Fleet"
    else:
        unique_entity_types = set(entity_types)
        unique_entity_type_to_first_field_appearance: dict[str, ResolvedDataField] = {
            entity_type: next((x for x in field_.values if entities[x.entity_idx].type == entity_type), None)
            for entity_type in unique_entity_types
        }
        logger.error(
            "Unexpected entity_types",
            entity_types=entity_types,
            field_name=field_.name,
            field_fact_subtype_id=field_.fact_subtype_id,
            field_values=[(x.value, x.observed_value, x.entity_idx) for x in field_.values],
            entities=entities,
            unique_entity_types=unique_entity_types,
            unique_entity_type_to_first_field_appereance=unique_entity_type_to_first_field_appearance,
        )

    if field_.fact_subtype_id:
        return f"fact-{field_.fact_subtype_id}"
    return f"name-{field_.name}-{distinct_type}"


def _convert_list_to_tuples(value: Any) -> Any:
    if isinstance(value, list):
        return tuple(_convert_list_to_tuples(item) for item in value)
    else:
        return value


def _remove_redundant_empty_values(field: ResolvedDataField, allow_multiple_observations: bool = False) -> None:
    entity_to_resolved_values: dict[str, list] = {}
    entity_to_values: dict[str, set] = {}
    for value in field.values:
        key = (
            f"{value.entity_idx}-{value.related_entity_idx}"
            if value.related_entity_idx is not None
            else str(value.entity_idx)
        )
        hashable_value = _convert_list_to_tuples(value.value)
        if entity_to_resolved_values.get(key):
            if not entity_to_resolved_values[key][0].value:
                entity_to_resolved_values[key] = [value]
                entity_to_values[key] = {hashable_value}
            elif value.value and (
                hashable_value not in entity_to_values[key]
                or field.fact_subtype_id in FACT_SUBTYPES_EXCLUDED_FROM_DEDUPLICATION
                or allow_multiple_observations
            ):
                entity_to_resolved_values[key].append(value)
                entity_to_values[key].add(hashable_value)
        else:
            entity_to_resolved_values[key] = [value]
            entity_to_values[key] = {hashable_value}
    field.values = sum(entity_to_resolved_values.values(), [])


def _process_fields(
    old_fields: list[ResolvedDataField],
    old_entities: Sequence[SubmissionEntity],
    entity_ids_to_idx: dict[str, int],
    new_file_idx: int,
    new_fields: dict[str, ResolvedDataField],
    drop_empty_values: bool,
    named_insured_idx: int | None,
    allow_multiple_observations: bool = False,
) -> bool:
    log = logger.bind(file_idx=new_file_idx)
    orphan_values = False
    for field_ in reversed(old_fields):
        field_key = _get_field_key(field_=field_, entities=old_entities)
        for value in reversed(field_.values):
            new_entity_idx = None
            if value.entity_idx is not None:
                entity = old_entities[value.entity_idx]
                if (new_entity_idx := entity_ids_to_idx.get(entity.unique_identifier)) is None:
                    field_.values.remove(value)
                    continue
            else:
                orphan_values = True
                if named_insured_idx is None:
                    log.warning(
                        "There is no entity set for value and there is no FNI. Value will be removed",
                        value=value.value,
                        field_name=field_key,
                    )
                    field_.values.remove(value)
                    continue
                new_entity_idx = named_insured_idx
            value.entity_idx = new_entity_idx
            value.file_idx = new_file_idx
            if value.related_entity_idx is not None:
                related_entity = old_entities[value.related_entity_idx]
                if (new_related_entity_idx := entity_ids_to_idx.get(related_entity.unique_identifier)) is None:
                    log.error(
                        "Related entity was not found. The value will be removed", value=value, field_name=field_key
                    )
                    field_.values.remove(value)
                    continue
                value.related_entity_idx = new_related_entity_idx
        if not field_.values:
            old_fields.remove(field_)
            continue
        if field_key in new_fields:
            new_fields[field_key].values += field_.values
        else:
            new_fields[field_key] = field_
        if drop_empty_values:
            _remove_redundant_empty_values(
                new_fields[field_key], allow_multiple_observations=allow_multiple_observations
            )
    return orphan_values


def _process_suggested_fields(
    old_suggested_fields: list[SuggestedField], new_suggested_fields: dict[str, SuggestedField], file_idx: int
) -> None:
    for suggested_field in old_suggested_fields:
        suggested_field_key = suggested_field.fact_subtype_id or suggested_field.name
        evidences = [replace(evidence, file_idx=file_idx) for evidence in suggested_field.evidences]
        if suggested_field_key in new_suggested_fields:
            new_suggested_fields[suggested_field_key].evidences += evidences
        else:
            new_suggested_fields[suggested_field_key] = replace(suggested_field, evidences=evidences)


def _load_files_and_data(
    files: Sequence[MergeDataRequest],
) -> tuple[list[UUID], dict[UUID, OnboardedFile]]:
    file_ids = []
    data = {}
    for file in files:
        file_ids.append(file.file_id)
        data[file.file_id] = file.data
    return file_ids, data


def _get_primary_insured_entity(entities: list[SubmissionEntity]) -> int | None:
    for idx, entity in enumerate(entities):
        if (
            entity.entity_named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
            or entity.type == SubmissionEntityType.PRIMARY_INSURED
        ):
            return idx
    return None


def _create_primary_insured_entity() -> SubmissionEntity:
    return SubmissionEntity(
        type=SubmissionEntityType.BUSINESS,
        entity_named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
        id=DUMMY_ID,
        resolved=False,
    )


def _filter_suggested_fields(
    suggested_fields: dict[str, SuggestedField], processed_fields: list[ResolvedDataField]
) -> dict[str, SuggestedField]:
    # The values should be unique, but checking if item in set is faster.
    existing_factsubtypes = {f.fact_subtype_id for f in processed_fields if f.fact_subtype_id}
    return {
        key: suggested_field
        for key, suggested_field in suggested_fields.items()
        if suggested_field.fact_subtype_id not in existing_factsubtypes
    }


def merge_onboarded_data(
    files: Sequence[MergeDataRequest],
    if_submission_data: bool,
    drop_empty_values: bool,
    merge_entity_values: bool = False,
    create_dummy_fni: bool = True,
    submission_id: UUID | str | None = None,
    allow_multiple_observations: bool = False,
    flatten_single_structures: bool = True,
    keep_project_gc: bool = False,
) -> OnboardedFile:
    if submission_id:
        submission_id = str(submission_id)
    EntityDeduplicator().extract_structures_from_duplicates(files)
    files_entity_merger = FilesEntityMerger(
        submission_id, flatten_single_structures=flatten_single_structures, keep_project_gc=keep_project_gc
    )
    entity_ids_to_idx, entities = files_entity_merger.merge_entities(files, allow_submission_data=if_submission_data)
    file_ids, data = _load_files_and_data(files)

    logger.info("Merging onboarded data", file_ids=file_ids, entities_count=len(entities))

    no_named_insured = False
    named_insured_idx = _get_primary_insured_entity(entities)
    if named_insured_idx is None:
        named_insured_idx = len(entities) if create_dummy_fni else None
        no_named_insured = True

    entity_information: dict[str, ResolvedDataField] = {}
    fields: dict[str, ResolvedDataField] = {}
    suggested_fields: dict[str, SuggestedField] = {}
    need_dummy_entity = False
    for file_id, onboarded_data in data.items():
        file_idx = file_ids.index(file_id)
        need_dummy_entity = (
            _process_fields(
                old_fields=onboarded_data.entity_information,
                old_entities=onboarded_data.entities,
                new_fields=entity_information,
                entity_ids_to_idx=entity_ids_to_idx,
                new_file_idx=file_idx,
                drop_empty_values=drop_empty_values,
                named_insured_idx=named_insured_idx,
                allow_multiple_observations=allow_multiple_observations,
            )
            or need_dummy_entity
        )
        need_dummy_entity = (
            _process_fields(
                old_fields=onboarded_data.fields,
                old_entities=onboarded_data.entities,
                new_fields=fields,
                entity_ids_to_idx=entity_ids_to_idx,
                new_file_idx=file_idx,
                drop_empty_values=drop_empty_values,
                named_insured_idx=named_insured_idx,
                allow_multiple_observations=allow_multiple_observations,
            )
            or need_dummy_entity
        )
        _process_suggested_fields(onboarded_data.additional_data.suggested_fields, suggested_fields, file_idx)
    if need_dummy_entity and no_named_insured and create_dummy_fni:
        entities.append(_create_primary_insured_entity())
    entities, processed_fields, processed_entity_information = files_entity_merger.merge_entities_with_distinct_uuids(
        entities,
        list(fields.values()),
        entity_ids_to_idx,
        list(entity_information.values()),
    )
    suggested_fields = _filter_suggested_fields(suggested_fields, processed_fields)
    merged_onboarded_data = OnboardedFile(
        fields=processed_fields,
        entity_information=processed_entity_information,
        entities=entities,
        files=file_ids,
        additional_data=AdditionalData(suggested_fields=list(suggested_fields.values())),
    )
    if merge_entity_values and not allow_multiple_observations:
        classifications = {file.file_id: file.classification for file in files}
        _remove_duplicate_values(merged_onboarded_data, classifications)
    return merged_onboarded_data


def _remove_duplicate_values(data: OnboardedFile, classifications: dict[UUID, ClassificationDocumentType]) -> None:
    # When merging onboarded data with data from automatically processed files, there might be duplicate values.
    # To resolve those conflicts we use FILES_DATA_WEIGHT and take the values from files with classification that
    # has higher weight. If there is no weight for given classification we use the default one.
    default_weight = FILES_DATA_WEIGHT["default"]
    for field_ in data.fields:
        entity_to_values = {}
        for value in field_.values:
            if current_value := entity_to_values.get(value.entity_idx):
                current_value_weight = FILES_DATA_WEIGHT.get(
                    classifications[data.files[current_value.file_idx]], default_weight
                )
                new_value_weight = FILES_DATA_WEIGHT.get(classifications[data.files[value.file_idx]], default_weight)
                if new_value_weight > current_value_weight:
                    entity_to_values[value.entity_idx] = value
            else:
                entity_to_values[value.entity_idx] = value
        field_.values = list(entity_to_values.values())


def _map_entity_information_to_entity_idx(onboarded_data: OnboardedFile) -> dict[int, dict[str, list[dict]]]:
    result = defaultdict(lambda: defaultdict(list))
    for entity_information in onboarded_data.entity_information:
        entity_info_without_values = {k: v for k, v in asdict(entity_information).items() if k != "values"}
        for value in entity_information.values:
            idx = value.entity_idx
            key = entity_information.fact_subtype_id or entity_information.name
            result[idx][key].append(
                {
                    **asdict(value),
                    **entity_info_without_values,
                }
            )
    return result


def create_shareholders(
    submission: Submission,
    fni: SubmissionBusiness | None,
    onboarded_data: OnboardedFile,
    file_id_to_classification: dict[UUID, ClassificationDocumentType],
):
    try:
        ownership_info = next(
            (ei for ei in onboarded_data.entity_information if ei.name == EntityFieldID.OWNERSHIP_INFORMATION.value),
            None,
        )
        if not ownership_info:
            return

        shareholders_from_nis_files = defaultdict(list)
        shareholders_from_other_file = defaultdict(list)
        for ownership_info_value in ownership_info.values:
            file_id = onboarded_data.files[ownership_info_value.file_idx]
            if file_id_to_classification.get(file_id) in [
                ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
                ClassificationDocumentType.NAMED_INSURED_SCHEDULE_PDF,
            ]:
                _add_shareholders_for_entity(
                    onboarded_data, ownership_info_value, submission, fni, file_id, shareholders_from_nis_files
                )
            else:
                _add_shareholders_for_entity(
                    onboarded_data, ownership_info_value, submission, fni, file_id, shareholders_from_other_file
                )

        shareholders_to_save = []
        for entity_id, shareholders in shareholders_from_other_file.items():
            if entity_id in shareholders_from_nis_files:
                # In the first version we'll merge using name, once we extend the functionality
                # for ERS we'll use shareholder id
                name_to_shareholder_from_nis = {s.shareholder_name: s for s in shareholders_from_nis_files[entity_id]}
                for shareholder in shareholders:
                    if shareholder.shareholder_name not in name_to_shareholder_from_nis:
                        logger.warning(
                            "Shareholder from other file not found in NIS for the same entity",
                            shareholder=shareholder,
                            entity_id=entity_id,
                        )
                        continue
                    if shareholder.is_director_or_board_member is not None:
                        name_to_shareholder_from_nis[shareholder.shareholder_name].is_director_or_board_member = (
                            shareholder.is_director_or_board_member
                        )
                    if shareholder.shareholder_type is not None:
                        name_to_shareholder_from_nis[shareholder.shareholder_name].shareholder_type = (
                            shareholder.shareholder_type
                        )
            else:
                shareholders_to_save.extend(shareholders)
        for shareholders in shareholders_from_nis_files.values():
            shareholders_to_save.extend(shareholders)
        db.session.add_all(shareholders_to_save)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger.exception("Error while creating shareholders", e=e)


def _add_shareholders_for_entity(
    onboarded_data: OnboardedFile,
    ownership_info_value: ResolvedDataValue,
    submission: Submission,
    fni: SubmissionBusiness | None,
    file_id: UUID,
    shareholders_per_file: dict[str, list],
):
    if not ownership_info_value.value:
        return
    entity = onboarded_data.entities[ownership_info_value.entity_idx]
    if not (entity.resolved and is_valid_uuid(entity.id)):
        return
    # In the first version we want to keep only the shareholders from the single file per entity
    if entity.id in shareholders_per_file:
        return
    shareholders_list = ShareholderInformationSchema().loads(ownership_info_value.value, many=True)
    for shareholder_info in shareholders_list:
        matching_sub_business = SubmissionBusiness.query.filter(
            SubmissionBusiness.business_id == entity.id,
            SubmissionBusiness.submission_id == submission.id,
        ).first()

        shareholder = Shareholder(
            submission_id=submission.id,
            file_id=file_id,
            shareholder_name=shareholder_info.shareholder_name,
            shareholder_type=shareholder_info.shareholder_type,
            ownership_percentage=shareholder_info.ownership_percentage,
            is_director_or_board_member=shareholder_info.is_director_or_board_member,
            submission_business_id=matching_sub_business.id if matching_sub_business else None,
            submission_business=matching_sub_business,
        )
        if shareholder_info.shareholder_type == ShareholderType.FNI:
            if fni:
                shareholder.shareholder_id = fni.business_id
                shareholder.shareholder_name = fni.requested_name
            elif not shareholder.shareholder_name:
                logger.warning(
                    "Shareholder type is FNI but no FNI found and no shareholder name",
                    shareholder=shareholder,
                    entity_id=entity.id,
                    file_id=file_id,
                )
                continue
        shareholders_per_file[entity.id].append(shareholder)


def handle_missing_submission_businesses(
    missing_submission_businesses: list[SubmissionBusiness],
    submission_id: str | UUID,
    exception_type: type[BadLocations],
):
    db.session.rollback()
    business_ids = []
    requested_names = []
    requested_addresses = []
    for sb in missing_submission_businesses:
        business_ids.append(sb.business_id)
        requested_names.append(sb.requested_name)
        requested_addresses.append(sb.requested_address)
    exception = exception_type(
        business_ids=business_ids, requested_names=requested_names, requested_addresses=requested_addresses
    )
    logger.info(
        exception.msg,
        business_ids=business_ids,
        requested_names=requested_names,
        requested_addresses=requested_addresses,
    )
    from copilot.logic.pds.submission_handler import (
        SubmissionHandler,  # to avoid having circular import
    )

    submission = SubmissionDAO.get_minimal_submission(submission_id)
    submission.is_processing = False
    SubmissionHandler().revert_to_business_confirmation(submission)
    raise exception


def validate_business_existence_in_ers(submission_businesses: dict[int, SubmissionBusiness], submission_id: str):
    business_id_idx = {str(sb.business_id): idx for idx, sb in submission_businesses.items()}
    business_id_to_business = {str(sb.business_id): sb for sb in submission_businesses.values()}

    business_id_entity_dict = current_app.ers_client_v3.get_entities(list(business_id_idx.keys()))
    submission_businesses_not_in_ers = [
        submission_businesses[idx]
        for business_id, idx in business_id_idx.items()
        if str(business_id) not in business_id_entity_dict
    ]
    if submission_businesses_not_in_ers:
        handle_missing_submission_businesses(
            missing_submission_businesses=submission_businesses_not_in_ers,
            submission_id=submission_id,
            exception_type=EntityNotInERS,
        )
    for business_id, entity in business_id_entity_dict.items():
        if business_id not in business_id_to_business:
            continue
        submission_business = business_id_to_business[business_id]
        submission_business.entity_data = entity


def _handle_advisor_additional_info(
    advisor_type_field: ResolvedDataField | None, idx: int, onboarded_data: OnboardedFile
) -> dict:
    if advisor_type_field is None:
        logger.warning("Advisor type field not found")
        return {}
    advisor_type_value = next((v for v in advisor_type_field.values if v.entity_idx == idx), None)
    if advisor_type_value is None:
        logger.warning("Advisor type value not found")
        return {}
    if advisor_type_value.related_entity_idx is None:
        logger.warning("Advisor type value does not have related entity")
        return {}
    if advisor_type_value.related_entity_idx >= len(onboarded_data.entities):
        logger.warning("Advisor type related entity index out of bounds")
        return {}
    related_entity_type = onboarded_data.entities[advisor_type_value.related_entity_idx].entity_role
    return {
        "advisor_type": advisor_type_value.value,
        "related_entity_type": related_entity_type,
        "advisor_raw_type": advisor_type_value.observed_value,
    }


def _extract_aliases(entity_information_for_idx: dict[str, list[dict]], idx: int, submission_id: str) -> list[str]:
    try:
        aliases_fields = entity_information_for_idx.get(EntityFieldID.NAME_ALIASES.value, [])
        aliases = [ast.literal_eval(a.get("observed_value")) for a in aliases_fields if a.get("observed_value")]
        return list(set(flatten(aliases)))
    except Exception as e:
        logger.error("Error while extracting aliases", exc_info=e, idx=idx, submission_id=submission_id)
        return []


def create_submission_businesses_and_first_party_entities(
    onboarded_data: OnboardedFile,
    submission_id=str,
    file_id_to_classification: dict[UUID, ClassificationDocumentType] = {},
) -> tuple[dict[int, SubmissionBusiness], dict[int, FirstPartyFieldEntity]]:
    submission_businesses = {}
    first_party_entities = {}
    entity_information = _map_entity_information_to_entity_idx(onboarded_data)
    entity_type_map = {
        SubmissionEntityType.GENERAL_CONTRACTOR: SubmissionBusinessEntityType.GENERAL_CONTRACTOR,
        SubmissionEntityType.PROJECT: SubmissionBusinessEntityType.PROJECT,
        SubmissionEntityType.OTHER_INSURED: SubmissionBusinessEntityType.OTHER_INSURED,
        SubmissionEntityType.PRIMARY_INSURED: SubmissionBusinessEntityType.PRIMARY_INSURED,
        SubmissionEntityType.BUSINESS: None,
    }
    named_insured_map = {
        SubmissionEntityType.OTHER_INSURED: SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED,
        SubmissionEntityType.PRIMARY_INSURED: SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    }

    advisor_type_field = next(
        (
            ei
            for ei in onboarded_data.entity_information
            if ei.name == EntityInformation.TRANSACTION_ADVISOR_CATEGORY.value
        ),
        None,
    )

    for idx, entity in enumerate(onboarded_data.entities):
        if entity.type == SubmissionEntityType.SUBMISSION:
            first_party_entities[idx] = FirstPartyFieldEntity(
                requested_name=None,
                requested_address=None,
                parent_type=ParentType.SUBMISSION,
                parent_id=convert_to_uuid(submission_id),
            )
            continue
        entity_information_for_idx = entity_information.get(idx, {})
        names = entity_information_for_idx.get(EntityFieldID.NAME.value, [])
        addresses = entity_information_for_idx.get(EntityFieldID.ADDRESS.value, [])
        mailing_addresses = entity_information_for_idx.get(FactSubtypeID.MAILING_ADDRESSES.value, [])
        file_id_sources_set = set()
        if onboarded_data.files:
            for name_or_address in names + addresses:
                file_id_sources_set.add(onboarded_data.files[name_or_address.get("file_idx")])
        name = next((nam for nam in names if nam["observed_value"]), names[0] if names else {})
        address = next(
            (addr for addr in (addresses or mailing_addresses) if addr["observed_value"]),
            (addresses or mailing_addresses)[0] if (addresses or mailing_addresses) else {},
        )
        requested_name = name.get("observed_value") if entity.resolved else name.get("value")
        requested_address = address.get("observed_value") if entity.resolved else address.get("value")
        aliases = _extract_aliases(entity_information_for_idx, idx, submission_id)
        parent_id = entity.id if is_valid_uuid(entity.id) else None
        remote_id = entity.remote_id
        if not remote_id and not is_valid_uuid(entity.id):
            remote_id = entity.id
        entity_role = entity.entity_role or ENTITY_ROLE_MAP.get(entity.type)
        entity_named_insured = entity.entity_named_insured or named_insured_map.get(entity.type)
        entity_parent_type = None
        entity_parent_id = None
        if entity.parent_idx is not None:
            entity_parent_type = onboarded_data.entities[entity.parent_idx].parent_type
            entity_parent_id = onboarded_data.entities[entity.parent_idx].id
            entity_parent_id = entity_parent_id if is_valid_uuid(entity_parent_id) else None

        first_party_entities[idx] = FirstPartyFieldEntity(
            requested_name=requested_name,
            requested_address=requested_address,
            parent_type=entity.parent_type,
            parent_id=parent_id,
            remote_id=remote_id,
            requested_name_aliases=aliases,
            entity_role=entity_role,
            entity_named_insured=entity_named_insured,
            entity_parent_type=entity_parent_type,
            entity_parent_id=entity_parent_id,
        )

        if entity.type not in entity_type_map:
            continue
        additional_info = {}
        if entity_role == SubmissionBusinessEntityRole.ADVISOR:
            advisor_info = _handle_advisor_additional_info(advisor_type_field, idx, onboarded_data)
            if advisor_info:
                additional_info["advisor_details"] = advisor_info
        submission_business = SubmissionBusiness(
            requested_name=requested_name,
            requested_legal_name=requested_name if requested_name and is_name_legal_entity(requested_name) else None,
            requested_address=requested_address,
            requested_phone_number=None,
            requested_industries=None,
            entity_role=entity_role,
            named_insured=entity_named_insured,
            project_insurance_type=None,
            description_of_operations=None,
            submission_id=submission_id if isinstance(submission_id, UUID) else UUID(submission_id),
            aliases=aliases,
            business_id=entity.id if entity.resolved and is_valid_uuid(entity.id) else None,
            additional_info=additional_info,
            file_id_sources=list(file_id_sources_set),
        )
        db.session.add(submission_business)
        submission_businesses[idx] = submission_business

    submission_businesses_none = [sb for sb in submission_businesses.values() if sb.business_id is None]
    if submission_businesses_none:
        handle_missing_submission_businesses(
            missing_submission_businesses=submission_businesses_none,
            submission_id=submission_id,
            exception_type=UnconfirmedLocations,
        )

    validate_business_existence_in_ers(submission_businesses, submission_id)

    db.session.commit()
    return submission_businesses, first_party_entities


def resolve_parent_type_n_id(
    entity: FirstPartyFieldEntity | None, file_classification: ClassificationDocumentType, field_: ResolvedDataField
) -> tuple[ParentType, UUID | None]:
    if entity is None:
        logger.error("Entity is None", field_=field_, file_classification=file_classification)
        return ParentType.SUBMISSION, None
    parent_type = entity.parent_type
    parent_id = entity.parent_id
    if (
        entity.parent_type == ParentType.BUSINESS
        and file_classification in ClassificationDocumentType.get_sov_classifications()
    ):
        group_ids: set[str] = set(field_.group_ids or [])
        if group_ids.intersection(BUSINESS_GROUPS):
            # Do nothing, allow parent_type to be BUSINESS
            pass
        elif GroupID.FLEET_OPERATIONS_OTHER in group_ids and not field_.fact_subtype_id:
            parent_type = ParentType.SUBMISSION
            parent_id = None
        else:
            parent_type = ParentType.PREMISES
            parent_id = None
    return parent_type, parent_id


def create_grouped_first_party_fields(
    onboarded_data: OnboardedFile,
    submission_businesses: dict[int, SubmissionBusiness],
    first_party_entities: dict[int, FirstPartyFieldEntity],
    file_id_to_classification: dict[UUID, ClassificationDocumentType],
    submission: Submission,
) -> FirstPartyFields:
    first_party_fields_grouped: dict[FirstPartyFieldsGroupType, list] = {
        FirstPartyFieldsGroupType(group_type): [] for group_type in FirstPartyFieldsGroupType.get_all()
    }
    entity_types = {idx: entity.type for idx, entity in enumerate(onboarded_data.entities)}
    group_type_map_by_entity_type = {
        SubmissionEntityType.GENERAL_CONTRACTOR: FirstPartyFieldsGroupType.SOV,
        SubmissionEntityType.PROJECT: FirstPartyFieldsGroupType.SOV,
        SubmissionEntityType.OTHER_INSURED: FirstPartyFieldsGroupType.SOV,
        SubmissionEntityType.PRIMARY_INSURED: FirstPartyFieldsGroupType.SOV,
        SubmissionEntityType.BUSINESS: FirstPartyFieldsGroupType.SOV,
        SubmissionEntityType.DRIVER: FirstPartyFieldsGroupType.DRIVERS,
        SubmissionEntityType.VEHICLE: FirstPartyFieldsGroupType.VEHICLES,
        SubmissionEntityType.EQUIPMENT: FirstPartyFieldsGroupType.EQUIPMENT,
        SubmissionEntityType.STRUCTURE: FirstPartyFieldsGroupType.SOV,
        SubmissionEntityType.PRODUCT: FirstPartyFieldsGroupType.ACORD,
        SubmissionEntityType.GARAGE: FirstPartyFieldsGroupType.VEHICLES,
        SubmissionEntityType.EMPLOYEE: FirstPartyFieldsGroupType.EMPLOYEES,
        SubmissionEntityType.BENEFITS_PLAN: FirstPartyFieldsGroupType.BENEFIT_PLANS,
        SubmissionEntityType.TRANSACTION: FirstPartyFieldsGroupType.TRANSACTIONS,
        SubmissionEntityType.SUBMISSION: FirstPartyFieldsGroupType.SOV,
    }
    group_type_by_parent_type = {
        ParentType.BUSINESS: FirstPartyFieldsGroupType.SOV,
        ParentType.PREMISES: FirstPartyFieldsGroupType.SOV,
        ParentType.DOCUMENT: FirstPartyFieldsGroupType.SOV,
        ParentType.ORGANIZATION: FirstPartyFieldsGroupType.SOV,
        ParentType.GEOGRAPHICAL_AREA: FirstPartyFieldsGroupType.SOV,
        ParentType.SUBMISSION: FirstPartyFieldsGroupType.SOV,
        ParentType.REPORT: FirstPartyFieldsGroupType.SOV,
        ParentType.DRIVER: FirstPartyFieldsGroupType.DRIVERS,
        ParentType.VEHICLE: FirstPartyFieldsGroupType.VEHICLES,
        ParentType.EQUIPMENT: FirstPartyFieldsGroupType.EQUIPMENT,
        ParentType.PRODUCT: FirstPartyFieldsGroupType.ACORD,
        ParentType.GARAGE: FirstPartyFieldsGroupType.VEHICLES,
        ParentType.PERSON: FirstPartyFieldsGroupType.EMPLOYEES,
        ParentType.ERISA_PLAN: FirstPartyFieldsGroupType.BENEFIT_PLANS,
    }
    file_source_resolver = FileSourceResolver(submission)
    for field_ in onboarded_data.fields:
        first_party_fields = {}
        facts_subtype_id = field_.fact_subtype_id or field_.naics_code
        facts_subtype_id = facts_subtype_id.value if facts_subtype_id else None
        first_party_field = FirstPartyField(
            name=field_.name,
            value_type=field_.value_type,
            display_as_fact=field_.display_as_fact,
            values=[],
            unit_name=field_.unit.value if field_.unit else None,
            fact_subtype_id=facts_subtype_id,
            aggregation_type=field_.aggregation_type,
            group_ids=field_.group_ids,
        )
        for value in field_.values:
            if value.entity_idx is None:
                continue
            submission_business_id = None
            if sb := submission_businesses.get(value.entity_idx):
                submission_business_id = sb.id
            elif (parent_idx := onboarded_data.entities[value.entity_idx].parent_idx) is not None:
                submission_business_id = getattr(submission_businesses.get(parent_idx), "id", None)
            file_classification = file_id_to_classification.get(onboarded_data.files[value.file_idx])
            parent_type, parent_id = resolve_parent_type_n_id(
                first_party_entities.get(value.entity_idx), file_classification, field_
            )
            evidences = []
            for evidence in value.evidences:
                resolved_file_id = file_source_resolver.resolve(onboarded_data.files[evidence.file_idx])
                if onboarded_data.files[evidence.file_idx] != resolved_file_id:
                    logger.info(
                        "Evidence is for internal file, will drop it.",
                        file_id=onboarded_data.files[evidence.file_idx],
                        submission_id=submission.id,
                    )
                    continue
                evidences.append(replace(evidence, file_id=resolved_file_id))
            first_party_value = FirstPartyFieldValue(
                value=value.value,
                submission_business_id=str(submission_business_id) if submission_business_id else None,
                requested_name=(
                    first_party_entities[value.entity_idx].requested_name
                    if value.entity_idx in first_party_entities
                    else None
                ),
                requested_address=(
                    first_party_entities[value.entity_idx].requested_address
                    if value.entity_idx in first_party_entities
                    else None
                ),
                parent_type=parent_type,
                parent_id=parent_id,
                remote_id=(
                    first_party_entities[value.entity_idx].remote_id
                    if value.entity_idx in first_party_entities
                    else None
                ),
                file_id=file_source_resolver.resolve(onboarded_data.files[value.file_idx]),
                explanations=value.explanations,
                observed_name=value.observed_name or field_.name,
                evidences=evidences,
            )
            if submission_business := submission_businesses.get(value.entity_idx):
                first_party_value.submission_business_id = submission_business.id
            group_type = group_type_map_by_entity_type[entity_types[value.entity_idx]]
            if group_type not in first_party_fields:
                first_party_fields[group_type] = replace(first_party_field, values=[])
            first_party_fields[group_type].values.append(first_party_value)
        for group_type in first_party_fields:
            first_party_fields_grouped[group_type.value].append(first_party_fields[group_type])
    file_source_resolver.log_internal_files_without_parent()
    first_party_entities_grouped = {
        FirstPartyFieldsGroupType(group_type): [] for group_type in FirstPartyFieldsGroupType.get_all()
    }

    for first_party_entity in first_party_entities.values():
        if first_party_entity.parent_type not in group_type_by_parent_type:
            logger.info(
                "First party entity parent type has no group. Skipping",
                parent_type=first_party_entity.parent_type,
                requested_name=first_party_entity.requested_name,
                requested_address=first_party_entity.requested_address,
            )
            continue
        group_type = group_type_by_parent_type[first_party_entity.parent_type]
        first_party_entities_grouped[group_type].append(first_party_entity)

    return FirstPartyFields(
        fields_groups=[
            FirstPartyFieldsGroup(
                type=FirstPartyFieldsGroupType(group_type),
                fields=first_party_fields_grouped[FirstPartyFieldsGroupType(group_type)],
                entities=first_party_entities_grouped[FirstPartyFieldsGroupType(group_type)],
                discovered_in=None,
            )
            for group_type in FirstPartyFieldsGroupType.get_all()
        ]
    )


def _maybe_add_entity(
    entity_idx: int,
    entity: SubmissionEntity,
    data: OnboardedFileData,
    entity_name_value: ResolvedDataValue,
    entity_address_value: ResolvedDataValue,
) -> None:
    if entity_idx in data.entity_ids_map:
        return
    new_entity_idx = len(data.entities)
    data.entity_ids_map[entity_idx] = new_entity_idx
    data.entities.append(entity)
    if not (name_field := data.entity_information.get(EntityFieldID.NAME.value)):
        name_field = ResolvedDataField(
            name=EntityFieldID.NAME.value,
            values=[],
            value_type=FieldType.TEXT,
        )
        data.entity_information[EntityFieldID.NAME.value] = name_field
    name_value = get_or_create_value_for_entity(name_field, new_entity_idx, 0, should_create=False)
    if name_value is None:
        new_name_value = replace(entity_name_value, entity_idx=new_entity_idx, file_idx=0)
        name_field.values.append(new_name_value)
    if not (address_field := data.entity_information.get(EntityFieldID.ADDRESS.value)):
        address_field = ResolvedDataField(
            name=EntityFieldID.ADDRESS.value,
            values=[],
            value_type=FieldType.TEXT,
        )
        data.entity_information[EntityFieldID.ADDRESS.value] = address_field
    address_value = get_or_create_value_for_entity(address_field, new_entity_idx, 0, should_create=False)
    if address_value is None:
        new_address_value = replace(entity_address_value, entity_idx=new_entity_idx, file_idx=0)
        address_field.values.append(new_address_value)


def _process_field(
    value: ResolvedDataValue,
    field_: ResolvedDataField,
    data: OnboardedFile,
    result: dict[int, OnboardedFileData],
    data_field: str,
    name_field: ResolvedDataField,
    address_field: ResolvedDataField,
) -> None:
    if value.entity_idx is None:
        raise NoEntityAssociated(field=field_.name, value=value.value)
    file_idx_temp = 0
    if value.file_idx is not None:
        file_idx_temp = value.file_idx
    current_data = result[file_idx_temp]
    new_entity = replace(data.entities[value.entity_idx])
    name_value = get_or_create_value_for_entity(name_field, value.entity_idx, value.file_idx)
    address_value = get_or_create_value_for_entity(address_field, value.entity_idx, value.file_idx)
    _maybe_add_entity(value.entity_idx, new_entity, current_data, name_value, address_value)
    if new_entity.parent_idx is not None:
        new_parent_entity = replace(data.entities[new_entity.parent_idx])
        name_value = get_or_create_value_for_entity(name_field, new_entity.parent_idx, value.file_idx)
        address_value = get_or_create_value_for_entity(address_field, new_entity.parent_idx, value.file_idx)
        _maybe_add_entity(new_entity.parent_idx, new_parent_entity, current_data, name_value, address_value)
        new_entity.parent_idx = current_data.entity_ids_map[new_entity.parent_idx]
    if data_field != "entity_information" or field_.name not in {EntityFieldID.NAME.value, EntityFieldID.ADDRESS.value}:
        _add_value_to_data_field(value, current_data, field_, data_field)


def _add_value_to_data_field(
    value: ResolvedDataValue, data: OnboardedFileData, field_: ResolvedDataField, data_field: str
) -> None:
    new_value = replace(value, file_idx=0, entity_idx=data.entity_ids_map[value.entity_idx])
    field_name = field_.fact_subtype_id or field_.name
    if field_name in getattr(data, data_field):
        getattr(data, data_field)[field_name].values.append(new_value)
    else:
        getattr(data, data_field)[field_name] = replace(field_, values=[new_value])


def _is_empty(value: Any) -> bool:
    return value is None or (isinstance(value, str) and not value.strip())


def _process_fields_for_splitting(
    data: OnboardedFile,
    result: dict[int, OnboardedFileData],
    name_field: ResolvedDataField,
    address_field: ResolvedDataField,
) -> None:
    for item in data.fields:
        for value in item.values:
            if _is_empty(value.value):
                continue
            _process_field(value, item, data, result, "fields", name_field, address_field)


def _process_entity_information_for_splitting(
    data: OnboardedFile,
    result: dict[int, OnboardedFileData],
    name_field: ResolvedDataField,
    address_field: ResolvedDataField,
) -> None:
    for item in data.entity_information:
        for value in item.values:
            if _is_empty(value.value):
                continue
            _process_field(value, item, data, result, "entity_information", name_field, address_field)
            file_idx_temp = 0
            if value.file_idx is not None:
                file_idx_temp = value.file_idx
            for idx, file in result.items():
                if idx == file_idx_temp:
                    continue
                if value.entity_idx in file.entity_ids_map and item.name not in {
                    EntityFieldID.NAME.value,
                    EntityFieldID.ADDRESS.value,
                }:
                    _add_value_to_data_field(value, file, item, "entity_information")


def split_data_to_files(data: OnboardedFile) -> dict[UUID, OnboardedFile]:
    result: dict[int, OnboardedFileData] = {idx: OnboardedFileData(files=[file]) for idx, file in enumerate(data.files)}
    name_field = get_or_create_entity_field_by_name(data, EntityFieldID.NAME.value, FieldType.TEXT)
    address_field = get_or_create_entity_field_by_name(data, EntityFieldID.ADDRESS.value, FieldType.TEXT)
    _process_fields_for_splitting(data, result, name_field, address_field)
    _process_entity_information_for_splitting(data, result, name_field, address_field)
    for file in result.values():
        entity_fields_to_remove = []
        for fn, f in file.entity_information.items():
            f.values = [v for v in f.values if not _is_empty(v.value)]
            if not f.values:
                entity_fields_to_remove.append(fn)
        for fn in entity_fields_to_remove:
            del file.entity_information[fn]

    return {
        data.files[idx]: OnboardedFile(
            fields=list(file_data.fields.values()),
            entity_information=list(file_data.entity_information.values()),
            entities=file_data.entities,
            files=file_data.files,
        )
        for idx, file_data in result.items()
    }


def infer_load_method(processed_data: dict) -> Callable | None:
    if "fields_groups" in processed_data:
        return first_party_fields_loader
    elif "fields" in processed_data:
        return onboarded_file_loader
    else:
        logger.error("Processed data doesn't match any of the supported formats.")
        return None


def load_processed_data(file: ProcessedFile) -> OnboardedFile:
    if load_method := infer_load_method(file.processed_data):
        loaded_processed_data = load_method(file.processed_data, file.file_id)
        _clean_onboarded_data(loaded_processed_data)
        return loaded_processed_data
    else:
        raise UnsupportedProcessedDataFormat(file_id=str(file.file_id))


def _load_onboarded_data(file: ProcessedFile) -> OnboardedFile:
    if file.entity_mapped_data:
        entity_mapped_data = (
            file.entity_mapped_data
            if hasattr(file.entity_mapped_data, "entities")
            else onboarded_file_schema.load(file.entity_mapped_data)
        )
        _clean_onboarded_data(entity_mapped_data)
        return entity_mapped_data
    else:
        return load_processed_data(file)


def load_data(
    file: ProcessedFile,
    data_field: str,
    enhance_with_resolution_data: bool = False,
) -> None:
    # As we load the data into obj we need to detach as otherwise when trying to sync the session it throws an error.
    if file in db.session:
        db.session.expunge(file)
    if data := getattr(file, data_field):
        setattr(file, data_field, onboarded_file_schema.load(data))
        # If we already have the entity_mapped_data or data_oboarded_data, then it has been already enriched with
        # the resolution data and repeating the process can only result in errors
        enhance_with_resolution_data = False
    elif data_field == "entity_mapped_data":
        file.entity_mapped_data = load_processed_data(file)
    elif data_field == "onboarded_data":
        file.onboarded_data = _load_onboarded_data(file)

    if enhance_with_resolution_data:
        enhance_with_business_resolution_data(file, data_field)


def load_data_multiple_files(
    files: Sequence[ProcessedFileWithClassification],
    data_field: str,
    enhance_with_resolution_data: bool = False,
) -> None:
    for file in files:
        load_data(file.data, data_field, enhance_with_resolution_data)


def get_or_create_entity_field_by_name(
    file: OnboardedFile, field_name: str, value_type: FieldType
) -> ResolvedDataField:
    result = next(
        (resolved_field for resolved_field in file.entity_information if resolved_field.name == field_name), None
    )
    if result is None:
        result = ResolvedDataField(
            name=field_name,
            values=[],
            value_type=value_type,
        )
        file.entity_information.append(result)
    return result


def get_or_create_field_by_fact_subtype(
    file: OnboardedFile,
    field_name: str,
    fact_subtype_id: str | None,
    value_type: FieldType,
    fact_subtypes: dict[str, FactSubtype],
) -> ResolvedDataField:
    name = field_name
    fsid = fact_subtype_id
    if fact_subtype := _get_fact_subtype(fact_subtype_id or field_name, fact_subtypes):
        name = fact_subtype.get("display_name") or field_name
        fsid = fact_subtype.get("id")
    fact_subtype_id = FactSubtypeID.try_parse_str(fsid)
    naics_code_id = NaicsCode.try_parse_str(fsid)
    result = next(
        (
            resolved_field
            for resolved_field in file.fields
            if (resolved_field.fact_subtype_id == fact_subtype_id if fact_subtype_id else False)
        ),
        None,
    )
    if result is None:
        result = ResolvedDataField(
            name=name,
            fact_subtype_id=fact_subtype_id,
            values=[],
            value_type=value_type,
            display_as_fact=fact_subtype_id is not None or naics_code_id is not None,
            naics_code=naics_code_id,
            unit=get_unit(fact_subtype),
        )
        file.fields.append(result)
    return result


def get_or_create_value_for_entity(
    resolved_field: ResolvedDataField, entity_idx: int, file_idx: int, should_create: bool = True
) -> ResolvedDataValue:
    result = next(
        (value for value in resolved_field.values if value.entity_idx == entity_idx),
        None,
    )
    if result is None and should_create:
        result = ResolvedDataValue(
            value="",
            entity_idx=entity_idx,
            file_idx=file_idx,
        )
        resolved_field.values.append(result)
    return result


def _maybe_update_entity_named_insured(entity: SubmissionEntity, is_fni: bool) -> None:
    if entity.is_fni and not is_fni:
        entity.entity_named_insured = None
        entity.type = SubmissionEntityType.BUSINESS
    if not entity.is_fni and is_fni:
        entity.entity_named_insured = SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED


def update_entity_role(entity, entity_role: SubmissionBusinessEntityRole) -> None:
    entity.entity_role = entity_role


def enhance_with_business_resolution_data(file: ProcessedFile, data_field: str) -> None:
    if file.business_resolution_data is None:
        return
    data: OnboardedFile = getattr(file, data_field)
    if isinstance(file.business_resolution_data, dict):
        resolution_data: list[BusinessResolutionData] = BusinessResolutionDataSchema().load(
            file.business_resolution_data.get("resolution_data"), many=True
        )
    else:
        resolution_data = file.business_resolution_data
    name_field = get_or_create_entity_field_by_name(data, EntityFieldID.NAME.value, FieldType.TEXT)
    address_field = get_or_create_entity_field_by_name(data, EntityFieldID.ADDRESS.value, FieldType.TEXT)

    for entity_brd in resolution_data:
        if entity_brd.entity_idx is None:
            logger.info("Entity without entity index found", entity_brd=entity_brd)
            if new_entity := _create_business_entity_from_business_resolution_data(entity_brd):
                new_entity_idx = len(data.entities)
                logger.info(
                    "Created business entity with new index",
                    entity_brd=entity_brd,
                    new_entity=new_entity,
                    new_entity_idx=new_entity_idx,
                )
                entity_brd.entity_idx = new_entity_idx
                data.entities.append(new_entity)
            else:
                continue
        entity = data.entities[entity_brd.entity_idx]
        # We want to always update the FNI status
        _maybe_update_entity_named_insured(entity, entity_brd.is_fni)
        # We want to update the role based on brd
        entity.entity_role = entity_brd.entity_role
        if entity_brd.entity_id:
            # If the entity is marked as resolved and the id is the same, we can simply skip
            if entity.resolved and entity.id == str(entity_brd.entity_id):
                continue
            # If the entity is resolved, but the id is different, we log an error and overwrite the existing data
            if entity.resolved:
                logger.error(
                    "Entity already resolved, but with different id", file_id=file.file_id, entity_brd=entity_brd
                )
            entity.id = str(entity_brd.entity_id)
            entity.resolved = True
            name_value = get_or_create_value_for_entity(
                resolved_field=name_field, entity_idx=entity_brd.entity_idx, file_idx=0
            )
            name_value.observed_value = entity_brd.requested_name
            if entity_brd.resolved_name != entity_brd.requested_name:
                name_value.value = (
                    f"{entity_brd.resolved_name} ({entity_brd.requested_name})"
                    if entity_brd.requested_name
                    else entity_brd.resolved_name
                )
            address_value = get_or_create_value_for_entity(
                resolved_field=address_field, entity_idx=entity_brd.entity_idx, file_idx=0
            )
            address_value.observed_value = entity_brd.requested_address
            if entity_brd.resolved_address != entity_brd.requested_address:
                address_value.value = (
                    f"{entity_brd.resolved_address} ({entity_brd.requested_address})"
                    if entity_brd.requested_address
                    else entity_brd.resolved_address
                )
        elif data_field == "data_onboarding":
            logger.warning("File has unresolved entities going into DO", file_id=file.file_id)

    file.business_resolution_data = {"resolution_data": BusinessResolutionDataSchema().dump(resolution_data, many=True)}
    db.session.commit()


def all_structures_have_parent_ids(processed_file: ProcessedFile) -> bool:
    of: OnboardedFile = onboarded_file_schema.load(processed_file.processed_data)
    return all(e.parent_idx is not None for e in of.entities if e.type == SubmissionEntityType.STRUCTURE)


def _create_business_entity_from_business_resolution_data(brd: BusinessResolutionData) -> SubmissionEntity | None:
    if brd.requested_name or brd.requested_address:
        remote_id = calculate_entity_id(brd.requested_name, brd.requested_address)
    else:
        logger.warning("Could not create entity from business resolution data", business_resolution_data=brd)
        return None

    return SubmissionEntity(type=SubmissionEntityType.BUSINESS, id=brd.entity_id, remote_id=remote_id)


def __should_apply_suggestion(original_value: str, suggestion_result: ValueSuggestionResult) -> bool:
    if suggestion_result.value == original_value:
        return False

    if suggestion_result.confidence is None or suggestion_result.confidence < 1.0:
        return False

    return suggestion_result.auto_apply is True


def _clean_onboarded_data(file: OnboardedFile):
    fact_subtypes = _get_fact_subtypes()
    fields = file.fields + file.entity_information
    for file_field in fields:
        fact_subtype = _get_fact_subtype(file_field.fact_subtype_id, fact_subtypes)
        for value in file_field.values:
            if value.value is None:
                continue
            str_value = str(value.value)
            suggestion_result = suggest_value_for_fact_subtype(value=value.value, fact_subtype=fact_subtype)
            if not __should_apply_suggestion(str_value, suggestion_result):
                continue
            if value.observed_value is None:
                value.observed_value = value.value

            if random() < 0.001:
                logger.info(
                    "Auto-applying suggestion",
                    fact_subtype_id=file_field.fact_subtype_id,
                    original_value=str_value,
                    suggested_value=suggestion_result.value,
                )
            value.value = suggestion_result.value


def create_or_update_entity_information(
    file: OnboardedFile,
    entity_idx: int,
    entity_field_id: EntityFieldID,
    value: Any,
    value_type: FieldType = FieldType.TEXT,
) -> OnboardedFile:
    entity = file.entities[entity_idx]
    # check if entity information contains the entity_field_id
    entity_info_field = get_or_create_entity_field_by_name(file, entity_field_id.value, value_type)
    resolved_value = get_or_create_value_for_entity(entity_info_field, entity_idx, 0)
    resolved_value.value = value

    if entity.type in SubmissionEntityType.business_entities():
        new_id = recalculate_entity_id(file, entity_idx)
        entity.id = new_id

    return file
