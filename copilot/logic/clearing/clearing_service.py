from datetime import datetime, timed<PERSON>ta
from uuid import UUID
import re

from infrastructure_common.logging import get_logger
from sqlalchemy import and_, case, func, literal, or_, true, tuple_
from sqlalchemy.orm import contains_eager, lazyload, load_only
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_processing_state import SubmissionProcessingState
import flask

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.constants import (
    CONIFER_EMAIL_PREFIX_TO_IGNORE_CORRESPONDENCE,
    SUPPORT_CLEARING_SLACK_CHANNEL_ID,
)
from copilot.kalepa_domain_events.kalepa_events_handler import KalepaEventsHandler
from copilot.logic.clearing.bowhead import (
    handle_submissions_matched_by_premises_for_bowhead,
)
from copilot.logic.clearing.common import (
    FinishExternalClearingHandler,
    PremisesClearingHandler,
)
from copilot.logic.clearing.conifer import finish_external_clearing_for_conifer
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.models import (
    File,
    Organization,
    ReportPermission,
    ReportProcessingDependency,
    ReportV2,
    Submission,
    SubmissionBusiness,
    SubmissionClearingIssue,
    SubmissionStage,
    Subscription,
    User,
    db,
)
from copilot.models.clearing.external import FinishExternalClearingRequest
from copilot.models.files import ProcessedFile
from copilot.models.reports import (
    ClearingSubStatus,
    SubmissionClearingSubStatus,
    SubmissionCoverage,
)
from copilot.models.submission_premises import SubmissionPremises
from copilot.models.types import (
    ClearingStatus,
    CoverageType,
    PermissionType,
    ReportShadowType,
)
from copilot.services.orgs.bowhead_service import BowheadService
from copilot.utils import psql_similarity

logger = get_logger()

FULL_CLEARING_REASON = "Submissions share common entities, have a similar name, and different brokers"
SUBJECT_ILIKE_REASON = "Email subject or name is like the other subject or name"
SUBJECT_SIMILAR_REASON = "Email or name subject is similar"
SAME_FILE_REASON = "There is at least 1 shared file"
SAME_ENTITY_REASON = "The entity data is the same"
IN_FORCE_POLICY_REASON = (
    "Submission contains common entities, similar name, and similar coverages as current active policy"
)
BLACKLISTED_WORDS = [
    "#secure#",
    "transportation",
    "crc#",
    "construction",
    "trucking",
    "contractors",
    "services",
    "fw:",
    "fwd:",
    "llc",
    "incorporated",
    "submission",
    "excess",
    "liability",
    "|",
    "submission:",
    "effective",
    "eff.",
    "eff date",
    "please",
    "new business",
    "rush",
    "rush:",
    "urgent",
    "urgent:",
    "workers comp",
    "work comp",
    "work compensation",
    "workers compensation",
    "paragon",
    "insurance",
    "please clear",
    "on behalf of",
    "on behalf",
    "*",
    "casualty",
    "holding",
    "holdin",
    "llc",
    "declined",
    "decline",
    "incomplete",
    "renewal",
    "missing information",
    "policy",
    "e&s",
    "new submission",
    "new business submission",
    "gl&xs",
    "gl & xs",
    "gl/xs",
]
replace_words = {
    "inc": "",
    "eff": "",
    "wc": "",
    "xs": "",
    "gl": "",
    "solutions": "company",
    "group": "company",
    "corporation": "corp",
}

ORGS_SUPPORTING_FINISHING_EXTERNAL_CLEARING: dict[int, FinishExternalClearingHandler] = {
    ExistingOrganizations.BishopConifer.value: finish_external_clearing_for_conifer
}

SHELL_CONFLICTS_ALLOWED = {ExistingOrganizations.BowheadSpecialty.value}

ORGS_SUPPORTING_MATCH_BY_PREMISES: dict[int, PremisesClearingHandler] = {
    ExistingOrganizations.BowheadSpecialty.value: PremisesClearingHandler(
        matching_premises_fetcher=BowheadService.get_matching_submission_premises,
        matching_premises_handler=handle_submissions_matched_by_premises_for_bowhead,
    )
}


def _is_excluded_file(f: File) -> bool:
    if f.file_type == FileType.EMAIL or not f.name:
        return True
    name = f.name.lower()
    return name.endswith("png") or name.endswith("jpg")


def _normalize_email(email: str | None) -> str:
    if not email:
        return ""
    email = email.lower()
    for word in sorted(BLACKLISTED_WORDS, key=len, reverse=True):
        email = email.replace(f"{word}", "").strip()
    email = email.replace("  ", " ").strip()
    # remove dates
    email = re.sub(r"\d{1,4}/\d{1,2}/\d{1,4}", "", email)
    # Remove everything more than 5 digits - usually those are control numbers
    email = re.sub(r"\d{5,16}", "", email)
    for word in replace_words.items():
        email = email.replace(f" {word[0]} ", f" {word[1]} ")
        email = email.replace(f"{word[0]} ", f"{word[1]} ")
        email = email.replace(f" {word[0]}", f" {word[1]}")
    return email.strip()


class ClearingService:
    CREATED_AT_RANGE_TO_CONSIDER_DAYS = 100
    SIMILARITY_THRESHOLD = 0.66
    BOSS_SIMILARITY_THRESHOLD = 0.6
    QUERY_THRESHOLD = 0.3
    FULL_CLEARING_QUERY_THRESHOLD = 0.5
    MIN_SUBJECT_LENGTH = 4

    @staticmethod
    def resolve_light_clearing_issues(report: ReportV2) -> None:
        for clearing_issue in report.submission.clearing_issues:
            if clearing_issue.is_light and not clearing_issue.is_resolved:
                logger.info("Resolving clearing issue", report_id=report.id, clearing_issue_id=clearing_issue.id)
                clearing_issue.is_resolved = True

    @staticmethod
    def publish_submission_light_cleared_event(submission: Submission) -> None:
        is_dependent_submission = (
            submission.active_submission_processing and submission.active_submission_processing.is_dependency_processing
        )
        KalepaEventsHandler.send_light_clearing_finished_event(
            submission.id, is_dependent_submission=is_dependent_submission
        )

    @staticmethod
    def notify_submission_needs_light_clearing(submission: Submission):
        report = submission.report
        company = Organization.get_company_for_id(report.organization_id)
        try:
            if flask.current_app.is_prod:
                flask.current_app.slack_client.send_slack_message(
                    SUPPORT_CLEARING_SLACK_CHANNEL_ID, f"{report.get_url()} needs light clearing ({company}). "
                )
        except Exception as e:
            logger.exception("Slack notification failed", e=e)

    def handle_clearing(self, report: ReportV2, light_clearing_only: bool = False) -> None:
        if not (submission := report.submission):
            return
        try:
            owner: User = User.query.get(report.owner_id)
            if light_clearing_only:
                self._light_clear_submission(submission, owner)
                if submission.has_unresolved_light_clearing_issues:
                    submission.processing_state = SubmissionProcessingState.NEEDS_CLEARING
                    submission.is_processing = False
                    db.session.commit()
                    self.notify_submission_needs_light_clearing(submission)
                else:
                    submission.light_cleared = True
                    db.session.commit()
                    self.publish_submission_light_cleared_event(submission)
            elif self._should_clear_report(report, owner):
                self._clear_submission(submission, owner)
                db.session.commit()
        except Exception:
            logger.exception("Clearing checks failed", report_id=report.id)
            db.session.rollback()

    def finish_external_clearing(
        self, submission_id: UUID | str, finish_external_clearing_request: FinishExternalClearingRequest
    ) -> None:
        if isinstance(submission_id, str):
            submission_id = UUID(submission_id)

        organization_id = finish_external_clearing_request.organization_id
        handler = ORGS_SUPPORTING_FINISHING_EXTERNAL_CLEARING.get(organization_id)

        if not handler:
            raise RuntimeError(f"Organization({organization_id}) does not support finishing external clearing")
        else:
            handler(submission_id, finish_external_clearing_request)

    def _should_clear_report(self, report: ReportV2, owner: User) -> bool:
        if not owner.applicable_settings or not owner.applicable_settings.is_clearing_enabled:
            return False
        submission = report.submission
        if submission.is_verification_required and not submission.is_verified:
            return False
        if not submission.businesses or report.is_deleted:
            return False
        if submission.clearing_status in ClearingStatus.resolved_statuses():
            logger.info("Skipping clearing as report was already resolved", report_id=report.id)
            return False

        return True

    def _light_clear_submission(self, submission: Submission, owner: User) -> None:
        log = logger.bind(submission_id=submission.id, owner_id=owner.id)
        if self._should_skip_light_clearing(submission):
            log.info("Skipping finding light clearing issues")
            return
        log.info("Looking for light clearing issues")
        clearing_results = self._get_similar_reports_light(submission)
        log.info("Found similar reports", report_count=len(clearing_results))

        if len(clearing_results) == 0:
            log.info("No light clearing issues found")
            return

        actual_clearing_issues = {}
        for report, reason in clearing_results:
            if not report.submission.is_processing_enabled:
                log.info("Skipping clearing result because match is not enabled for processing", report_id=report.id)
                continue
            if report.submission.is_shell_from_sync:
                log.info("Skipping clearing result because the match is a shell from sync", report_id=report.id)
                continue
            # Double check similarity condition on normalized strings
            if reason in {SUBJECT_SIMILAR_REASON, SUBJECT_ILIKE_REASON}:
                use_boss = submission.is_boss and report.submission.origin == Origin.EMAIL
                threshold = self.BOSS_SIMILARITY_THRESHOLD if use_boss else self.SIMILARITY_THRESHOLD
                verified = self._verify_str_reasons(report.email_subject, submission.report.email_subject, threshold)
                verified = verified or self._verify_str_reasons(report.name, submission.report.name, threshold)
                if not verified:
                    continue
            actual_clearing_issues[report.id] = (report, reason)

        log.info("Actual clearing issues", actual_clearing_issues_len=len(actual_clearing_issues))
        # Add clearing issues to the current sub
        for report, reason in actual_clearing_issues.values():
            existing_report_ids = [ci.suspected_report_id for ci in submission.clearing_issues]
            if report.id in existing_report_ids:
                log.info("Skipping report when light clearing as issue exists", report_id=report.id)
                continue
            submission.clearing_issues.append(
                SubmissionClearingIssue(
                    submission_id=submission.id,
                    is_resolved=False,
                    suspected_report_id=report.id,
                    reason=reason,
                    is_light=True,
                )
            )

    @staticmethod
    def _should_skip_light_clearing(submission: Submission) -> bool:
        report = submission.report
        if submission.is_renewal:
            logger.info("Skipping light clearing because sub is a renewal", submission_id=submission.id)
            return True
        if (
            Organization.is_bishop_conifer_for_id(report.organization_id)
            and report.email_subject
            and report.email_subject.lower().startswith(CONIFER_EMAIL_PREFIX_TO_IGNORE_CORRESPONDENCE)
        ):
            logger.info("Skipping light clearing because of sub email", submission_id=submission.id)
            return True
        if submission.is_boss:
            logger.info("Skipping light clearing because sub is a BOSS submission", submission_id=submission.id)
            return True
        return False

    def _verify_str_reasons(self, e1: str, e2: str, threshold: float = SIMILARITY_THRESHOLD) -> bool:
        def _verify_single(email_1: str, email_2: str) -> bool:
            email_1 = _normalize_email(email_1)
            email_2 = _normalize_email(email_2)
            if not email_1 or len(email_1) < self.MIN_SUBJECT_LENGTH:
                return False
            if not email_2 or len(email_2) < self.MIN_SUBJECT_LENGTH:
                return False
            if email_1 in email_2 or email_2 in email_1:
                return True
            if psql_similarity(email_1, email_2) < threshold:
                return False
            return True

        names_1 = [e1, *self._generate_other_names(e1)]
        names_2 = [e2, *self._generate_other_names(e2)]
        if any(_verify_single(n1, n2) for n1 in names_1 for n2 in names_2):
            return True
        return False

    def _clear_submission(self, submission: Submission, owner: User) -> None:
        affected_reports = self._process_clearing(submission, owner)
        if affected_reports:
            for report in affected_reports:
                self._share_with_users_that_can_clear(report.submission)
        else:
            self._share_with_users_that_can_clear(submission)

    def _share_with_users_that_can_clear(self, submission: Submission) -> None:
        owner: User = User.query.get(submission.owner_id)
        org_users: list[User] = User.skip_disabled_query.filter(
            User.organization_id == owner.organization_id, User.cross_organization_access == False
        ).all()
        users_to_share = set()
        for user in org_users:
            if user.applicable_settings and user.applicable_settings.can_resolve_clearing_issues:
                users_to_share.add(user)

        logger.info(
            "Sharing a clearing with clearing users",
            submission_id=submission.id,
            users_to_share=[u.id for u in users_to_share],
            user_count=len(users_to_share),
        )
        for user in users_to_share:
            if submission.report:
                self._add_clearing_user_permission(submission.report, user)

    def _add_clearing_user_permission(self, report: ReportV2, user: User):
        new_permission = ReportPermission(
            grantee=user,
            report_id=report.id,
            permission_type=PermissionType.OWNER,
        )
        current_user_permission = next((p for p in report.report_permissions if p.grantee_user_id == user.id), None)
        if not current_user_permission:
            report.report_permissions.append(new_permission)
            db.session.add(Subscription(report_id=report.id, user_id=user.id))
        elif current_user_permission.permission_type not in {PermissionType.OWNER, PermissionType.ADMIN}:
            report.report_permissions.remove(current_user_permission)
            report.report_permissions.append(new_permission)

    def _process_clearing(self, current_submission: Submission, owner: User) -> list[ReportV2]:
        log = logger.bind(submission_id=current_submission.id, owner_id=owner.id)
        log.info("Looking for clearing issues")

        enable_conflict_detection_against_shells = FeatureFlagsClient.is_feature_enabled(
            FeatureType.ENABLE_CONFLICT_DETECTION_AGAINST_SHELLS, owner.email
        )
        reports = self._get_similar_reports(
            current_submission, should_match_sync_address=enable_conflict_detection_against_shells
        )
        current_report = current_submission.reports[0]
        current_org_id = current_report.organization_id
        log.info("Found similar reports", report_count=len(reports), report_ids=[r.id for r, _ in reports])

        if current_org_id == ExistingOrganizations.BishopConifer.value and not current_submission.agent_code:
            clearing_sub_status = SubmissionClearingSubStatus()
            clearing_sub_status.status = ClearingSubStatus.MISSING_DETAILS.value
            clearing_sub_status.sub_status = "Non-Appointed Broker"
            current_submission.clearing_sub_statuses.append(clearing_sub_status)

        if current_org_id == ExistingOrganizations.BishopConifer.value:
            self._check_in_force_policies_as_conflict(
                current_submission, should_match_sync_address=enable_conflict_detection_against_shells
            )

        shell_conflicts_allowed = current_org_id in SHELL_CONFLICTS_ALLOWED
        match_by_premises = current_org_id in ORGS_SUPPORTING_MATCH_BY_PREMISES

        # TODO(plenza): Remove this after testing for a few days ENG-28940
        if (
            current_org_id == ExistingOrganizations.BowheadSpecialty.value
            and not FeatureFlagsClient.is_feature_enabled(FeatureType.BOWHEAD_CLEARING_BY_MATCHED_SUB_PREMISES)
        ):
            log.info("Bowhead clearing by matching sub premises is disabled on FF")
            shell_conflicts_allowed = False
            match_by_premises = False

        if match_by_premises:
            log.info("Organization has 'match by premises' handler, invoking", organization_id=current_org_id)
            handler = ORGS_SUPPORTING_MATCH_BY_PREMISES[current_org_id]
            matched_submission_premises = handler.matching_premises_fetcher(current_submission, db.session, log)
            reports.extend(handler.matching_premises_handler(current_submission, matched_submission_premises, log))

        actual_clearing_issues = []
        for report, reason in reports:
            if report.submission.is_shell_from_sync and not (
                shell_conflicts_allowed or enable_conflict_detection_against_shells
            ):
                log.info(
                    (
                        "Skipping clearing result because the match is a shell from sync and we do not allow shell sync"
                        " conflicts"
                    ),
                    report_id=report.id,
                    shell_conflicts_allowed=shell_conflicts_allowed,
                    enable_conflict_detection_against_shells=enable_conflict_detection_against_shells,
                )
                continue

            # Double check similarity condition on normalized strings
            if reason in {SUBJECT_SIMILAR_REASON, SUBJECT_ILIKE_REASON}:
                threshold = self.SIMILARITY_THRESHOLD
                verified = self._verify_str_reasons(
                    report.email_subject, current_submission.report.email_subject, threshold
                )
                verified = verified or self._verify_str_reasons(report.name, current_submission.report.name, threshold)
                if not verified:
                    continue
            actual_clearing_issues.append((report, reason))

        if len(actual_clearing_issues) == 0:
            log.info("No clearing issues found")
            return []

        affected_reports = set()
        # Add clearing issues to the current sub
        for report, reason in actual_clearing_issues:
            existing_report_ids = [
                ci.suspected_report_id for ci in current_submission.clearing_issues if not ci.is_light
            ]
            if report.id in existing_report_ids:
                log.info("Skipping report when clearing as issue exists", report_id=report.id)
                continue
            current_submission.clearing_issues.append(
                SubmissionClearingIssue(
                    submission_id=current_submission.id,
                    is_resolved=False,
                    suspected_report_id=report.id,
                    reason=reason or FULL_CLEARING_REASON,
                )
            )
            current_submission.clearing_status = ClearingStatus.IN_CLEARING_CONFLICT
            affected_reports.add(current_report)

        # Append single clearing issue to other reports, if they are not cleared
        for report, reason in actual_clearing_issues:
            submission = report.submission
            if submission.is_shell_from_sync:
                log.info("Skipping clearing result because the match is a shell from sync", report_id=report.id)
                continue
            if submission.clearing_status == ClearingStatus.CLEARED:
                log.info("Skipping sub because it was already cleared", skipped_sub=submission.id)
                continue
            if submission.clearing_status == ClearingStatus.BLOCKED:
                log.info("Skipping sub when clearing as it was processed", skipped_sub=submission.id)
                continue
            existing_report_ids = [ci.suspected_report_id for ci in submission.clearing_issues]
            if current_report.id in existing_report_ids:
                log.info("Skipping sub when clearing as issue exists", skipped_sub=submission.id)
                continue
            submission.clearing_issues.append(
                SubmissionClearingIssue(
                    submission_id=submission.id,
                    is_resolved=False,
                    suspected_report_id=current_report.id,
                    reason=reason or FULL_CLEARING_REASON,
                )
            )
            affected_reports.add(report)
            submission.clearing_status = ClearingStatus.IN_CLEARING_CONFLICT
        return list(affected_reports)

    def _check_in_force_policies_as_conflict(self, submission: Submission, should_match_sync_address: bool = False):
        current_date = datetime.now()
        [submission_fni] = submission.first_named_insured
        submission_coverage_ids = [c.coverage_id for c in submission.coverages]

        active_policies = (
            Submission.query.options(
                load_only(Submission.id, Submission.report_id, Submission.broker_id),
                lazyload(Submission.assigned_underwriters),
            )
            .join(Submission.report)
            .filter(
                Submission.is_verified == True,
                ReportV2.is_deleted == False,
                ReportV2.organization_id == submission.organization_id,
                Submission.stage == SubmissionStage.QUOTED_BOUND,
                Submission.policy_status == "Active",
                Submission.proposed_effective_date <= current_date,
                or_(Submission.policy_expiration_date.is_(None), current_date <= Submission.policy_expiration_date),
            )
        )

        submission_in_effect_before_policy_expiration = (
            (
                func.coalesce(
                    Submission.policy_expiration_date, Submission.proposed_effective_date + timedelta(days=365)
                )
                > submission.proposed_effective_date
            )
            if submission.proposed_effective_date is not None
            else true()
        )
        same_fni = and_(
            SubmissionBusiness.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
            or_(
                SubmissionBusiness.business_id == submission_fni.business_id,
                and_(
                    SubmissionBusiness.requested_name == submission_fni.requested_name,
                    SubmissionBusiness.requested_address == submission_fni.requested_address,
                ),
            ),
        )
        policy_coverages_contain_submission_coverages = SubmissionCoverage.coverage_id.in_(submission_coverage_ids)

        conflicting_policies = (
            active_policies.filter(submission_in_effect_before_policy_expiration)
            .join(SubmissionBusiness)
            .filter(same_fni)
            .join(Submission.coverages)
            .filter(policy_coverages_contain_submission_coverages)
        )

        if should_match_sync_address:
            premises_addresses = [premise.address.strip().lower() for premise in submission.premises]
            premises_ids = [premise.premises_id for premise in submission.premises if premise.premises_id]
            if not premises_addresses and not premises_ids:
                log = logger.bind(submission_id=submission.id)
                log.error("No sync addresses found")
            else:
                sync_address_conds = []
                if premises_addresses:
                    sync_address_conds.append(func.lower(func.trim(SubmissionPremises.address)).in_(premises_addresses))
                if premises_ids:
                    sync_address_conds.append(SubmissionPremises.premises_id.in_(premises_ids))

                conflicting_policies = conflicting_policies.outerjoin(SubmissionPremises).filter(
                    or_(*sync_address_conds)
                )

        conflicting_policies = conflicting_policies.all()

        if conflicting_policies:
            submission.clearing_status = ClearingStatus.BLOCKED

        for policy in conflicting_policies:
            submission_clearing_issue = SubmissionClearingIssue()
            submission_clearing_issue.submission_id = submission.id
            submission_clearing_issue.suspected_report_id = policy.report_id
            submission_clearing_issue.reason = IN_FORCE_POLICY_REASON
            submission.clearing_issues.append(submission_clearing_issue)
            db.session.commit()

            different_agency = submission.broker_id is None or submission.broker_id != policy.broker_id
            clearing_sub_status = SubmissionClearingSubStatus()
            clearing_sub_status.status = ClearingSubStatus.CONFLICT.value
            clearing_sub_status.sub_status = "Active policy - " + (
                "Different agency" if different_agency else "Same agency"
            )
            clearing_sub_status.submission_clearing_issue = submission_clearing_issue
            submission.clearing_sub_statuses.append(clearing_sub_status)
            db.session.commit()

    def _generate_similarity_conditions(self, name: str, conditions: list, case_conds: list, field=None) -> None:
        if not name:
            return
        if not field:
            field = ReportV2.email_subject
        subject_ilike = field.ilike(f"%{name}%")
        subject_length = func.length(field) >= self.MIN_SUBJECT_LENGTH
        reverse_ilike = and_(literal(name).contains(field), subject_length)
        # The same as func.similarity, but faster and able to use index.
        subject_sim = field.op("%")(name)
        conditions.extend([subject_ilike, reverse_ilike, subject_sim])
        case_conds.extend(
            [
                (subject_ilike, SUBJECT_ILIKE_REASON),
                (subject_sim, SUBJECT_SIMILAR_REASON),
                (reverse_ilike, SUBJECT_ILIKE_REASON),
            ]
        )

    def _generate_other_names(self, name: str) -> list:
        if not name:
            return []
        other_names = []
        name = name.lower()
        splitters = ["dba", "c/o", "llc", "d/b/a"]
        normalized = _normalize_email(name)
        if normalized != name:
            other_names.append(normalized)
        for splitter in splitters:
            if splitter in name:
                other_names.append(name.split(splitter)[0].strip().rstrip(",.;:"))
        # "inc" splitter, but it should not match "prince" or "increment"
        tokens = re.split(r"\binc\b", name)
        if tokens and len(tokens) > 1:
            other_names.append(tokens[0].strip().rstrip(",.;:"))
        return list({o.lower().strip() for o in other_names if len(o) >= self.MIN_SUBJECT_LENGTH})

    def _get_base_clearing_query(self, conditions: list, case_conds: list, joins: list, base_report: ReportV2):
        organization_id = base_report.organization_id
        date_from = base_report.created_at - timedelta(days=ClearingService.CREATED_AT_RANGE_TO_CONSIDER_DAYS)

        query = db.session.query(ReportV2, case(case_conds)).distinct()
        query = query.options(lazyload(ReportV2.report_bundle), contains_eager(ReportV2.submission))
        query = query.join(ReportV2.submission)
        query = SubmissionDAO.add_organization_id_filter(query, organization_id)
        query = query.filter(ReportV2.is_deleted.isnot(True))
        query = query.filter(ReportV2.is_archived.isnot(True))
        query = query.filter(ReportV2.id != base_report.id)
        query = query.filter(ReportV2.created_at >= date_from)
        query = query.filter(Submission.created_at >= date_from)
        query = query.filter(Submission.is_pre_renewal.isnot(True))
        query = query.filter(Submission.is_renewal_shell.isnot(True))
        query = query.filter(Submission.is_stub.isnot(True))
        for join in joins:
            query = query.join(join)
        query = query.filter(or_(*conditions))

        return query

    def _get_similar_reports_light(self, submission: Submission) -> list[ReportV2]:
        report: ReportV2 = submission.report
        email_subject = report.email_subject
        files = submission.files
        files_data = [f.checksum for f in files if not _is_excluded_file(f) and f.checksum]
        email = [f for f in files if f.file_type == FileType.EMAIL]
        email_names = email[0].processed_file.resolution_requested_names if email and email[0].processed_file else []
        coverages = [(c.coverage_id, c.coverage_type or CoverageType.PRIMARY.value) for c in submission.coverages]
        skip_shadows = or_(
            ReportV2.shadow_type.is_(None),
            ReportV2.shadow_type.notin_(
                [
                    ReportShadowType.IS_ACTIVE_SHADOW,
                    ReportShadowType.CHAINED_SHADOW,
                    ReportShadowType.IS_SHADOW_ORIGIN,
                ]
            ),
        )

        same_file = File.checksum.in_(files_data)
        same_requested_name = func.jsonb_extract_path_text(
            ProcessedFile.business_resolution_data, "resolution_data", "0", "requested_name"
        ).in_(email_names)
        no_report_processing_dependency = ReportProcessingDependency.report_id.is_distinct_from(report.id)
        other_names = self._generate_other_names(email_subject)

        # list of tuples: (conditions [list], case_conds [list of tuples])
        query_groups = []
        file_conditions = [same_file]
        file_case_conditions = [(same_file, SAME_FILE_REASON)]
        if email_names:
            file_conditions.append(same_requested_name)
            file_case_conditions.append((same_requested_name, SAME_ENTITY_REASON))
        query_groups.append((file_conditions, file_case_conditions))
        conditions = []
        case_conds = []
        if email_subject and len(email_subject) >= self.MIN_SUBJECT_LENGTH:
            self._generate_similarity_conditions(email_subject, conditions, case_conds)
            for other_name in other_names:
                self._generate_similarity_conditions(other_name, conditions, case_conds)
        if report.name and len(report.name) >= self.MIN_SUBJECT_LENGTH:
            self._generate_similarity_conditions(report.name, conditions, case_conds, field=ReportV2.name)
        if conditions:
            query_groups.append((conditions, case_conds))

        threshold = self.QUERY_THRESHOLD
        db.session.execute(f"SET pg_trgm.similarity_threshold = {threshold};")

        result = []
        for conditions, case_conds in query_groups:
            query = self._get_base_clearing_query(conditions, case_conds, [], report)
            query = (
                query.options(load_only(ReportV2.id, ReportV2.email_subject, ReportV2.name))
                .join(Submission.files, isouter=True)
                .join(File.processed_file, isouter=True)
                .join(ReportV2.processing_depends_on_report, isouter=True)
                .filter(no_report_processing_dependency)
                .filter(skip_shadows)
            )

            if submission.brokerage_id:
                query = query.filter(
                    or_(Submission.brokerage_id.is_(None), Submission.brokerage_id == submission.brokerage_id)
                )

            if coverages:
                query = query.join(Submission.coverages, isouter=True)
                query = query.filter(
                    or_(
                        SubmissionCoverage.coverage_id.is_(None),
                        tuple_(
                            SubmissionCoverage.coverage_id,
                            func.coalesce(SubmissionCoverage.coverage_type, CoverageType.PRIMARY.value),
                        ).in_(coverages),
                    )
                )
            result.extend(query.all())
        return result

    def _get_similar_reports(
        self, submission: Submission, should_match_sync_address: bool = False
    ) -> list[tuple[ReportV2, str]]:
        report = submission.report
        email_subject = report.email_subject
        other_names = self._generate_other_names(email_subject)
        linked_report_ids = [r.report_2_id for r in submission.report.links]
        business_ids = [business.business_id for business in submission.businesses]

        # list of tuples: (conditions [list], case_conds [list of tuples], joins [list])
        query_groups = []
        shares_entity_condition = [SubmissionBusiness.business_id.in_(business_ids)]
        shares_entity_case_cond = [(SubmissionBusiness.business_id.in_(business_ids), FULL_CLEARING_REASON)]
        query_groups.append((shares_entity_condition, shares_entity_case_cond, []))
        conditions = []
        case_conds = []
        # For Conifer, we don't want to match by email.
        is_conifer = Organization.is_bishop_conifer_for_id(report.organization_id)
        if email_subject and len(email_subject) >= self.MIN_SUBJECT_LENGTH and not is_conifer:
            self._generate_similarity_conditions(email_subject, conditions, case_conds)
            for other_name in other_names:
                self._generate_similarity_conditions(other_name, conditions, case_conds)
        if report.name and len(report.name) >= self.MIN_SUBJECT_LENGTH:
            self._generate_similarity_conditions(report.name, conditions, case_conds, field=ReportV2.name)
        if conditions:
            query_groups.append((conditions, case_conds, []))

        if should_match_sync_address:
            premises_addresses = [premise.address.strip().lower() for premise in submission.premises]
            premises_ids = [premise.premises_id for premise in submission.premises if premise.premises_id]
            if not premises_addresses and not premises_ids:
                log = logger.bind(submission_id=submission.id)
                log.error("No sync addresses found")

            sync_address_conds = []
            if premises_addresses:
                sync_address_conds.append(func.lower(func.trim(SubmissionPremises.address)).in_(premises_addresses))
            if premises_ids:
                sync_address_conds.append(SubmissionPremises.premises_id.in_(premises_ids))

            if sync_address_conds:
                sync_address_cond = or_(*sync_address_conds)
                query_groups.append(
                    ([sync_address_cond], [(sync_address_cond, FULL_CLEARING_REASON)], [SubmissionPremises])
                )

        threshold = self.FULL_CLEARING_QUERY_THRESHOLD
        db.session.execute(f"SET pg_trgm.similarity_threshold = {threshold};")

        result = []
        verification_conds = [
            Submission.is_verified == True,
            Submission.is_verification_required == False,
            Submission.is_verification_required == None,
        ]
        for conditions, case_conds, joins in query_groups:
            query = self._get_base_clearing_query(conditions, case_conds, joins, report)
            query = query.join(SubmissionBusiness)
            query = query.filter(or_(*verification_conds))
            query = query.filter(ReportV2.id.notin_(linked_report_ids))

            result.extend(query.all())
        return result
