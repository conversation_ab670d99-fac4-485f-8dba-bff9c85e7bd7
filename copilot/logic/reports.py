from datetime import datetime, timed<PERSON><PERSON>
from uuid import UUID

from common.clients.facts import Facts<PERSON>lient
from facts_client_v2.model.summary_config import SummaryConfig
from flask import abort, current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy import and_, func, or_
from sqlalchemy.orm import joinedload, load_only, selectinload
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.metric import MetricType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.enums.underwriters import SubmissionUserSource
import pytz
import redis_lock

from copilot.kalepa_domain_events.kalepa_events_handler import KalepaEventsHandler
from copilot.logic.clearing.clearing_service import ClearingService
from copilot.logic.dao.report_dao import ReportDAO
from copilot.logic.dao.submission_client_id_dao import SubmissionClientIdDAO
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.dao.submission_relation_dao import SubmissionRelationDAO
from copilot.logic.org_sharing.org_sharing import share_report_with_org
from copilot.logic.pds.file_handler import FileHandler
from copilot.logic.pds.stuck_submission_service import StuckSubmissionService
from copilot.logic.pds.submission_handler import SubmissionHandler
from copilot.metric_utils import calculate_hidden_summary_config_ids
from copilot.models import (
    Loss,
    MetricV2,
    Organization,
    ReportPermission,
    ReportProcessingDependency,
    ReportV2,
    Submission,
    Subscription,
    User,
    db,
)
from copilot.models.emails import Email, ReportEmailCorrespondence
from copilot.models.files import File, SubmissionFilesData
from copilot.models.reports import (
    ReportShadowDependency,
    SubmissionClientId,
    SubmissionIdentifier,
)
from copilot.models.submission_consolidation_process import (
    SubmissionConsolidationProcess,
)
from copilot.models.submission_history import (
    SubmissionHistory,
    track_submission_history_event,
    track_submission_history_pds_event,
)
from copilot.models.submission_sync import SubmissionIdentifiersSuggestion
from copilot.models.types import (
    CoverageType,
    PermissionType,
    ReportDependencyType,
    ReportShadowType,
    SubmissionActionType,
    SubmissionEvent,
)
from copilot.v3.utils.reports import add_processing_dependency
from copilot.v3.utils.support_users import assign_next_report_for_support_user

logger = get_logger()


def get_create_shadow_lock_name(report_id: UUID) -> str:
    return f"create_shadow_lock_{report_id}"


def delete_permission_and_user_stage(permission: ReportPermission):
    subscriptions = Subscription.query.filter_by(
        report_id=permission.report_id, user_id=permission.grantee_user_id
    ).all()
    for subscription in subscriptions:
        db.session.delete(subscription)
    users_with_open_report = User.query.filter(
        User.open_reports.any(and_(ReportV2.id == permission.report_id, User.id == permission.grantee_user_id))
    ).all()
    for user in users_with_open_report:
        if permission.report in user.open_reports:
            user.open_reports.remove(permission.report)
    db.session.delete(permission)


def _get_summary_configs_as_dict() -> dict[str, SummaryConfig]:
    return current_app.facts_client_v2.get_summary_configs_as_dict()


def __update_metric_v2_quintiles(metrics: list[MetricV2]) -> None:
    summary_configs_by_id: dict[str, SummaryConfig] = _get_summary_configs_as_dict()
    for metric in metrics:
        if metric.metric_type == MetricType.MEAN and metric.summary_config_id:
            summary_config: SummaryConfig = summary_configs_by_id.get(metric.summary_config_id)
            if hasattr(summary_config, "quintiles") and summary_config.quintiles:
                metric.custom_quintiles = summary_config.quintiles


def get_summary_config_ids_to_hide(primary_naics: str | None):
    if not primary_naics or current_user.is_internal_machine_user:
        return []

    try:
        client: FactsClient = current_app.facts_client
        groups = client.get_groups(ids=["OPERATIONS_HOSPITALITY", "OPERATIONS_31_42_44"], with_summary_configs=True)

        def config_ids(group):
            ids = set()
            for fs in group["fact_subtypes"]:
                for config in fs["summary_configs"]:
                    ids.add(config["id"])
            return ids

        hospitality_ids = config_ids(next(g for g in groups if g["id"] == "OPERATIONS_HOSPITALITY"))
        industrial_ids = config_ids(next(g for g in groups if g["id"] == "OPERATIONS_31_42_44"))

        two_digit_naics = int(primary_naics.replace("NAICS_", "")[:2])

        return calculate_hidden_summary_config_ids(two_digit_naics, hospitality_ids, industrial_ids)
    except Exception as e:
        logger.exception("Could not get summary config ids to hide for naics", primary_naics=primary_naics, exception=e)
        return []


def get_metrics_v2_for_report(
    report_id: UUID,
    summary_config_ids: list[str] | None = None,
    submission_business_id: str | None = None,
    submission_business_ids: list[str] | None = None,
    include_location_level_metrics: bool = False,
) -> list[MetricV2]:
    rownb = (
        func.row_number()
        .over(
            partition_by=(MetricV2.parent_id, MetricV2.parent_type, MetricV2.summary_config_id),
            order_by=[MetricV2.created_at.desc()],
        )
        .label("rownb")
    )

    metrics_subquery = db.session.query(MetricV2.id, rownb).filter(MetricV2.report_v2_id == report_id)
    if submission_business_id:
        metrics_subquery = metrics_subquery.filter(MetricV2.submission_business_id == submission_business_id)
    elif submission_business_ids:
        metrics_subquery = metrics_subquery.filter(MetricV2.submission_business_id.in_(submission_business_ids))
    elif not include_location_level_metrics:
        metrics_subquery = metrics_subquery.filter(MetricV2.submission_business_id == None)

    if summary_config_ids:
        metrics_subquery = metrics_subquery.filter(MetricV2.summary_config_id.in_(summary_config_ids))

    metrics_subquery = metrics_subquery.subquery()

    metrics_ids = [r["id"] for r in db.session.query(metrics_subquery.c.id).filter(metrics_subquery.c.rownb == 1).all()]

    metrics = (
        db.session.query(MetricV2)
        .options(
            selectinload(MetricV2.sources),
        )
        .filter(MetricV2.id.in_(metrics_ids))
        .order_by(MetricV2.name)
        .all()
    )
    __update_metric_v2_quintiles(metrics)

    return metrics


def should_expose_permissions(report_id: UUID) -> bool:
    # Only owners can see what are the reports permission
    if current_user.is_internal_machine_user or (
        not current_user.has_report_permission(PermissionType.OWNER, report_id) and current_user.role != "manager"
    ):
        return False
    return True


def handle_delete_report(report: ReportV2, from_sync: bool = False, deletion_reason: str | None = None) -> None:
    if report.submission.is_boss and report.shadow_type not in (
        ReportShadowType.IS_SHADOW_ORIGIN,
        ReportShadowType.CHAINED_SHADOW,
    ):
        logger.error("Can't delete BOSS report", report_id=report.id)
        return
    if not from_sync and report.submission.has_unique_submission_client_id:
        logger.warning("Can't delete report with unique submission_client_id", report_id=report.id)
        return

    report.is_deleted = True
    report.deletion_reason = deletion_reason

    if report.submission:
        SubmissionClientIdDAO.delete_submission_client_ids(report.submission.id, report.organization_id)
        try:
            SubmissionRelationDAO.delete_submission_relations(from_submission_id=report.submission.id)
            SubmissionRelationDAO.delete_submission_relations(to_submission_id=report.submission.id)
        except:
            logger.exception(
                "Failed to delete submission relations", report_id=report.id, submission_id=report.submission.id
            )
        SubmissionHandler.handle_submission_deletion(report.submission)
        current_app.event_service.handle_submission_event(SubmissionEvent.SUBMISSION_UPDATED, report.submission)
        if not report.submission.is_verified:
            for rpd in report.processing_dependencies:
                _remove_processing_dependency(rpd)
            if report.shadow_origin_dependency and report.shadow_origin_dependency.is_active:
                delete_report_shadow_dependency(report.shadow_origin_dependency, report.submission)
                db.session.commit()
                return

        for shadow_dependency in [sd for sd in report.shadow_dependencies if sd.is_active]:
            delete_report_shadow_dependency(shadow_dependency)
        track_submission_history_event(report.submission.id, SubmissionActionType.REPORT_DELETED)

    users_with_open_report = User.query.filter(User.open_reports.any(ReportV2.id == report.id)).all()
    for user in users_with_open_report:
        user.open_reports.remove(report)

    db.session.merge(report)
    organization_id = report.organization_id
    report_correspondence_id = report.correspondence_id
    db.session.commit()

    _check_emails_without_submission_processing(organization_id, report_correspondence_id)


def delete_report_shadow_dependency(shadow_dependency: ReportShadowDependency, shadow_submission: Submission = None):
    logger.info(
        "Deleting shadow report dependency",
        report_id=shadow_dependency.report_id,
        shadow_report_id=shadow_dependency.shadow_report_id,
    )
    shadowed_report = shadow_dependency.report
    for file in [f for f in shadowed_report.submission.files if f.is_required_shadow_processing]:
        file.is_required_shadow_processing = False
        file.processing_state = FileProcessingState.IGNORED
    shadowed_report.shadow_type = None

    shadow_report = shadow_submission.report if shadow_submission else shadow_dependency.shadow_report
    shadow_submission = shadow_submission or shadow_report.submission
    db.session.delete(shadow_submission)
    logger.info("Deleted shadow submission dependency")
    db.session.delete(shadow_report)
    logger.info("Deleted shadow report dependency")


def handle_shadow_report_verification(shadow_submission: Submission) -> Submission | None:
    if shadow_submission.report.shadow_type != ReportShadowType.IS_ACTIVE_SHADOW:
        return
    shadowed_report_id = ReportDAO.get_shadowed_report_id(shadow_submission.report_id)
    shadowed_submission = SubmissionDAO.get_minimal_submission(report_id=shadowed_report_id, include_report=True)
    shadowed_report = shadowed_submission.report
    shadow_report_id = shadow_submission.report_id
    shadow_report = shadow_submission.report

    shadow_submission.report_id = shadowed_report_id
    shadow_submission.report = shadowed_report
    shadowed_report.submission = shadow_submission
    shadowed_report.shadow_type = None
    shadowed_report.name = shadow_report.name
    shadowed_report.is_user_waiting_for_shadow = False

    shadowed_submission.report_id = shadow_report_id
    shadowed_submission.report = shadow_report
    shadow_report.submission = shadowed_submission
    shadow_report.shadow_type = ReportShadowType.IS_SHADOW_ORIGIN

    db.session.query(ReportShadowDependency).filter(
        ReportShadowDependency.report_id == shadowed_report_id,
        ReportShadowDependency.shadow_report_id == shadow_report_id,
    ).update({"is_active": False, "report_id": shadow_report_id, "shadow_report_id": shadowed_report_id})

    _sync_submission_fields_from_shadowed_sub(shadow_submission, shadowed_submission)
    _sync_submission_assigned_underwriters(shadow_submission, shadowed_submission)
    _sync_submission_client_ids_from_shadowed_sub(shadow_submission, shadowed_submission)
    _sync_submission_identifiers_from_shadowed_sub(shadow_submission, shadowed_submission)
    _maybe_sync_nationwide_files(shadow_submission, shadowed_submission)
    _update_submission_identifiers_suggestions(shadow_submission, shadowed_submission)

    handle_delete_report(shadow_report)
    return shadowed_submission


def _sync_submission_fields_from_shadowed_sub(shadow_submission: Submission, shadowed_submission: Submission) -> None:
    # Some fields (not related to processing) could be changed when the shadow was active - we need to sync them
    shadow_submission.client_stage_id = shadowed_submission.client_stage_id
    shadow_submission.client_clearing_status = shadowed_submission.client_clearing_status
    shadow_submission.clearing_status = shadowed_submission.clearing_status
    shadow_submission.cleared_date = shadowed_submission.cleared_date

    shadow_submission.stage = shadowed_submission.stage
    shadow_submission.stage_details = shadowed_submission.stage_details
    shadow_submission.quoted_date = shadowed_submission.quoted_date
    shadow_submission.bound_date = shadowed_submission.bound_date
    shadow_submission.frozen_as_of = shadowed_submission.frozen_as_of
    shadow_submission.synced_at = shadowed_submission.synced_at
    shadow_submission.lost_reasons = shadowed_submission.lost_reasons
    shadow_submission.decline_email_sent = shadowed_submission.decline_email_sent
    shadow_submission.decline_email_recipient_address = shadowed_submission.decline_email_recipient_address
    shadow_submission.send_decline_email = shadowed_submission.send_decline_email
    shadow_submission.active_email_template_id = shadowed_submission.active_email_template_id
    shadow_submission.notes = shadowed_submission.notes

    # We want to sync the submission_notes except those that were created by recommendation rules
    for note in shadow_submission.submission_notes:
        if note.rule_id is None:
            db.session.delete(note)

    for note in shadowed_submission.submission_notes:
        if note.rule_id is None:
            note_copy = note.copy(old_to_new_ids={shadowed_submission.id: shadow_submission.id})
            db.session.add(note_copy)
            shadow_submission.submission_notes.append(note_copy)


def _sync_submission_assigned_underwriters(incoming_submission: Submission, existing_submission: Submission) -> None:
    if not any(
        uw.source in [SubmissionUserSource.MANUAL, SubmissionUserSource.API]
        for uw in existing_submission.assigned_underwriters
    ):
        return
    underwriters_to_be_deleted = [
        uw
        for uw in incoming_submission.assigned_underwriters
        if uw.user_id not in [au.user_id for au in existing_submission.assigned_underwriters]
    ]
    for uw in underwriters_to_be_deleted:
        incoming_submission.assigned_underwriters.remove(uw)
        db.session.delete(uw)
    remaining_user_ids = [au.user_id for au in incoming_submission.assigned_underwriters]
    for assigned_underwriter in existing_submission.assigned_underwriters:
        if assigned_underwriter.user_id in remaining_user_ids:
            continue
        new_assigned_underwriter = assigned_underwriter.copy()
        new_assigned_underwriter.submission_id = incoming_submission.id
        db.session.add(new_assigned_underwriter)
        incoming_submission.assigned_underwriters.append(new_assigned_underwriter)


def _sync_submission_identifiers_from_shadowed_sub(
    incoming_submission: Submission, existing_submission: Submission
) -> None:
    incoming_submission.identifiers.clear()
    for identifier in existing_submission.identifiers:
        new_identifier = SubmissionIdentifier(
            submission_id=incoming_submission.id,
            identifier=identifier.identifier,
            identifier_type=identifier.identifier_type,
        )
        db.session.add(new_identifier)
        incoming_submission.identifiers.append(new_identifier)


def _update_submission_identifiers_suggestions(
    incoming_submission: Submission, existing_submission: Submission
) -> None:
    db.session.query(SubmissionIdentifiersSuggestion).filter(
        SubmissionIdentifiersSuggestion.submission_id == existing_submission.id
    ).update({"submission_id": incoming_submission.id}, synchronize_session=False)


# Incoming submission is created when shadow report is requested and it takes current submission client ids
# as they are in shadowed submission (current original).
# After shadow report is verified, incoming submission, which will become current, is updated to have
# any changes that might have happened to exiting submission client ids while shadow was actively processed.
def _sync_submission_client_ids_from_shadowed_sub(
    incoming_submission: Submission, existing_submission: Submission
) -> None:
    incoming_submission.client_submission_ids.clear()
    for client_id in existing_submission.client_submission_ids:
        new_client_id = SubmissionClientId(
            client_submission_id=client_id.client_submission_id,
            submission_id=incoming_submission.id,
            submission=incoming_submission,
            source=client_id.source,
        )
        db.session.add(new_client_id)
        incoming_submission.client_submission_ids.append(new_client_id)


def _maybe_sync_nationwide_files(incoming_submission: Submission, existing_submission: Submission) -> None:
    if (
        not Organization.is_nationwide_for_id(incoming_submission.organization_id)
        or incoming_submission.origin != Origin.API
    ):
        return
    # for every file uploaded to BOSS, its metadata and external id should be copied to the corresponding file
    # in the shadow report. Also, check for any files that should be uploaded to BOSS from the new submission
    logger.info(
        "Syncing nationwide files between shadow submissions",
        incoming_submission_id=incoming_submission.id,
        existing_submission_id=existing_submission.id,
    )
    existing_files_checksum_to_file: dict[str, File] = {
        esf.checksum: esf for esf in existing_submission.files if esf.checksum
    }
    incoming_files_checksum_to_file: dict[str, File] = {
        isf.checksum: isf for isf in incoming_submission.files if isf.checksum
    }
    incoming_files: list[File] = [isf for isf in incoming_submission.files if isf.checksum]
    # we should not sync files that are internal
    incoming_files = [isf for isf in incoming_files if not isf.is_internal]
    id_to_incoming_file = {isf.id: isf for isf in incoming_files}

    for incoming_file in incoming_files:
        parent_file = id_to_incoming_file.get(incoming_file.parent_file_id)
        should_upload = not parent_file or parent_file.file_type == FileType.RAW_EMAIL

        if incoming_file.checksum not in existing_files_checksum_to_file:
            if not should_upload:
                continue
            KalepaEventsHandler.send_submission_file_added_event(
                incoming_submission, incoming_file, start_file_processing=False
            )

        if incoming_file.checksum in existing_files_checksum_to_file:
            existing_file = existing_files_checksum_to_file[incoming_file.checksum]
            is_file_uploaded = bool(existing_file.boss_doc_id)
            logger.info(
                "Syncing nationwide file",
                incoming_file_id=incoming_file.id,
                existing_file_id=existing_file.id,
            )
            incoming_file.external_identifier = existing_file.external_identifier
            incoming_file.client_file_type = existing_file.client_file_type
            incoming_file.client_file_tags = dict(existing_file.client_file_tags or {})
            incoming_file.created_at = existing_file.created_at
            db.session.add(incoming_file)

            if not is_file_uploaded and should_upload:
                KalepaEventsHandler.send_submission_file_added_event(
                    incoming_submission, incoming_file, start_file_processing=False
                )

    files_to_delete: list[File] = [esf for esf in existing_submission.files if esf.checksum]
    files_to_delete = [esf for esf in files_to_delete if not esf.is_internal]
    files_to_delete = [esf for esf in files_to_delete if esf.checksum not in incoming_files_checksum_to_file]
    files_to_delete = [esf for esf in files_to_delete if esf.boss_doc_id]

    for file in files_to_delete:
        logger.info("Deleting nationwide file", file_id=file.id)
        KalepaEventsHandler.send_submission_file_deleted_event(incoming_submission, file)


def _handle_history_events_for_starting_pds(submission_id: UUID, processing_action_type: SubmissionActionType):
    for submission_history in SubmissionHistory.query.filter(SubmissionHistory.submission_id == submission_id).all():
        if SubmissionActionType.is_pds_type(submission_history.submission_action_type):
            submission_history.invalid = True

    track_submission_history_pds_event(
        submission_id=submission_id,
        submission_action_type=processing_action_type,
    )


def enhance_shell_report(report: ReportV2) -> None:
    submission = report.submission
    log = logger.bind(submission_id=submission.id, report_id=report.id)
    if submission.is_processing_enabled:
        log.warning("Submission is already auto processed or there is a processing dependency on another report.")
        return
    if submission.is_enhanced_shell or submission.processing_state == SubmissionProcessingState.ENHANCING_SHELL:
        log.warning("Submission is already enhanced shell.")
        return
    if not submission.files_for_enhanced_shell_processing():
        log.warning("No files for enhanced shell processing.")
        return
    log.info("Enhancing shell report")
    submission.processing_state = SubmissionProcessingState.ENHANCING_SHELL
    for file in submission.files_for_enhanced_shell_processing():
        FileHandler().handle_file(file, submission)


def start_report_processing(
    report: ReportV2,
    add_processing_dependencies: bool = True,
    check_terminal_stages: bool = True,
) -> None:
    submission = report.submission
    log = logger.bind(submission_id=submission.id, report_id=report.id)
    if submission.is_processing_enabled:
        log.warning("Submission is already auto processed or there is a processing dependency on another report.")
        return
    if submission.processing_state == SubmissionProcessingState.COMPLETED:
        log.warning(
            "Submission is already completed. If you want to restart processing, use restart_report_processing."
        )
        return
    if check_terminal_stages and submission.is_in_terminal_stage:
        logger.info(
            "Moving submission to CANCELLED state because it's in terminal state during start processing",
            submission_id=submission.id,
        )
        submission.is_auto_processed = True
        submission.processing_state = SubmissionProcessingState.CANCELLED
        report.full_pds = True
        _handle_verified_shells_when_starting_processing(report)
        db.session.add(report)
        db.session.commit()
        return
    if add_processing_dependencies and maybe_add_report_processing_dependencies(report):
        _handle_verified_shells_when_starting_processing(report)
        db.session.commit()
        log.info("Added processing dependency for submission.")
        return
    log.info("Starting processing for submission.")
    submission.is_auto_processed = True
    submission.is_processing = True
    submission.is_verified_shell = False
    _handle_verified_shells_when_starting_processing(report)
    submission.processing_state = SubmissionProcessingState.NOT_STARTED
    report.full_pds = True

    _handle_history_events_for_starting_pds(submission.id, SubmissionActionType.PDS_PROCESSING_STARTED)

    StuckSubmissionService().stuck_if_missing_data(submission)
    StuckSubmissionService().stuck_if_sender_matches(submission)

    db.session.add(report)
    db.session.commit()

    for file in submission.files:
        FileHandler().handle_new_file_added(submission, file, starting_processing_submission=True)


def _handle_verified_shells_when_starting_processing(report: ReportV2) -> None:
    if report.organization_id == ExistingOrganizations.Nationwide.value:
        from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType

        report.submission.is_verified_shell = True
        if FeatureFlagsClient.is_feature_enabled(FeatureType.NATIONWIDE_VERIFIED_SHELLS_PDS):
            share_report_with_org(report)


def cancel_report_processing(report: ReportV2) -> None:
    submission = report.submission
    report_correspondence_id = report.correspondence_id
    organization_id = report.organization_id
    log = logger.bind(submission_id=submission.id, report_id=report.id)
    if submission.processing_state == SubmissionProcessingState.COMPLETED:
        log.warning("Can't cancel completed submission")
        abort(400, "Can't cancel completed submission")
    log.info("Cancel processing submission")
    submission.is_auto_processed = True
    if submission.processing_state == SubmissionProcessingState.CANCELLED or submission.is_verified:
        log.warning(
            "Submission is not in a state allowing cancellation.",
            processing_state=submission.processing_state,
            is_auto_processed=submission.is_auto_processed,
            is_verified=submission.is_verified,
        )
        return
    if report.is_document_ingestion:
        submission.is_auto_processed = True
        submission.processing_state = SubmissionProcessingState.CANCELLED
        submission.stage = SubmissionStage.CANCELED
    submission.is_processing = False
    submission.processing_state = SubmissionProcessingState.CANCELLED
    assign_next_report_for_support_user(report_id=submission.report_id)
    track_submission_history_pds_event(submission.id, SubmissionActionType.PDS_PROCESSING_CANCELLED)
    ClearingService.resolve_light_clearing_issues(report)
    db.session.commit()

    if report.submission:
        if not report.submission.is_verified:
            for rpd in report.processing_dependencies:
                cancel_dependent_report = (
                    rpd.dependency_type == ReportDependencyType.SAME_ORG
                    and rpd.dependent_report.submission.is_in_terminal_stage
                )
                _remove_processing_dependency(rpd, cancel_dependent_report=cancel_dependent_report)
            # If this is shadow report to another report, we drop the dependency and delete the current report
            if report.shadow_origin_dependency and report.shadow_origin_dependency.is_active:
                delete_report_shadow_dependency(report.shadow_origin_dependency, report.submission)
                logger.info("Committing deletion of shadow report dependency")
                try:
                    db.session.commit()
                except Exception:
                    logger.exception("Failed to commit deletion of shadow report dependency", exc_info=True)

    _check_emails_without_submission_processing(organization_id, report_correspondence_id)


def can_restart_report_processing(report: ReportV2) -> tuple[bool, str | None]:
    if report.shadow_type == ReportShadowType.HAS_ACTIVE_SHADOW:
        return False, "Cannot restart processing for report with active shadow report"

    min_time_before_restart = datetime.utcnow().replace(tzinfo=pytz.UTC) - timedelta(minutes=5)

    submission = report.submission
    last_processing_started_time = (
        SubmissionHistory.query.filter(
            SubmissionHistory.submission_id == submission.id,
            SubmissionHistory.submission_action_type
            in [SubmissionActionType.PDS_PROCESSING_RESTARTED, SubmissionActionType.PDS_PROCESSING_STARTED],
        )
        .order_by(SubmissionHistory.created_at.desc())
        .first()
    )
    if last_processing_started_time and last_processing_started_time.created_at > min_time_before_restart:
        return False, "Last processing started less than 5 minutes ago."
    return True, None


def _restart_report_processing_before_complete(report: ReportV2, skip_file_cache: bool) -> None:
    submission = report.submission
    logger.info("Restarting processing for submission before complete", submission_id=submission.id)
    if report.processing_depends_on_report:
        rpd = report.processing_depends_on_report
        logger.info(
            "Removing processing dependency", report_id=rpd.report_id, dependency_report_id=rpd.dependent_report_id
        )
        db.session.delete(rpd)

    report.full_pds = True

    db.session.query(Loss).filter_by(submission_id=submission.id).delete()
    db.session.query(SubmissionFilesData).filter(SubmissionFilesData.submission_id == submission.id).delete()
    db.session.query(SubmissionConsolidationProcess).filter(
        SubmissionConsolidationProcess.submission_id == submission.id
    ).delete()

    for file in sorted(submission.files, key=lambda f: f.parent_file_id is not None):
        if file.is_deleted:
            continue
        if file.parent_file_id and file.file_type != FileType.EMAIL:
            KalepaEventsHandler.send_submission_file_deleted_event(submission, file)
            db.session.delete(file)
        else:
            FileHandler.remove_file_extracted_data(file, remove_processed_data=True)
            _retry_file_processing(file)

    submission.is_auto_processed = True
    submission.is_processing = True
    submission.processing_state = SubmissionProcessingState.NOT_STARTED
    submission.is_waiting_for_auto_verify = False
    submission.is_manual_verified = False
    submission.is_auto_verified = False
    submission.is_verified = False
    submission.verified_at = None
    submission.auto_verified_at = None
    submission.manual_verified_at = None
    submission.is_verification_required = True
    submission.is_metrics_set_manually = False

    _handle_history_events_for_starting_pds(submission.id, SubmissionActionType.PDS_PROCESSING_RESTARTED)
    if skip_file_cache:
        track_submission_history_pds_event(
            submission_id=submission.id,
            submission_action_type=SubmissionActionType.PDS_SKIP_FILE_CACHE,
            only_if_not_exists=True,
        )

    db.session.commit()

    for file in submission.files:
        FileHandler().handle_new_file_added(submission, file)


def _restart_report_processing_after_complete_with_shadow_report(report: ReportV2, skip_file_cache: bool) -> None:
    lock_name = get_create_shadow_lock_name(report.id)
    try:
        with redis_lock.Lock(current_app.locks_client, name=lock_name, expire=60):
            if any(sd.is_active for sd in report.shadow_dependencies):
                abort(409, "Cannot restart processing for report with active shadow report")
            logger.info(
                "Restarting processing for report with shadow report",
                report_id=report.id,
                submission_id=report.submission.id,
            )
            shadow_report = report.create_shadow_report(restart_shadow=True)
            shadow_submission = shadow_report.submission

            for file in (f for f in shadow_submission.files if not f.is_deleted):
                FileHandler.remove_file_extracted_data(file, remove_processed_data=True)
                _retry_file_processing(file)

            _maybe_invalidate_processing_dependency(report)

            if report.shadow_type == ReportShadowType.CHAINED_SHADOW:
                handle_delete_report(report)

            if skip_file_cache:
                track_submission_history_pds_event(
                    submission_id=shadow_submission.id,
                    submission_action_type=SubmissionActionType.PDS_SKIP_FILE_CACHE,
                    only_if_not_exists=True,
                )

            db.session.commit()
            for file in shadow_submission.files:
                FileHandler().handle_new_file_added(shadow_submission, file)
    except redis_lock.NotAcquired:
        logger.warning("Lock already expired", lock=lock_name)


def restart_report_processing(report: ReportV2, skip_file_cache: bool) -> None:
    if report.submission.processing_state == SubmissionProcessingState.COMPLETED or report.submission.businesses:
        _restart_report_processing_after_complete_with_shadow_report(report, skip_file_cache)
    else:
        _restart_report_processing_before_complete(report, skip_file_cache)


def retry_report_processing(report: ReportV2) -> None:
    any_file_for_processing = False
    submission = report.submission
    for file in submission.files:
        if file.processing_state in [
            FileProcessingState.PROCESSING_FAILED,
            FileProcessingState.PROCESSING,
            FileProcessingState.AUTOCONFIRMING,
            FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            FileProcessingState.NOT_CLASSIFIED,
            FileProcessingState.CLASSIFIED,
        ]:
            any_file_for_processing = True
            _retry_file_processing(file)

    if any_file_for_processing:
        db.session.commit()
        for file in submission.files:
            FileHandler().handle_new_file_added(submission, file)
    else:
        KalepaEventsHandler.send_submission_processing_finished(report.submission.id)  # type: ignore[arg-type]


def _retry_file_processing(file: File) -> None:
    if file.file_type in [FileType.EMAIL, FileType.RAW_EMAIL]:
        file.classification = (
            ClassificationDocumentType.RAW_EMAIL.value
            if file.file_type == FileType.RAW_EMAIL
            else ClassificationDocumentType.EMAIL.value
        )
        file.processing_state = FileProcessingState.CLASSIFIED
    elif file.file_type == FileType.BETTERVIEW_REPORT:
        file.classification = ClassificationDocumentType.BETTERVIEW_REPORT_PDF.value
        file.processing_state = FileProcessingState.CLASSIFIED
    else:
        file.processing_state = FileProcessingState.NOT_CLASSIFIED
        file.classification = None
        file.file_type = FileType.UNKNOWN
    file.execution_arn = None
    file.initial_processing_state = None


def remove_cross_org_processing_dependencies(report: ReportV2, force: bool = False) -> None:
    lock_name = f"remove_cross_org_processing_dependencies_{report.id}"
    with redis_lock.Lock(current_app.locks_client, name=lock_name, expire=10):
        processing_dependencies = ReportDAO.get_processing_dependencies(report.id)
        for rpd in processing_dependencies:
            # If the parent report is with ORIGIN API, we want to treat the dependency as cross org
            if rpd.dependency_type == ReportDependencyType.SAME_ORG and report.submission.origin == Origin.EMAIL:
                continue
            _remove_processing_dependency(rpd, force)


def _remove_processing_dependency(
    report_processing_dependency: ReportProcessingDependency, force: bool = False, cancel_dependent_report: bool = False
) -> None:
    dependent_report = report_processing_dependency.dependent_report
    if dependent_report.submission.is_verified:
        if not force:
            logger.info(
                "The report is already completed, won't remove processing dependency",
                report_id=dependent_report.id,
                parent_report_id=report_processing_dependency.report_id,
            )
            return
        else:
            logger.info(
                "Forceful removal of processing dependency",
                report_id=dependent_report.id,
                parent_report_id=report_processing_dependency.report_id,
            )
    logger.info(
        "Removing processing dependency",
        report_id=report_processing_dependency.report_id,
        dependency_report_id=report_processing_dependency.dependent_report_id,
    )
    db.session.delete(report_processing_dependency)
    if cancel_dependent_report:
        dependent_report.submission.is_processing = False
        dependent_report.submission.is_auto_processed = True
        dependent_report.submission.processing_state = SubmissionProcessingState.CANCELLED
    db.session.commit()
    if not cancel_dependent_report:
        KalepaEventsHandler.send_start_report_processing_request(
            dependent_report.submission.id, dependent_report.id, dependent_report.organization_id
        )


def _check_if_numbers_match(candidate: str, model: str) -> bool:
    if not candidate:
        return False
    model_numbers = [int(s) for s in [*model] if s.isdigit()]
    logger.info("Model numbers", model_numbers=model_numbers)
    candidate_numbers = [int(s) for s in [*candidate] if s.isdigit()]
    logger.info("Candidate numbers", candidate_numbers=candidate_numbers)
    return model_numbers == candidate_numbers


def _get_best_candidate_for_boss_matching(
    candidates: list[tuple[ReportV2, float, float]], report: ReportV2
) -> ReportV2 | None:
    logger.info("Found candidates for boss matching", report_id=report.id, candidates=len(candidates))
    candidates = [c for c in candidates if _check_if_numbers_match(c[0].email_subject, report.email_subject)]
    if not candidates:
        return None
    if len(candidates) == 1 and candidates[0][0].submission.origin == Origin.EMAIL:
        return candidates[0][0]
    temp_candidates = []
    # Prioritize candidates coming from email.
    list_candidates = [
        c for c in candidates if (c[0].submission.origin == Origin.EMAIL and (c[1] == 1.0 or c[2] == 1.0))
    ]
    if len(list_candidates) == 1:
        return list_candidates[0][0]
    if list_candidates:
        temp_candidates = list_candidates
        logger.info("Found perfect email candidates for boss matching", report_id=report.id, candidates=len(candidates))
    if not temp_candidates and len(report.email_subject) >= 50:
        list_candidates = [c for c in candidates if c[0].submission.origin == Origin.EMAIL]
        if len(list_candidates) == 1:
            return list_candidates[0][0]
        if list_candidates:
            temp_candidates = list_candidates
            logger.info(
                "Found similar email candidates for boss matching", report_id=report.id, candidates=len(candidates)
            )
    if not temp_candidates:
        # If the parent report is with ORIGIN API, we want to treat to proceed only if the files match
        list_candidates = [
            c
            for c in candidates
            if (c[1] == 1.0 or c[2] == 1.0)
            and _files_applicable_for_processing_dependency(c[0].submission, report.submission, is_boss_matching=True)
        ]
        if len(list_candidates) == 1:
            return list_candidates[0][0]
        if list_candidates:
            temp_candidates = list_candidates
            logger.info("Found api candidates for boss matching", report_id=report.id, candidates=len(candidates))
    if not temp_candidates:
        return None
    # Prioritize candidates with matching coverages.
    coverages = {(c.coverage_id, c.coverage_type or CoverageType.PRIMARY.value) for c in report.submission.coverages}
    if coverages:
        exact_matches = []
        partial_matches = []
        for r in temp_candidates:
            covs = {(c.coverage_id, c.coverage_type or CoverageType.PRIMARY.value) for c in r[0].submission.coverages}
            if coverages == covs:
                exact_matches.append(r)
            elif coverages.intersection(covs):
                partial_matches.append(r)
        if exact_matches:
            temp_candidates = exact_matches
            logger.info("Found candidates with same covs", report_id=report.id, candidates=len(temp_candidates))
        elif partial_matches:
            temp_candidates = partial_matches
            logger.info("Found candidates with similar covs", report_id=report.id, candidates=len(temp_candidates))
    if len(temp_candidates) == 1:
        return temp_candidates[0][0]

    # Prioritize the latest candidate.
    return max(temp_candidates, key=lambda c: c[0].created_at)[0]


def _find_parent_report_for_processing_dependency(report: ReportV2) -> (ReportV2 | None, ReportDependencyType | None):
    matched_report = None
    processing_dependency_type = None
    submission = report.submission
    report_created_at = report.created_at if report.created_at else datetime.utcnow()
    same_org_matched_report_cutoff_time = report_created_at - timedelta(days=7)
    cross_org_matched_report_cutoff_time = report_created_at - timedelta(days=7)
    is_boss_matching = submission.origin == Origin.API and Organization.is_nationwide_for_id(report.organization_id)
    min_str_length = 5

    matching_conditions = []
    if report.email_subject and len(report.email_subject) > min_str_length:
        matching_conditions.append(ReportV2.email_subject.ilike(report.sql_escaped_email_subject))
        if is_boss_matching:
            matching_conditions.append(ReportV2.email_subject.op("%")(report.email_subject))
    if is_boss_matching and report.name and len(report.name) > min_str_length:
        matching_conditions.append(ReportV2.name.ilike(report.sql_escaped_report_name))
        matching_conditions.append(ReportV2.name.op("%")(report.name))
    if not matching_conditions:
        return None, None

    skip_shadows = or_(
        ReportV2.shadow_type.is_(None),
        ReportV2.shadow_type.notin_(
            [
                ReportShadowType.IS_ACTIVE_SHADOW,
                ReportShadowType.CHAINED_SHADOW,
                ReportShadowType.IS_SHADOW_ORIGIN,
            ]
        ),
    )
    query_for = [ReportV2]
    if is_boss_matching:
        query_for.append(func.similarity(ReportV2.email_subject, report.email_subject))
        query_for.append(func.similarity(ReportV2.name, report.name))
    query = (
        db.session.query(*query_for)
        .join(ReportV2.submission)
        .filter(ReportV2.id != report.id)
        .filter(ReportV2.is_deleted.isnot(True))
        .filter(Submission.is_auto_processed.is_(True))
        .filter(Submission.is_pre_renewal.isnot(True))
        .filter(Submission.is_stub.isnot(True))
        .filter(Submission.processing_state != SubmissionProcessingState.CANCELLED)
        .filter(skip_shadows)
    )
    logger.info("is_boss_matching", is_boss_matching=is_boss_matching)
    if is_boss_matching:
        # similarity of 1 is helpful when dealing with characters like "." or ",", and other special chars.
        # similarity('c&c fooo 123, ', 'c & c fooo 123.!#') == 1
        # we want to allow for a bit more flexibility in the matching, so we set the threshold to 0.95
        # (as long as the subject is over 50 characters)
        db.session.execute("SET pg_trgm.similarity_threshold = 0.95;")
        processing_dependency_type = ReportDependencyType.SAME_ORG
        boss_query = query.filter(ReportV2.created_at > same_org_matched_report_cutoff_time)
        boss_query = boss_query.filter(ReportV2.organization_id == report.organization_id)
        boss_query = boss_query.filter(or_(*matching_conditions))
        boss_query = boss_query.options(joinedload(ReportV2.submission).joinedload(Submission.priority))
        candidates = boss_query.all()
        matched_report = _get_best_candidate_for_boss_matching(candidates, report)

    if not matched_report:
        processing_dependency_type = ReportDependencyType.CROSS_ORG
        query = query.filter(or_(*matching_conditions))
        query = query.filter(ReportV2.organization_id.in_(Organization.active_client_organizations()))
        query = query.filter(ReportV2.created_at > cross_org_matched_report_cutoff_time)
        query = query.filter(ReportV2.organization_id != report.organization_id)
        query = query.order_by(ReportV2.created_at)
        query = query.options(
            joinedload(ReportV2.submission).options(joinedload(Submission.priority), selectinload(Submission.files))
        )
        candidates = query.all()

        logger.info(f"Found {len(candidates)} cross org matched report candidates", report_id=report.id)
        if is_boss_matching:
            matched_report = next(
                (
                    c[0]
                    for c in candidates
                    if _files_applicable_for_processing_dependency(c[0].submission, submission, is_boss_matching=True)
                ),
                None,
            )
        else:
            matched_report = next(
                (c for c in candidates if _files_applicable_for_processing_dependency(c.submission, submission)),
                None,
            )
    return matched_report, processing_dependency_type


def maybe_add_report_processing_dependencies(report: ReportV2) -> bool:
    if Organization.is_nationwide_ml_for_id(report.organization_id):
        logger.info("Skipping creation of processing dependency for NW ML", report_id=report.id)
        return False
    if report.is_document_ingestion:
        logger.info("Skipping creation of processing dependency for document ingestion", report_id=report.id)
        return False
    if report.submission.is_pre_renewal:
        logger.info("Skipping creation of processing dependency for pre-renewal", report_id=report.id)
        return False
    if not report.email_subject or report.shadow_type is not None:
        return False
    if report.organization_id not in Organization.active_client_organizations():
        return False
    if report.submission.is_stub:
        logger.info(
            "Skipping creation of processing dependency for stub sub",
            report_id=report.id,
            submission_id=report.submission.id,
        )
        return False

    matched_report, processing_dependency_type = _find_parent_report_for_processing_dependency(report)
    if matched_report:
        logger.info(
            "Found matched report for processing dependency",
            parent_report_id=matched_report.id,
            dependent_report_id=report.id,
            dependency_type=processing_dependency_type,
        )
        return add_processing_dependency(
            parent_report=matched_report,
            dependent_report=report,
            dependency_type=processing_dependency_type,
            abort_request_if_error=False,
        )
    return False


def _get_file_checksums_for_comparison(submission: Submission) -> set[str]:
    parent_raw_email_ids = (
        [f.id for f in submission.files if f.file_type == FileType.RAW_EMAIL and f.parent_file_id is None]
        if submission.origin == Origin.API
        else []
    )
    return {
        file.checksum
        for file in submission.files
        if (file.parent_file_id is None or file.parent_file_id in parent_raw_email_ids)
        and file.id not in parent_raw_email_ids
        and not file.is_internal
        and file.file_type != FileType.EMAIL
    }


def _files_applicable_for_processing_dependency(
    parent_submission: Submission,
    dependent_submission: Submission,
    is_boss_matching: bool = False,
) -> bool:
    if parent_submission.is_verified and any(
        f.processing_state in FileProcessingState.not_started_processing_states() for f in parent_submission.files
    ):
        return False
    parent_files_checksums = _get_file_checksums_for_comparison(parent_submission)
    dependent_files_checksums = _get_file_checksums_for_comparison(dependent_submission)
    return (
        (parent_files_checksums or is_boss_matching)
        and parent_files_checksums == dependent_files_checksums
        and None not in parent_files_checksums
    )


def _maybe_invalidate_processing_dependency(report: ReportV2) -> None:
    if report.submission.is_verified and report.processing_dependencies:
        for dependency in report.processing_dependencies:
            if dependency.dependent_report.submission.is_processing_completed:
                logger.info(
                    "Invalidating processing dependency",
                    report_id=dependency.report_id,
                    dependency_report_id=dependency.dependent_report_id,
                )
                dependency.is_invalidated = True


def create_shadow_submission_for_revert(report: ReportV2) -> Submission:
    logger.info("Creating shadow for reverting submission", report_id=report.id, submission_id=report.submission.id)
    shadow_report = report.create_shadow_report(revert_shadow=True)
    shadow_submission = shadow_report.submission
    shadow_submission.is_processing = True
    _maybe_invalidate_processing_dependency(report)
    if report.shadow_type == ReportShadowType.CHAINED_SHADOW:
        handle_delete_report(report)
    track_submission_history_pds_event(report.submission.id, SubmissionActionType.PDS_REVERTED_TO_DO)
    db.session.commit()
    return shadow_submission


def _check_emails_without_submission_processing(organization_id: int, report_correspondence_id: UUID | None) -> None:
    if not report_correspondence_id:
        return
    report_correspondence = (
        ReportEmailCorrespondence.query.filter(ReportEmailCorrespondence.id == report_correspondence_id)
        .options(joinedload(ReportEmailCorrespondence.emails))
        .one()
    )
    email_message_ids = {e.message_id for e in report_correspondence.emails if e.message_id and not e.is_embedded}
    email_account = report_correspondence.email_account
    emails = (
        Email.query.join(ReportEmailCorrespondence, Email.correspondence)
        .join(ReportV2, ReportEmailCorrespondence.reports)
        .join(Submission, ReportV2.submission)
        .filter(Submission.is_auto_processed.is_(True))
        .filter(
            or_(
                Submission.is_verified.is_(True),
                and_(
                    Submission.is_deleted.isnot(True),
                    Submission.processing_state != SubmissionProcessingState.CANCELLED,
                ),
            )
        )
        .filter(Email.message_id.in_(email_message_ids))
        .filter(Email.email_account == email_account)
        .options(load_only(Email.message_id))
        .all()
    )

    email_message_ids_without_submission_processing = email_message_ids - {e.message_id for e in emails}
    if not email_message_ids_without_submission_processing:
        return

    KalepaEventsHandler.send_email_without_verified_submission_event(
        organization_id, list(email_message_ids_without_submission_processing), email_account
    )
